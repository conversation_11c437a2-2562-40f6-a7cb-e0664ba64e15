#!/usr/bin/env python3
"""
Simple test script to verify data and run basic analysis - ASCII compatible
"""

import pandas as pd
import numpy as np
import os
import sys

print("=== Testing Data and Basic Analysis ===")

# Check data files
try:
    print("1. Checking data files...")
    
    # Load datasets
    data1 = pd.read_csv('data_diag.csv')
    data2 = pd.read_csv('data_diag_maj_sub.csv')
    
    print(f"[OK] data_diag.csv loaded: {data1.shape}")
    print(f"[OK] data_diag_maj_sub.csv loaded: {data2.shape}")
    
    print(f"\nDataset 1 columns: {list(data1.columns)}")
    print(f"Dataset 1 target distribution:")
    print(data1.iloc[:, -1].value_counts())
    
    print(f"\nDataset 2 columns: {list(data2.columns)}")
    print(f"Dataset 2 target distribution:")
    print(data2.iloc[:, -1].value_counts())
    
    # Check for missing values
    print(f"\nMissing values in Dataset 1: {data1.isnull().sum().sum()}")
    print(f"Missing values in Dataset 2: {data2.isnull().sum().sum()}")
    
    # Basic ML test
    print("\n2. Running basic ML test...")
    
    from sklearn.model_selection import train_test_split
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.metrics import accuracy_score, f1_score
    from sklearn.preprocessing import LabelEncoder
    
    # Prepare data1
    X1 = data1.iloc[:, :-1]
    y1 = data1.iloc[:, -1]
    
    # Encode labels if they are strings
    if y1.dtype == 'object':
        le = LabelEncoder()
        y1_encoded = le.fit_transform(y1)
        print(f"Encoded labels: {list(le.classes_)}")
    else:
        y1_encoded = y1
    
    # Split and train
    X1_train, X1_test, y1_train, y1_test = train_test_split(X1, y1_encoded, test_size=0.2, random_state=42)
    
    rf = RandomForestClassifier(n_estimators=50, random_state=42)
    rf.fit(X1_train, y1_train)
    
    y1_pred = rf.predict(X1_test)
    acc1 = accuracy_score(y1_test, y1_pred)
    f1_1 = f1_score(y1_test, y1_pred, average='weighted')
    
    print(f"Dataset 1 - Accuracy: {acc1:.4f}, F1-Score: {f1_1:.4f}")
    
    # Prepare data2
    X2 = data2.iloc[:, :-1]
    y2 = data2.iloc[:, -1]
    
    # Encode labels if they are strings
    if y2.dtype == 'object':
        le2 = LabelEncoder()
        y2_encoded = le2.fit_transform(y2)
        print(f"Dataset 2 encoded labels: {list(le2.classes_)}")
    else:
        y2_encoded = y2
    
    # Split and train
    X2_train, X2_test, y2_train, y2_test = train_test_split(X2, y2_encoded, test_size=0.2, random_state=42)
    
    rf2 = RandomForestClassifier(n_estimators=50, random_state=42)
    rf2.fit(X2_train, y2_train)
    
    y2_pred = rf2.predict(X2_test)
    acc2 = accuracy_score(y2_test, y2_pred)
    f1_2 = f1_score(y2_test, y2_pred, average='weighted')
    
    print(f"Dataset 2 - Accuracy: {acc2:.4f}, F1-Score: {f1_2:.4f}")
    
    # Check external files
    print("\n3. Checking external result files...")
    
    external_files = ['enhanced_model_metrics.csv', 'advanced_ml_final_results.csv']
    for file in external_files:
        if os.path.exists(file):
            df = pd.read_csv(file)
            print(f"[OK] {file}: {df.shape}")
            if 'Accuracy' in df.columns:
                best_acc = df['Accuracy'].max()
                print(f"  Best accuracy: {best_acc:.4f}")
        else:
            print(f"[X] {file}: Not found")
    
    print("\n=== Basic Analysis Complete ===")
    print("[OK] Data files are accessible")
    print("[OK] Basic ML models can be trained")
    print("[OK] Ready for full analysis execution")
    
except Exception as e:
    print(f"Error: {str(e)}")
    import traceback
    traceback.print_exc()
