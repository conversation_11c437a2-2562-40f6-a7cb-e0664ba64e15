#!/usr/bin/env python3
"""
Complete Results Report Generator
Creates a comprehensive technical report with execution results, visualizations, and analysis
"""

import os
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from datetime import datetime
import base64
import markdown

def create_results_visualizations():
    """Create additional visualizations from the results data"""
    
    results_dir = Path("enhanced_results")
    output_dir = Path("technical_report_output")
    
    if not results_dir.exists():
        print("❌ Enhanced results directory not found")
        return False
    
    # Set style for consistent plots
    plt.style.use('seaborn-v0_8')
    sns.set_palette("husl")
    
    try:
        # Load model metrics
        if (results_dir / "enhanced_model_metrics.csv").exists():
            metrics_df = pd.read_csv(results_dir / "enhanced_model_metrics.csv", index_col=0)
            
            # Create performance comparison chart
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
            
            # Top 10 models by accuracy
            top_models = metrics_df.head(10)
            
            # Accuracy comparison
            ax1.barh(range(len(top_models)), top_models['Accuracy'], color='skyblue', alpha=0.8)
            ax1.set_yticks(range(len(top_models)))
            ax1.set_yticklabels(top_models.index, fontsize=10)
            ax1.set_xlabel('Accuracy')
            ax1.set_title('Model Accuracy Comparison (Top 10)')
            ax1.grid(True, alpha=0.3)
            
            # Add accuracy values on bars
            for i, v in enumerate(top_models['Accuracy']):
                ax1.text(v + 0.005, i, f'{v:.3f}', va='center', fontsize=9)
            
            # F1-Score vs Accuracy scatter
            ax2.scatter(top_models['Accuracy'], top_models['F1'], 
                       s=100, alpha=0.7, c=top_models['ROC AUC'], cmap='viridis')
            ax2.set_xlabel('Accuracy')
            ax2.set_ylabel('F1-Score')
            ax2.set_title('Accuracy vs F1-Score')
            ax2.grid(True, alpha=0.3)
            
            # Add colorbar
            cbar = plt.colorbar(ax2.collections[0], ax=ax2)
            cbar.set_label('ROC AUC')
            
            # Training time comparison
            ax3.bar(range(len(top_models)), top_models['Training_Time'], 
                   color='lightcoral', alpha=0.8)
            ax3.set_xticks(range(len(top_models)))
            ax3.set_xticklabels(top_models.index, rotation=45, ha='right')
            ax3.set_ylabel('Training Time (seconds)')
            ax3.set_title('Training Time Comparison')
            ax3.grid(True, alpha=0.3)
            
            # Performance metrics radar chart (for top 3 models)
            metrics_cols = ['Accuracy', 'Precision', 'Recall', 'F1', 'ROC AUC']
            top_3 = top_models.head(3)
            
            angles = np.linspace(0, 2 * np.pi, len(metrics_cols), endpoint=False).tolist()
            angles += angles[:1]  # Complete the circle
            
            ax4 = plt.subplot(2, 2, 4, projection='polar')
            
            colors = ['red', 'blue', 'green']
            for i, (model_name, row) in enumerate(top_3.iterrows()):
                values = [row[col] for col in metrics_cols]
                values += values[:1]  # Complete the circle
                
                ax4.plot(angles, values, 'o-', linewidth=2, 
                        label=model_name, color=colors[i], alpha=0.8)
                ax4.fill(angles, values, alpha=0.1, color=colors[i])
            
            ax4.set_xticks(angles[:-1])
            ax4.set_xticklabels(metrics_cols)
            ax4.set_ylim(0, 1)
            ax4.set_title('Performance Metrics Comparison (Top 3 Models)')
            ax4.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
            
            plt.tight_layout()
            plt.savefig(output_dir / "comprehensive_results_analysis.png", 
                       dpi=300, bbox_inches='tight')
            plt.close()
            
            print("✅ Created comprehensive results analysis visualization")
        
        # Create feature importance visualization if available
        if (results_dir / "feature_importance_LightGBM.csv").exists():
            feat_imp = pd.read_csv(results_dir / "feature_importance_LightGBM.csv")
            
            plt.figure(figsize=(12, 8))
            top_features = feat_imp.head(15)
            
            # Create horizontal bar chart
            bars = plt.barh(range(len(top_features)), top_features['importance'], 
                           color='lightgreen', alpha=0.8)
            plt.yticks(range(len(top_features)), top_features['feature'])
            plt.xlabel('Feature Importance Score')
            plt.title('Top 15 Feature Importance - Best Model (LightGBM)')
            plt.grid(True, alpha=0.3)
            
            # Add importance values on bars
            for i, v in enumerate(top_features['importance']):
                plt.text(v + 0.001, i, f'{v:.3f}', va='center', fontsize=9)
            
            plt.gca().invert_yaxis()
            plt.tight_layout()
            plt.savefig(output_dir / "detailed_feature_importance.png", 
                       dpi=300, bbox_inches='tight')
            plt.close()
            
            print("✅ Created detailed feature importance visualization")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating visualizations: {e}")
        return False

def create_executive_dashboard():
    """Create an executive dashboard with key metrics"""
    
    results_dir = Path("enhanced_results")
    output_dir = Path("technical_report_output")
    
    if not (results_dir / "experiment_summary.json").exists():
        return False
    
    try:
        # Load experiment summary
        with open(results_dir / "experiment_summary.json", 'r') as f:
            exp_summary = json.load(f)
        
        # Create dashboard
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 10))
        fig.suptitle('Machine Learning Analysis - Executive Dashboard', fontsize=16, fontweight='bold')
        
        # Key metrics display
        best_model = exp_summary.get('best_model', {})
        
        # Accuracy gauge
        accuracy = best_model.get('accuracy', 0)
        ax1.pie([accuracy, 1-accuracy], labels=['Achieved', 'Remaining'], 
               colors=['#2ecc71', '#ecf0f1'], startangle=90,
               wedgeprops=dict(width=0.3))
        ax1.set_title(f'Best Model Accuracy\n{accuracy:.1%}', fontweight='bold')
        
        # Performance metrics bar chart
        metrics = ['Accuracy', 'F1-Score', 'ROC AUC']
        values = [best_model.get('accuracy', 0), 
                 best_model.get('f1_score', 0), 
                 best_model.get('roc_auc', 0)]
        
        bars = ax2.bar(metrics, values, color=['#3498db', '#e74c3c', '#f39c12'], alpha=0.8)
        ax2.set_ylim(0, 1)
        ax2.set_ylabel('Score')
        ax2.set_title('Key Performance Metrics')
        ax2.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, value in zip(bars, values):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # Dataset information
        dataset_info = exp_summary.get('experiment_info', {})
        info_text = f"""Dataset Overview:
        
• Total Samples: {dataset_info.get('dataset_shape', [0, 0])[0]:,}
• Features: {dataset_info.get('dataset_shape', [0, 0])[1]}
• Classes: {dataset_info.get('n_classes', 0)}
• Test Split: {dataset_info.get('test_size', 0)*100:.0f}%

Best Model: {best_model.get('name', 'N/A')}
Training Time: {best_model.get('training_time', 0):.2f}s"""
        
        ax3.text(0.1, 0.9, info_text, transform=ax3.transAxes, fontsize=12,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        ax3.set_xlim(0, 1)
        ax3.set_ylim(0, 1)
        ax3.axis('off')
        ax3.set_title('Dataset & Model Information')
        
        # Top 5 models comparison
        if (results_dir / "enhanced_model_metrics.csv").exists():
            metrics_df = pd.read_csv(results_dir / "enhanced_model_metrics.csv", index_col=0)
            top_5 = metrics_df.head(5)
            
            x_pos = np.arange(len(top_5))
            ax4.bar(x_pos, top_5['Accuracy'], alpha=0.8, color='steelblue')
            ax4.set_xticks(x_pos)
            ax4.set_xticklabels(top_5.index, rotation=45, ha='right')
            ax4.set_ylabel('Accuracy')
            ax4.set_title('Top 5 Models Comparison')
            ax4.grid(True, alpha=0.3)
            
            # Add accuracy values on bars
            for i, v in enumerate(top_5['Accuracy']):
                ax4.text(i, v + 0.005, f'{v:.3f}', ha='center', va='bottom', fontsize=9)
        
        plt.tight_layout()
        plt.savefig(output_dir / "executive_dashboard.png", 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ Created executive dashboard")
        return True
        
    except Exception as e:
        print(f"❌ Error creating executive dashboard: {e}")
        return False

def create_complete_html_report():
    """Create a complete HTML report with embedded visualizations"""
    
    output_dir = Path("technical_report_output")
    
    # Check if enhanced markdown exists
    enhanced_md_path = output_dir / "Technical_Report_With_Results.md"
    if not enhanced_md_path.exists():
        print("❌ Enhanced markdown report not found")
        return False
    
    # Read the enhanced markdown
    with open(enhanced_md_path, 'r', encoding='utf-8') as f:
        md_content = f.read()
    
    # Convert to HTML
    md = markdown.Markdown(extensions=[
        'markdown.extensions.toc',
        'markdown.extensions.tables',
        'markdown.extensions.codehilite',
        'markdown.extensions.fenced_code'
    ])
    
    html_content = md.convert(md_content)
    
    # Embed visualizations
    visualization_html = ""
    
    # Add executive dashboard
    dashboard_path = output_dir / "executive_dashboard.png"
    if dashboard_path.exists():
        with open(dashboard_path, 'rb') as img_file:
            img_data = base64.b64encode(img_file.read()).decode('utf-8')
            visualization_html += f"""
            <div class="visualization-section">
                <h2>📊 Executive Dashboard</h2>
                <img src="data:image/png;base64,{img_data}" alt="Executive Dashboard" style="width: 100%; max-width: 1200px; height: auto;">
            </div>
            """
    
    # Add comprehensive analysis
    analysis_path = output_dir / "comprehensive_results_analysis.png"
    if analysis_path.exists():
        with open(analysis_path, 'rb') as img_file:
            img_data = base64.b64encode(img_file.read()).decode('utf-8')
            visualization_html += f"""
            <div class="visualization-section">
                <h2>📈 Comprehensive Results Analysis</h2>
                <img src="data:image/png;base64,{img_data}" alt="Comprehensive Analysis" style="width: 100%; max-width: 1200px; height: auto;">
            </div>
            """
    
    # Add feature importance
    feature_path = output_dir / "detailed_feature_importance.png"
    if feature_path.exists():
        with open(feature_path, 'rb') as img_file:
            img_data = base64.b64encode(img_file.read()).decode('utf-8')
            visualization_html += f"""
            <div class="visualization-section">
                <h2>🔍 Detailed Feature Importance</h2>
                <img src="data:image/png;base64,{img_data}" alt="Feature Importance" style="width: 100%; max-width: 1000px; height: auto;">
            </div>
            """
    
    # Insert visualizations into HTML
    if visualization_html:
        # Find insertion point (after results section)
        insertion_point = html_content.find('<h2>📊 Execution Results and Findings</h2>')
        if insertion_point != -1:
            # Find the end of the results section
            next_section = html_content.find('<h2>', insertion_point + 50)
            if next_section != -1:
                html_content = (
                    html_content[:next_section] + 
                    visualization_html + 
                    html_content[next_section:]
                )
            else:
                html_content += visualization_html
        else:
            html_content += visualization_html
    
    # Create complete HTML with enhanced styling
    complete_html = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Complete Machine Learning Analysis Report with Results</title>
        <style>
            body {{
                font-family: 'Georgia', 'Times New Roman', serif;
                line-height: 1.6;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
                color: #333;
                background-color: #fff;
            }}
            
            .visualization-section {{
                margin: 40px 0;
                padding: 20px;
                background-color: #f8f9fa;
                border-radius: 10px;
                border: 1px solid #e9ecef;
                text-align: center;
            }}
            
            .visualization-section h2 {{
                color: #2c3e50;
                margin-bottom: 20px;
            }}
            
            .visualization-section img {{
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                border-radius: 5px;
            }}
            
            h1 {{
                color: #2c3e50;
                border-bottom: 3px solid #3498db;
                padding-bottom: 10px;
                font-size: 2.5em;
                text-align: center;
            }}
            
            h2 {{
                color: #34495e;
                border-bottom: 2px solid #ecf0f1;
                padding-bottom: 8px;
                margin-top: 40px;
                font-size: 1.8em;
            }}
            
            table {{
                border-collapse: collapse;
                width: 100%;
                margin: 20px 0;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }}
            
            th, td {{
                border: 1px solid #ddd;
                padding: 12px;
                text-align: left;
            }}
            
            th {{
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }}
            
            tr:nth-child(even) {{
                background-color: #f8f9fa;
            }}
            
            .results-highlight {{
                background-color: #e8f5e8;
                border-left: 4px solid #28a745;
                padding: 15px;
                margin: 20px 0;
                border-radius: 5px;
            }}
            
            .metric-box {{
                display: inline-block;
                background-color: #3498db;
                color: white;
                padding: 10px 15px;
                margin: 5px;
                border-radius: 5px;
                font-weight: bold;
            }}
        </style>
    </head>
    <body>
        <div style="text-align: center; margin-bottom: 40px; padding: 20px; background-color: #f8f9fa; border-radius: 10px;">
            <h1>Complete Machine Learning Analysis Report</h1>
            <p><strong>With Execution Results and Visualizations</strong></p>
            <p>Generated on: {datetime.now().strftime("%B %d, %Y at %I:%M %p")}</p>
            <div class="results-highlight">
                <p><strong>🎯 This report includes actual execution results, performance metrics, and comprehensive visualizations from the machine learning analysis.</strong></p>
            </div>
        </div>
        
        {html_content}
        
        <div style="text-align: center; margin-top: 40px; padding: 20px; background-color: #f8f9fa; border-radius: 10px;">
            <p><strong>Report Generation Complete</strong></p>
            <p>Generated automatically with execution results and visualizations</p>
            <p>Timestamp: {datetime.now().isoformat()}</p>
        </div>
    </body>
    </html>
    """
    
    # Save complete HTML report
    complete_html_path = output_dir / "Complete_Technical_Report_With_Results.html"
    with open(complete_html_path, 'w', encoding='utf-8') as f:
        f.write(complete_html)
    
    print(f"✅ Complete HTML report with visualizations created: {complete_html_path}")
    return complete_html_path

def main():
    """Main function to create complete results report"""
    
    print("🔄 Creating complete technical report with execution results and visualizations...")
    
    # Create visualizations
    print("📊 Creating results visualizations...")
    create_results_visualizations()
    
    # Create executive dashboard
    print("📈 Creating executive dashboard...")
    create_executive_dashboard()
    
    # Create complete HTML report
    print("📄 Creating complete HTML report...")
    complete_html_path = create_complete_html_report()
    
    if complete_html_path:
        print("\n🎉 Complete technical report created successfully!")
        print(f"📁 Output location: {complete_html_path}")
        print("📊 Includes: Execution results, performance metrics, and visualizations")
        print("💡 Open the HTML file in your browser to view the complete report")
        return True
    else:
        print("❌ Failed to create complete report")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
