import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_auc_score
import warnings
warnings.filterwarnings('ignore')

print("=== ACUTE LEUKEMIA ML ANALYSIS ===")

# Load and examine data
df_major = pd.read_csv('data_diag.csv')
df_subgroup = pd.read_csv('data_diag_maj_sub.csv')

print(f"Data loaded: Major {df_major.shape}, Subgroup {df_subgroup.shape}")
print(f"Major classes: {df_major['Diagnosis'].value_counts().to_dict()}")
print(f"Subgroup classes: {df_subgroup['Diagnosis'].value_counts().to_dict()}")

# Prepare data
X_major = df_major.drop('Diagnosis', axis=1)
y_major = df_major['Diagnosis']
X_subgroup = df_subgroup.drop('Diagnosis', axis=1)
y_subgroup = df_subgroup['Diagnosis']

print(f"Features: {list(X_major.columns)}")

# Simple analysis function
def run_analysis(X, y, name):
    print(f"\n--- {name} ---")
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
    
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    models = {
        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
        'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
        'SVM': SVC(random_state=42, probability=True)
    }
    
    results = {}
    for model_name, model in models.items():
        model.fit(X_train_scaled, y_train)
        y_pred = model.predict(X_test_scaled)
        y_pred_proba = model.predict_proba(X_test_scaled)
        
        accuracy = accuracy_score(y_test, y_pred)
        
        if len(np.unique(y)) == 2:
            auc = roc_auc_score(y_test, y_pred_proba[:, 1])
        else:
            auc = roc_auc_score(y_test, y_pred_proba, multi_class='ovr', average='weighted')
        
        results[model_name] = {'accuracy': accuracy, 'auc': auc}
        print(f"{model_name}: Accuracy={accuracy:.4f}, AUC={auc:.4f}")
    
    return results

# Run analyses
results_major = run_analysis(X_major, y_major, "MAJOR CATEGORIES")
results_subgroup = run_analysis(X_subgroup, y_subgroup, "SUBGROUP CATEGORIES")

print("\n=== SUMMARY ===")
print("MAJOR CATEGORIES:")
for model, metrics in results_major.items():
    print(f"  {model}: Acc={metrics['accuracy']:.4f}, AUC={metrics['auc']:.4f}")

print("SUBGROUP CATEGORIES:")
for model, metrics in results_subgroup.items():
    print(f"  {model}: Acc={metrics['accuracy']:.4f}, AUC={metrics['auc']:.4f}")

print("\nAnalysis completed successfully!")
