#!/usr/bin/env python3
"""
Technical Report Converter
Converts the Technical_Report.md file to various output formats
"""

import os
import sys
import subprocess
import markdown
from pathlib import Path
from datetime import datetime
import json

def check_dependencies():
    """Check if required dependencies are available"""
    dependencies = {
        'markdown': 'pip install markdown'
    }

    missing = []
    for dep, install_cmd in dependencies.items():
        try:
            __import__(dep)
        except ImportError:
            missing.append((dep, install_cmd))

    if missing:
        print("Missing dependencies:")
        for dep, cmd in missing:
            print(f"  {dep}: {cmd}")
        return False
    return True

def convert_md_to_html(md_file_path, output_dir):
    """Convert Markdown to HTML with enhanced styling"""
    
    # Read the markdown file
    with open(md_file_path, 'r', encoding='utf-8') as f:
        md_content = f.read()
    
    # Configure markdown with extensions
    md = markdown.Markdown(extensions=[
        'markdown.extensions.toc',
        'markdown.extensions.tables',
        'markdown.extensions.codehilite',
        'markdown.extensions.fenced_code',
        'markdown.extensions.attr_list'
    ])
    
    # Convert to HTML
    html_content = md.convert(md_content)
    
    # Create enhanced HTML with CSS styling
    html_template = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Comprehensive Machine Learning Analysis - Technical Report</title>
        <style>
            body {{
                font-family: 'Georgia', 'Times New Roman', serif;
                line-height: 1.6;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
                color: #333;
                background-color: #fff;
            }}
            
            h1 {{
                color: #2c3e50;
                border-bottom: 3px solid #3498db;
                padding-bottom: 10px;
                font-size: 2.5em;
                text-align: center;
            }}
            
            h2 {{
                color: #34495e;
                border-bottom: 2px solid #ecf0f1;
                padding-bottom: 8px;
                margin-top: 40px;
                font-size: 1.8em;
            }}
            
            h3 {{
                color: #2c3e50;
                margin-top: 30px;
                font-size: 1.4em;
            }}
            
            h4 {{
                color: #34495e;
                margin-top: 25px;
                font-size: 1.2em;
            }}
            
            code {{
                background-color: #f8f9fa;
                padding: 2px 6px;
                border-radius: 3px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 0.9em;
                color: #e74c3c;
            }}
            
            pre {{
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 5px;
                padding: 15px;
                overflow-x: auto;
                margin: 20px 0;
            }}
            
            pre code {{
                background-color: transparent;
                padding: 0;
                color: #2c3e50;
            }}
            
            table {{
                border-collapse: collapse;
                width: 100%;
                margin: 20px 0;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }}
            
            th, td {{
                border: 1px solid #ddd;
                padding: 12px;
                text-align: left;
            }}
            
            th {{
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }}
            
            tr:nth-child(even) {{
                background-color: #f8f9fa;
            }}
            
            blockquote {{
                border-left: 4px solid #3498db;
                margin: 20px 0;
                padding: 10px 20px;
                background-color: #f8f9fa;
                font-style: italic;
            }}
            
            .toc {{
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 20px;
                margin: 30px 0;
            }}
            
            .toc h2 {{
                margin-top: 0;
                color: #2c3e50;
                border-bottom: none;
            }}
            
            .toc ul {{
                list-style-type: none;
                padding-left: 0;
            }}
            
            .toc li {{
                margin: 8px 0;
            }}
            
            .toc a {{
                color: #3498db;
                text-decoration: none;
                font-weight: 500;
            }}
            
            .toc a:hover {{
                text-decoration: underline;
            }}
            
            .header-info {{
                text-align: center;
                margin-bottom: 40px;
                padding: 20px;
                background-color: #f8f9fa;
                border-radius: 10px;
                border: 1px solid #e9ecef;
            }}
            
            .date-generated {{
                text-align: right;
                font-style: italic;
                color: #6c757d;
                margin-top: 30px;
                border-top: 1px solid #e9ecef;
                padding-top: 15px;
            }}
            
            @media print {{
                body {{
                    font-size: 12pt;
                    line-height: 1.4;
                }}
                
                h1 {{
                    font-size: 18pt;
                }}
                
                h2 {{
                    font-size: 16pt;
                    page-break-after: avoid;
                }}
                
                h3 {{
                    font-size: 14pt;
                    page-break-after: avoid;
                }}
                
                pre {{
                    page-break-inside: avoid;
                }}
                
                table {{
                    page-break-inside: avoid;
                }}
            }}
        </style>
    </head>
    <body>
        <div class="header-info">
            <h1>Comprehensive Machine Learning Analysis of Cell Population Data for Acute Leukemia Diagnosis</h1>
            <p><strong>Technical Documentation</strong></p>
            <p>Generated on: {datetime.now().strftime("%B %d, %Y at %I:%M %p")}</p>
        </div>
        
        {html_content}
        
        <div class="date-generated">
            <p>Document generated automatically from Technical_Report.md</p>
            <p>Generation timestamp: {datetime.now().isoformat()}</p>
        </div>
    </body>
    </html>
    """
    
    # Save HTML file
    html_output_path = output_dir / "Technical_Report.html"
    with open(html_output_path, 'w', encoding='utf-8') as f:
        f.write(html_template)
    
    print(f"✅ HTML report generated: {html_output_path}")
    return html_output_path

def create_print_friendly_html(html_file_path, output_dir):
    """Create a print-friendly HTML version for manual PDF conversion"""
    try:
        # Read the existing HTML
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()

        # Add print-specific CSS
        print_css = """
        <style media="print">
            @page {
                margin: 1in;
                size: A4;
            }
            body {
                font-size: 11pt;
                line-height: 1.4;
            }
            h1 { page-break-before: always; }
            h2 { page-break-after: avoid; }
            h3, h4 { page-break-after: avoid; }
            pre, table { page-break-inside: avoid; }
            .no-print { display: none; }
        </style>
        """

        # Insert print CSS before closing head tag
        html_content = html_content.replace('</head>', f'{print_css}</head>')

        # Save print-friendly version
        print_html_path = output_dir / "Technical_Report_Print_Ready.html"
        with open(print_html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"✅ Print-ready HTML generated: {print_html_path}")
        print("📝 To create PDF: Open the HTML file in your browser and use 'Print to PDF'")
        return print_html_path

    except Exception as e:
        print(f"❌ Error creating print-friendly HTML: {e}")
        return None

def main():
    """Main conversion function"""
    print("🔄 Starting Technical Report Conversion...")

    # Check for enhanced report first, then fall back to original
    enhanced_md_file = Path("technical_report_output/Technical_Report_With_Results.md")
    original_md_file = Path("Technical_Report.md")

    if enhanced_md_file.exists():
        md_file = enhanced_md_file
        print("📊 Using enhanced report with execution results")
    elif original_md_file.exists():
        md_file = original_md_file
        print("📄 Using original technical report")
    else:
        print(f"❌ Error: No technical report found!")
        return False
    
    # Create output directory
    output_dir = Path("technical_report_output")
    output_dir.mkdir(exist_ok=True)
    
    try:
        # Convert to HTML
        print("📄 Converting Markdown to HTML...")
        html_file = convert_md_to_html(md_file, output_dir)
        
        # Create print-friendly HTML
        print("📑 Creating print-ready HTML...")
        print_file = create_print_friendly_html(html_file, output_dir)

        print("\n🎉 Conversion completed successfully!")
        print(f"📁 Output directory: {output_dir.absolute()}")
        print(f"📄 HTML file: {html_file}")
        if print_file:
            print(f"📑 Print-ready HTML: {print_file}")
            print("💡 To create PDF: Open the print-ready HTML in your browser and use 'Print to PDF'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during conversion: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
