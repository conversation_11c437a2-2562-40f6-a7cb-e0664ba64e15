import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, classification_report
import warnings
warnings.filterwarnings('ignore')

def run_ml_pipeline():
    print("=== Starting ML Pipeline Execution ===")
    
    # 1. Load datasets
    df_major = pd.read_csv('data_diag.csv')
    df_subgroup = pd.read_csv('data_diag_maj_sub.csv')
    
    # Extract features and targets
    X_major = df_major.drop('Diagnosis', axis=1)
    y_major = df_major['Diagnosis']
    X_subgroup = df_subgroup.drop('Diagnosis', axis=1)
    y_subgroup = df_subgroup['Diagnosis']
    
    print(f"Dataset loaded - Major: {X_major.shape}, Subgroup: {X_subgroup.shape}")
    
    # 2. Train-test split
    X_train_maj, X_test_maj, y_train_maj, y_test_maj = train_test_split(
        X_major, y_major, test_size=0.2, random_state=42, stratify=y_major
    )
    X_train_sub, X_test_sub, y_train_sub, y_test_sub = train_test_split(
        X_subgroup, y_subgroup, test_size=0.2, random_state=42, stratify=y_subgroup
    )
    
    print(f"Train/test split complete - Train: {X_train_maj.shape[0]}, Test: {X_test_maj.shape[0]}")
    
    # 3. Feature Engineering
    def enhanced_feature_engineering(X):
        X_eng = X.copy()
        
        # Statistical features for each cell type
        for cell_type in ['NE', 'LY', 'MO']:
            features = [col for col in X.columns if col.startswith(cell_type)]
            if features:
                X_eng[f'{cell_type}_mean'] = X[features].mean(axis=1)
                X_eng[f'{cell_type}_std'] = X[features].std(axis=1)
                X_eng[f'{cell_type}_max'] = X[features].max(axis=1)
                X_eng[f'{cell_type}_min'] = X[features].min(axis=1)
                X_eng[f'{cell_type}_range'] = X[features].max(axis=1) - X[features].min(axis=1)
                X_eng[f'{cell_type}_cv'] = X[features].std(axis=1) / (X[features].mean(axis=1) + 1e-8)
        
        # Relational features
        if all(f'{ct}_mean' in X_eng.columns for ct in ['NE', 'LY', 'MO']):
            X_eng['NE_LY_ratio'] = X_eng['NE_mean'] / (X_eng['LY_mean'] + 1e-8)
            X_eng['NE_MO_ratio'] = X_eng['NE_mean'] / (X_eng['MO_mean'] + 1e-8)
            X_eng['LY_MO_ratio'] = X_eng['LY_mean'] / (X_eng['MO_mean'] + 1e-8)
        
        # Geometric features
        for cell_type in ['NE', 'LY', 'MO']:
            x_col, y_col, z_col = f'{cell_type}X', f'{cell_type}Y', f'{cell_type}Z'
            if all(col in X.columns for col in [x_col, y_col, z_col]):
                X_eng[f'{cell_type}_magnitude'] = np.sqrt(X[x_col]**2 + X[y_col]**2 + X[z_col]**2)
        
        return X_eng
    
    # Apply feature engineering
    X_train_maj_eng = enhanced_feature_engineering(X_train_maj)
    X_test_maj_eng = enhanced_feature_engineering(X_test_maj)
    X_train_sub_eng = enhanced_feature_engineering(X_train_sub)
    X_test_sub_eng = enhanced_feature_engineering(X_test_sub)
    
    print(f"Feature engineering complete - Original: {X_train_maj.shape[1]}, Enhanced: {X_train_maj_eng.shape[1]}")
    
    # 4. Feature scaling
    scaler_maj_eng = StandardScaler()
    X_train_maj_eng_scaled = scaler_maj_eng.fit_transform(X_train_maj_eng)
    X_test_maj_eng_scaled = scaler_maj_eng.transform(X_test_maj_eng)
    
    scaler_sub_eng = StandardScaler()
    X_train_sub_eng_scaled = scaler_sub_eng.fit_transform(X_train_sub_eng)
    X_test_sub_eng_scaled = scaler_sub_eng.transform(X_test_sub_eng)
    
    print("Feature scaling complete")
    
    # 5. Model training
    models_config = {
        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
        'Gradient Boosting': GradientBoostingClassifier(n_estimators=100, random_state=42),
        'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
        'SVM': SVC(random_state=42, probability=True)
    }
    
    # Train models for major categories
    models_major = {}
    print("\\nTraining models for major categories:")
    for name, model in models_config.items():
        print(f"  Training {name}...")
        if name in ['Logistic Regression', 'SVM']:
            models_major[name] = model.fit(X_train_maj_eng_scaled, y_train_maj)
        else:
            models_major[name] = model.fit(X_train_maj_eng, y_train_maj)
    
    # Train models for subgroups
    models_subgroup = {}
    print("\\nTraining models for subgroups:")
    for name, model in models_config.items():
        print(f"  Training {name}...")
        if name in ['Logistic Regression', 'SVM']:
            models_subgroup[name] = model.fit(X_train_sub_eng_scaled, y_train_sub)
        else:
            models_subgroup[name] = model.fit(X_train_sub_eng, y_train_sub)
    
    # 6. Model evaluation
    def evaluate_models(models, X_test_eng, X_test_scaled, y_test, dataset_name):
        results = []
        print(f"\\n=== Model Evaluation for {dataset_name} ===")
        
        for name, model in models.items():
            X_test_model = X_test_scaled if name in ['Logistic Regression', 'SVM'] else X_test_eng
            
            y_pred = model.predict(X_test_model)
            y_pred_proba = model.predict_proba(X_test_model)
            
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average='macro', zero_division=0)
            recall = recall_score(y_test, y_pred, average='macro', zero_division=0)
            f1 = f1_score(y_test, y_pred, average='macro', zero_division=0)
            
            try:
                auc = roc_auc_score(y_test, y_pred_proba, multi_class='ovr', average='macro')
            except:
                auc = np.nan
            
            results.append({
                'Model': name,
                'Accuracy': accuracy,
                'Precision': precision,
                'Recall': recall,
                'F1-Score': f1,
                'AUC': auc
            })
            
            print(f"  {name}:")
            print(f"    Accuracy: {accuracy:.4f}")
            print(f"    Precision: {precision:.4f}")
            print(f"    Recall: {recall:.4f}")
            print(f"    F1-Score: {f1:.4f}")
            if not np.isnan(auc):
                print(f"    AUC: {auc:.4f}")
        
        return pd.DataFrame(results)
    
    # Evaluate both datasets
    results_major = evaluate_models(models_major, X_test_maj_eng, X_test_maj_eng_scaled, y_test_maj, "Major Categories")
    results_subgroup = evaluate_models(models_subgroup, X_test_sub_eng, X_test_sub_eng_scaled, y_test_sub, "Subgroup Classifications")
    
    # Save results
    results_major.to_csv('ml_results_major.csv', index=False)
    results_subgroup.to_csv('ml_results_subgroup.csv', index=False)
    
    print("\\n=== Analysis Complete ===")
    print("\\nBest performing models:")
    best_major = results_major.loc[results_major['F1-Score'].idxmax()]
    best_subgroup = results_subgroup.loc[results_subgroup['F1-Score'].idxmax()]
    
    print(f"Major Categories - Best: {best_major['Model']} (F1: {best_major['F1-Score']:.4f})")
    print(f"Subgroup Classifications - Best: {best_subgroup['Model']} (F1: {best_subgroup['F1-Score']:.4f})")
    
    # Show detailed classification reports for best models
    print(f"\\n=== Detailed Results for Best Models ===")
    
    # Best model for major categories
    best_model_maj = models_major[best_major['Model']]
    X_test_best_maj = X_test_maj_eng_scaled if best_major['Model'] in ['Logistic Regression', 'SVM'] else X_test_maj_eng
    y_pred_best_maj = best_model_maj.predict(X_test_best_maj)
    
    print(f"\\nMajor Categories - {best_major['Model']} Classification Report:")
    print(classification_report(y_test_maj, y_pred_best_maj))
    
    # Best model for subgroups
    best_model_sub = models_subgroup[best_subgroup['Model']]
    X_test_best_sub = X_test_sub_eng_scaled if best_subgroup['Model'] in ['Logistic Regression', 'SVM'] else X_test_sub_eng
    y_pred_best_sub = best_model_sub.predict(X_test_best_sub)
    
    print(f"\\nSubgroup Classifications - {best_subgroup['Model']} Classification Report:")
    print(classification_report(y_test_sub, y_pred_best_sub))
    
    return results_major, results_subgroup

if __name__ == "__main__":
    results_major, results_subgroup = run_ml_pipeline()
