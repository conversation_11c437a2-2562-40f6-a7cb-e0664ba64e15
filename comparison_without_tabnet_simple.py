import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import logging
import json
import pickle
from datetime import datetime
from pathlib import Path

from sklearn.model_selection import (train_test_split, cross_validate, StratifiedKFold,
                                   RandomizedSearchCV, GridSearchCV, validation_curve)
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.pipeline import Pipeline
from sklearn.metrics import (accuracy_score, precision_score, recall_score,
                             f1_score, roc_auc_score, confusion_matrix, RocCurveDisplay,
                             classification_report, precision_recall_curve, average_precision_score)
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import (RandomForestClassifier, GradientBoostingClassifier,
                            StackingClassifier, VotingClassifier, ExtraTreesClassifier)
from sklearn.svm import SVC
from sklearn.neighbors import KNeighborsClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.feature_selection import SelectKBest, f_classif, RFE
import xgboost as xgb
import lightgbm as lgb
from catboost import CatBoostClassifier
from sklearn.gaussian_process import GaussianProcessClassifier
from sklearn.gaussian_process.kernels import RBF
from scipy import stats

# Configuration
RANDOM_STATE = 42
TEST_SIZE = 0.20
CV_FOLDS = 5
N_JOBS = -1

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('model_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Configuration class for better parameter management
class ModelConfig:
    """Configuration class for model parameters and hyperparameter grids"""

    # Enhanced hyperparameter grids for systematic tuning
    PARAM_GRIDS = {
        'LogisticRegression': {
            'clf__C': [0.001, 0.01, 0.1, 1.0, 10.0, 100.0],
            'clf__penalty': ['l1', 'l2', 'elasticnet'],
            'clf__solver': ['liblinear', 'saga'],
            'clf__max_iter': [1000, 2000]
        },
        'RandomForest': {
            'n_estimators': [100, 200, 300, 500],
            'max_depth': [5, 10, 15, 20, None],
            'min_samples_split': [2, 5, 10, 15],
            'min_samples_leaf': [1, 2, 5, 10],
            'max_features': ['sqrt', 'log2', None],
            'bootstrap': [True, False]
        },
        'XGBoost': {
            'n_estimators': [100, 200, 300, 500],
            'learning_rate': [0.01, 0.05, 0.1, 0.2],
            'max_depth': [3, 4, 5, 6, 8],
            'subsample': [0.6, 0.8, 1.0],
            'colsample_bytree': [0.6, 0.8, 1.0],
            'reg_alpha': [0, 0.1, 0.5, 1.0],
            'reg_lambda': [0, 0.1, 1.0, 5.0]
        },
        'LightGBM': {
            'n_estimators': [100, 200, 300, 500],
            'learning_rate': [0.01, 0.05, 0.1, 0.2],
            'num_leaves': [20, 31, 50, 80],
            'max_depth': [-1, 5, 10, 15],
            'reg_alpha': [0, 0.1, 0.5],
            'reg_lambda': [0, 0.1, 0.5],
            'subsample': [0.6, 0.8, 1.0]
        },
        'MLP': {
            'clf__hidden_layer_sizes': [(50,), (100,), (64, 32), (100, 50), (128, 64, 32)],
            'clf__activation': ['relu', 'tanh'],
            'clf__alpha': [0.0001, 0.001, 0.01, 0.1],
            'clf__learning_rate': ['constant', 'adaptive'],
            'clf__learning_rate_init': [0.001, 0.01, 0.1],
            'clf__max_iter': [500, 1000, 2000]
        }
    }

# Utility functions
def create_results_directory():
    """Create directory for storing results"""
    results_dir = Path("enhanced_results")
    results_dir.mkdir(exist_ok=True)
    return results_dir

def save_model_config(config, filepath):
    """Save model configuration to JSON file"""
    with open(filepath, 'w') as f:
        json.dump(config, f, indent=2, default=str)

def load_and_explore_data(filepath="data_diag.csv"):
    """Load data and perform basic exploration"""
    logger.info(f"Loading data from {filepath}")

    df = pd.read_csv(filepath)
    X = df.drop(columns=["Diagnosis"])
    y = df["Diagnosis"]

    # Data exploration
    logger.info(f"Dataset shape: {df.shape}")
    logger.info(f"Features: {X.columns.tolist()}")
    logger.info(f"Target distribution:\n{y.value_counts().sort_index()}")
    logger.info(f"Missing values: {df.isnull().sum().sum()}")

    # Check for class imbalance
    class_distribution = y.value_counts(normalize=True).sort_index()
    logger.info(f"Class distribution (proportions):\n{class_distribution}")

    return X, y

# ---------------------------------------------------------------------
# 1. Load and explore data
# ---------------------------------------------------------------------

X, y = load_and_explore_data()

# Stratified split to keep class ratios
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=TEST_SIZE, stratify=y, random_state=RANDOM_STATE)

logger.info(f"Training set size: {X_train.shape[0]}")
logger.info(f"Test set size: {X_test.shape[0]}")
logger.info(f"Training class distribution:\n{y_train.value_counts().sort_index()}")
logger.info(f"Test class distribution:\n{y_test.value_counts().sort_index()}")

# ---------------------------------------------------------------------
# 2. Enhanced model definitions with improved regularization
# ---------------------------------------------------------------------

def create_enhanced_models():
    """Create enhanced models with improved regularization and architecture"""

    models = {
        "Logistic Regression": Pipeline([
            ("scaler", StandardScaler()),
            ("clf", LogisticRegression(
                C=0.1,  # Stronger L2 regularization
                max_iter=2000,  # Increased iterations
                multi_class="multinomial",
                random_state=RANDOM_STATE,
                penalty='l2',
                solver='lbfgs'  # Better solver for multinomial
            ))
        ]),

        "Random Forest": RandomForestClassifier(
            n_estimators=500,  # Increased trees
            max_depth=12,  # Slightly deeper trees
            min_samples_split=8,  # Better regularization
            min_samples_leaf=4,  # Better regularization
            max_features='sqrt',  # Feature subsampling
            bootstrap=True,
            oob_score=True,  # Out-of-bag scoring
            random_state=RANDOM_STATE,
            n_jobs=N_JOBS
        ),

        "Gradient Boosting": GradientBoostingClassifier(
            n_estimators=300,  # Increased estimators
            learning_rate=0.05,  # Conservative learning rate
            max_depth=4,  # Shallow trees
            subsample=0.8,  # Stochastic gradient boosting
            max_features='sqrt',
            min_samples_split=10,
            min_samples_leaf=5,
            random_state=RANDOM_STATE
        ),

        "XGBoost": xgb.XGBClassifier(
            n_estimators=500,  # More estimators
            learning_rate=0.05,  # Lower learning rate
            max_depth=4,  # Shallower trees
            reg_alpha=0.1,  # L1 regularization
            reg_lambda=1.5,  # Stronger L2 regularization
            subsample=0.8,
            colsample_bytree=0.8,
            colsample_bylevel=0.8,  # Additional regularization
            gamma=0.1,  # Minimum split loss
            min_child_weight=3,  # Minimum sum of instance weight
            random_state=RANDOM_STATE,
            n_jobs=N_JOBS,
            eval_metric='mlogloss'
        ),

        "SVM (RBF)": Pipeline([
            ("scaler", StandardScaler()),
            ("clf", SVC(
                kernel="rbf",
                probability=True,
                C=1.0,  # Regularization parameter
                gamma='scale',  # Automatic gamma scaling
                random_state=RANDOM_STATE
            ))
        ]),

        "k‑NN": Pipeline([
            ("scaler", StandardScaler()),
            ("clf", KNeighborsClassifier(
                n_neighbors=7,  # Slightly more neighbors
                weights='distance',  # Distance-weighted voting
                metric='minkowski',
                p=2
            ))
        ]),

        "Enhanced MLP": Pipeline([
            ("scaler", StandardScaler()),
            ("clf", MLPClassifier(
                hidden_layer_sizes=(128, 64, 32),  # Deeper architecture
                activation='relu',
                alpha=0.01,  # L2 regularization
                learning_rate='adaptive',  # Adaptive learning rate
                learning_rate_init=0.001,
                max_iter=2000,  # More iterations
                early_stopping=True,  # Early stopping
                validation_fraction=0.1,
                n_iter_no_change=20,  # Patience for early stopping
                random_state=RANDOM_STATE
            ))
        ]),

        "LightGBM": lgb.LGBMClassifier(
            n_estimators=500,
            learning_rate=0.05,
            num_leaves=31,
            max_depth=6,
            reg_alpha=0.1,  # L1 regularization
            reg_lambda=0.1,  # L2 regularization
            subsample=0.8,
            colsample_bytree=0.8,
            min_child_samples=10,  # Minimum samples in leaf
            random_state=RANDOM_STATE,
            n_jobs=N_JOBS,
            verbose=-1
        ),

        "CatBoost": CatBoostClassifier(
            iterations=500,
            learning_rate=0.05,
            depth=6,
            l2_leaf_reg=3.0,  # L2 regularization
            border_count=128,
            random_seed=RANDOM_STATE,
            verbose=0,
            thread_count=N_JOBS if N_JOBS > 0 else None
        ),

        "Gaussian Process": Pipeline([
            ("scaler", StandardScaler()),
            ("clf", GaussianProcessClassifier(
                kernel=1.0 * RBF(1.0),
                random_state=RANDOM_STATE,
                max_iter_predict=100,
                n_restarts_optimizer=2
            ))
        ]),

        "Extra Trees": ExtraTreesClassifier(
            n_estimators=500,
            max_depth=12,
            min_samples_split=8,
            min_samples_leaf=4,
            max_features='sqrt',
            bootstrap=True,
            oob_score=True,
            random_state=RANDOM_STATE,
            n_jobs=N_JOBS
        )
    }

    return models

# Create enhanced models
models = create_enhanced_models()

# Enhanced ensemble methods
def create_ensemble_models():
    """Create enhanced ensemble models"""

    # Base models for ensemble
    base_models = [
        ('rf', RandomForestClassifier(
            n_estimators=200,
            max_depth=10,
            min_samples_split=5,
            random_state=RANDOM_STATE,
            n_jobs=N_JOBS
        )),
        ('xgb', xgb.XGBClassifier(
            n_estimators=200,
            learning_rate=0.1,
            max_depth=4,
            reg_alpha=0.1,
            reg_lambda=1.0,
            random_state=RANDOM_STATE,
            n_jobs=N_JOBS
        )),
        ('lgb', lgb.LGBMClassifier(
            n_estimators=200,
            learning_rate=0.1,
            num_leaves=31,
            random_state=RANDOM_STATE,
            n_jobs=N_JOBS,
            verbose=-1
        ))
    ]

    # Scaled base models for SVM
    scaled_base_models = [
        ('rf', RandomForestClassifier(
            n_estimators=200,
            max_depth=10,
            random_state=RANDOM_STATE,
            n_jobs=N_JOBS
        )),
        ('svm', Pipeline([
            ("scaler", StandardScaler()),
            ("clf", SVC(
                probability=True,
                C=1.0,
                gamma='scale',
                random_state=RANDOM_STATE
            ))
        ])),
        ('lr', Pipeline([
            ("scaler", StandardScaler()),
            ("clf", LogisticRegression(
                C=1.0,
                max_iter=1000,
                random_state=RANDOM_STATE
            ))
        ]))
    ]

    ensemble_models = {
        "Stacking Classifier": StackingClassifier(
            estimators=scaled_base_models,
            final_estimator=LogisticRegression(
                C=1.0,
                max_iter=1000,
                random_state=RANDOM_STATE
            ),
            cv=CV_FOLDS,
            n_jobs=N_JOBS
        ),

        "Voting Classifier (Hard)": VotingClassifier(
            estimators=base_models,
            voting='hard',
            n_jobs=N_JOBS
        ),

        "Voting Classifier (Soft)": VotingClassifier(
            estimators=scaled_base_models,
            voting='soft',
            n_jobs=N_JOBS
        )
    }

    return ensemble_models

# Add ensemble models to main models dictionary
ensemble_models = create_ensemble_models()
models.update(ensemble_models)

# ---------------------------------------------------------------------
# 3. Enhanced evaluation and hyperparameter tuning functions
# ---------------------------------------------------------------------

def evaluate_with_cv(models, X, y, cv=5, scoring_metrics=None):
    """Enhanced cross-validation with comprehensive metrics and statistical analysis"""

    if scoring_metrics is None:
        scoring_metrics = ['accuracy', 'precision_macro', 'recall_macro', 'f1_macro', 'roc_auc_ovr']

    cv_results = {}
    detailed_scores = {}
    skf = StratifiedKFold(n_splits=cv, shuffle=True, random_state=RANDOM_STATE)

    logger.info(f"Starting {cv}-fold cross-validation for {len(models)} models...")

    for name, model in models.items():
        logger.info(f"Cross-validating {name}...")

        try:
            scores = cross_validate(
                model, X, y,
                cv=skf,
                scoring=scoring_metrics,
                return_train_score=True,
                n_jobs=1  # Avoid nested parallelism
            )

            # Store detailed scores for statistical analysis
            detailed_scores[name] = scores

            # Calculate statistics
            cv_results[name] = {}
            for metric in scoring_metrics:
                test_scores = scores[f'test_{metric}']
                train_scores = scores[f'train_{metric}']

                cv_results[name][f'test_{metric}_mean'] = test_scores.mean()
                cv_results[name][f'test_{metric}_std'] = test_scores.std()
                cv_results[name][f'train_{metric}_mean'] = train_scores.mean()
                cv_results[name][f'train_{metric}_std'] = train_scores.std()
                cv_results[name][f'{metric}_overfitting'] = train_scores.mean() - test_scores.mean()

            # Summary string for display
            cv_results[name]['summary'] = (
                f"Acc: {scores['test_accuracy'].mean():.3f}±{scores['test_accuracy'].std():.3f}, "
                f"F1: {scores['test_f1_macro'].mean():.3f}±{scores['test_f1_macro'].std():.3f}, "
                f"AUC: {scores['test_roc_auc_ovr'].mean():.3f}±{scores['test_roc_auc_ovr'].std():.3f}"
            )

        except Exception as e:
            logger.error(f"Error evaluating {name}: {str(e)}")
            cv_results[name] = {'error': str(e)}

    return pd.DataFrame.from_dict(cv_results, orient='index'), detailed_scores

def perform_hyperparameter_tuning(models_to_tune, X_train, y_train, quick_tune=True):
    """Perform hyperparameter tuning for selected models"""

    tuned_models = {}
    tuning_results = {}

    logger.info(f"Starting hyperparameter tuning for {len(models_to_tune)} models...")

    # Determine search parameters based on quick_tune flag
    n_iter = 10 if quick_tune else 50
    cv_folds = 3 if quick_tune else CV_FOLDS

    for model_name in models_to_tune:
        if model_name not in ModelConfig.PARAM_GRIDS:
            logger.warning(f"No parameter grid defined for {model_name}, skipping...")
            continue

        logger.info(f"Tuning {model_name}...")

        try:
            # Get base model
            if model_name in models:
                base_model = models[model_name]
            else:
                logger.warning(f"Model {model_name} not found in models dictionary")
                continue

            # Setup search
            param_grid = ModelConfig.PARAM_GRIDS[model_name]

            search = RandomizedSearchCV(
                base_model,
                param_grid,
                n_iter=n_iter,
                cv=cv_folds,
                scoring='accuracy',
                random_state=RANDOM_STATE,
                n_jobs=1,  # Avoid nested parallelism
                verbose=1
            )

            # Perform search
            search.fit(X_train, y_train)

            # Store results
            tuned_models[model_name] = search.best_estimator_
            tuning_results[model_name] = {
                'best_score': search.best_score_,
                'best_params': search.best_params_,
                'cv_results': search.cv_results_
            }

            logger.info(f"Best score for {model_name}: {search.best_score_:.4f}")
            logger.info(f"Best parameters: {search.best_params_}")

        except Exception as e:
            logger.error(f"Error tuning {model_name}: {str(e)}")
            tuning_results[model_name] = {'error': str(e)}

    return tuned_models, tuning_results

# ---------------------------------------------------------------------
# 4. Execute enhanced training pipeline
# ---------------------------------------------------------------------

# Create results directory
results_dir = create_results_directory()
logger.info(f"Results will be saved to: {results_dir}")

# Step 1: Initial cross-validation evaluation
logger.info("=" * 60)
logger.info("STEP 1: Initial Cross-Validation Evaluation")
logger.info("=" * 60)

cv_results_df, detailed_cv_scores = evaluate_with_cv(models, X, y, cv=CV_FOLDS)
print("\n===== Initial Cross-Validation Results =====\n")
print(cv_results_df['summary'])

# Save initial CV results
cv_results_df.to_csv(results_dir / "initial_cv_results.csv", index_label="Model")
logger.info("Initial CV results saved to initial_cv_results.csv")

# Step 2: Hyperparameter tuning for selected models
logger.info("=" * 60)
logger.info("STEP 2: Hyperparameter Tuning")
logger.info("=" * 60)

# Select models for tuning (avoid computationally expensive ones for quick demo)
models_to_tune = ['RandomForest', 'XGBoost', 'LightGBM', 'MLP']
tuned_models, tuning_results = perform_hyperparameter_tuning(
    models_to_tune, X_train, y_train, quick_tune=True
)

# Update models with tuned versions
for model_name, tuned_model in tuned_models.items():
    models[f"Tuned {model_name}"] = tuned_model
    logger.info(f"Added tuned version of {model_name}")

# Save tuning results
tuning_summary = {}
for model_name, results in tuning_results.items():
    if 'error' not in results:
        tuning_summary[model_name] = {
            'best_score': results['best_score'],
            'best_params': results['best_params']
        }

save_model_config(tuning_summary, results_dir / "hyperparameter_tuning_results.json")

# Step 3: Final evaluation with tuned models
logger.info("=" * 60)
logger.info("STEP 3: Final Model Training and Evaluation")
logger.info("=" * 60)

# Enhanced training and evaluation with comprehensive metrics
def train_and_evaluate_models(models, X_train, y_train, X_test, y_test):
    """Train models and evaluate with comprehensive metrics"""

    metric_names = ["Accuracy", "Precision", "Recall", "F1", "ROC AUC"]
    metrics = {m: [] for m in metric_names}
    prob_scores = {}
    training_times = []
    prediction_times = []
    model_details = {}

    logger.info(f"Training and evaluating {len(models)} models...")

    for name, model in models.items():
        logger.info(f"Training {name}...")

        try:
            # Training time
            start_time = datetime.now()
            model.fit(X_train, y_train)
            training_time = (datetime.now() - start_time).total_seconds()
            training_times.append(training_time)

            # Prediction time
            start_time = datetime.now()
            y_pred = model.predict(X_test)
            prediction_time = (datetime.now() - start_time).total_seconds()
            prediction_times.append(prediction_time)

            # Probabilities / decision scores
            if hasattr(model, "predict_proba"):
                y_score = model.predict_proba(X_test)
            else:
                try:
                    y_dec = model.decision_function(X_test)
                    # decision_function can be (n_samples,) or (n_samples, n_classes)
                    if y_dec.ndim == 1:
                        y_score = np.column_stack([1 - y_dec, y_dec])
                    else:
                        y_score = y_dec
                except:
                    # Fallback for models without decision_function
                    y_score = np.eye(len(np.unique(y_test)))[y_pred]

            prob_scores[name] = y_score

            # Calculate metrics
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average="macro", zero_division=0)
            recall = recall_score(y_test, y_pred, average="macro", zero_division=0)
            f1 = f1_score(y_test, y_pred, average="macro", zero_division=0)

            try:
                roc_auc = roc_auc_score(y_test, y_score, multi_class="ovr")
            except:
                roc_auc = 0.0  # Fallback for problematic cases

            metrics["Accuracy"].append(accuracy)
            metrics["Precision"].append(precision)
            metrics["Recall"].append(recall)
            metrics["F1"].append(f1)
            metrics["ROC AUC"].append(roc_auc)

            # Store additional details
            model_details[name] = {
                'training_time': training_time,
                'prediction_time': prediction_time,
                'n_features': X_train.shape[1],
                'n_samples_train': X_train.shape[0],
                'n_samples_test': X_test.shape[0]
            }

            # Add model-specific details
            if hasattr(model, 'oob_score_'):
                model_details[name]['oob_score'] = model.oob_score_
            if hasattr(model, 'feature_importances_'):
                model_details[name]['has_feature_importance'] = True

            logger.info(f"Completed {name}: Accuracy={accuracy:.3f}, F1={f1:.3f}, Time={training_time:.2f}s")

        except Exception as e:
            logger.error(f"Error training {name}: {str(e)}")
            # Add placeholder values for failed models
            for metric in metric_names:
                metrics[metric].append(0.0)
            training_times.append(0.0)
            prediction_times.append(0.0)
            prob_scores[name] = np.zeros((len(y_test), len(np.unique(y_test))))
            model_details[name] = {'error': str(e)}

    # Create comprehensive results dataframe
    results_df = pd.DataFrame(metrics, index=models.keys())
    results_df['Training_Time'] = training_times
    results_df['Prediction_Time'] = prediction_times
    results_df = results_df.sort_values("Accuracy", ascending=False)

    return results_df, prob_scores, model_details

# Execute training and evaluation
results_df, prob_scores, model_details = train_and_evaluate_models(
    models, X_train, y_train, X_test, y_test
)

print("\n===== Enhanced Test Set Performance =====\n")
print(results_df.round(3).to_string())

# Save detailed results
results_df.to_csv(results_dir / "enhanced_model_metrics.csv", index_label="Model")
save_model_config(model_details, results_dir / "model_training_details.json")

# ---------------------------------------------------------------------
# 5. Enhanced Visualizations and Analysis
# ---------------------------------------------------------------------

def create_enhanced_visualizations(results_df, prob_scores, y_test, detailed_cv_scores, results_dir):
    """Create comprehensive visualizations for model analysis"""

    logger.info("Creating enhanced visualizations...")

    # Set up the plotting environment
    plt.style.use('seaborn-v0_8')

    # 1. Enhanced Performance Comparison
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # Performance metrics bar chart
    metric_cols = ["Accuracy", "Precision", "Recall", "F1", "ROC AUC"]
    top_models = results_df.head(10)  # Show top 10 models

    x = np.arange(len(top_models))
    bar_width = 0.15

    for i, metric in enumerate(metric_cols):
        ax1.bar(x + i * bar_width, top_models[metric], bar_width,
                label=metric, alpha=0.8)

    ax1.set_xlabel('Models')
    ax1.set_ylabel('Score')
    ax1.set_title('Top 10 Model Performance Comparison')
    ax1.set_xticks(x + bar_width * 2)
    ax1.set_xticklabels(top_models.index, rotation=45, ha='right')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Training vs Prediction Time
    ax2.scatter(top_models['Training_Time'], top_models['Accuracy'],
                s=100, alpha=0.7, c=top_models['F1'], cmap='viridis')
    ax2.set_xlabel('Training Time (seconds)')
    ax2.set_ylabel('Accuracy')
    ax2.set_title('Accuracy vs Training Time')
    ax2.grid(True, alpha=0.3)

    # Add model names as annotations
    for idx, (name, row) in enumerate(top_models.iterrows()):
        if idx < 5:  # Annotate top 5 to avoid clutter
            ax2.annotate(name, (row['Training_Time'], row['Accuracy']),
                        xytext=(5, 5), textcoords='offset points', fontsize=8)

    # Performance distribution
    ax3.boxplot([top_models[col] for col in metric_cols], labels=metric_cols)
    ax3.set_title('Performance Metrics Distribution')
    ax3.set_ylabel('Score')
    ax3.grid(True, alpha=0.3)

    # Model complexity vs performance
    complexity_proxy = top_models['Training_Time'] * top_models['Prediction_Time']
    ax4.scatter(complexity_proxy, top_models['Accuracy'],
                s=100, alpha=0.7, c=top_models['ROC AUC'], cmap='plasma')
    ax4.set_xlabel('Complexity Proxy (Train_Time × Pred_Time)')
    ax4.set_ylabel('Accuracy')
    ax4.set_title('Model Complexity vs Accuracy')
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(results_dir / "enhanced_model_comparison.png", dpi=300, bbox_inches='tight')
    plt.show()

    # 2. Cross-validation analysis
    if detailed_cv_scores:
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # CV score distributions
        cv_accuracies = []
        cv_f1_scores = []
        model_names = []

        for name, scores in detailed_cv_scores.items():
            if 'test_accuracy' in scores:
                cv_accuracies.extend(scores['test_accuracy'])
                cv_f1_scores.extend(scores['test_f1_macro'])
                model_names.extend([name] * len(scores['test_accuracy']))

        # Create DataFrame for easier plotting
        cv_df = pd.DataFrame({
            'Model': model_names,
            'Accuracy': cv_accuracies,
            'F1_Score': cv_f1_scores
        })

        # Box plot of CV accuracies
        top_cv_models = cv_df.groupby('Model')['Accuracy'].mean().nlargest(8).index
        cv_subset = cv_df[cv_df['Model'].isin(top_cv_models)]

        cv_subset.boxplot(column='Accuracy', by='Model', ax=axes[0,0])
        axes[0,0].set_title('Cross-Validation Accuracy Distribution')
        axes[0,0].set_xlabel('Model')
        axes[0,0].tick_params(axis='x', rotation=45)

        # Overfitting analysis
        overfitting_data = []
        for name, scores in detailed_cv_scores.items():
            if 'test_accuracy' in scores and 'train_accuracy' in scores:
                train_mean = scores['train_accuracy'].mean()
                test_mean = scores['test_accuracy'].mean()
                overfitting_data.append({
                    'Model': name,
                    'Train_Accuracy': train_mean,
                    'Test_Accuracy': test_mean,
                    'Overfitting': train_mean - test_mean
                })

        if overfitting_data:
            overfitting_df = pd.DataFrame(overfitting_data)
            overfitting_df = overfitting_df.nlargest(8, 'Test_Accuracy')

            axes[0,1].scatter(overfitting_df['Test_Accuracy'], overfitting_df['Overfitting'],
                             s=100, alpha=0.7)
            axes[0,1].set_xlabel('Test Accuracy')
            axes[0,1].set_ylabel('Overfitting (Train - Test)')
            axes[0,1].set_title('Overfitting Analysis')
            axes[0,1].grid(True, alpha=0.3)

            # Add model names
            for _, row in overfitting_df.iterrows():
                axes[0,1].annotate(row['Model'],
                                  (row['Test_Accuracy'], row['Overfitting']),
                                  xytext=(5, 5), textcoords='offset points', fontsize=8)

        plt.tight_layout()
        plt.savefig(results_dir / "cross_validation_analysis.png", dpi=300, bbox_inches='tight')
        plt.show()

    return True

# Create visualizations
create_enhanced_visualizations(results_df, prob_scores, y_test, detailed_cv_scores, results_dir)

# Enhanced analysis for best models
def analyze_best_models(results_df, models, prob_scores, X_test, y_test, results_dir):
    """Detailed analysis of the best performing models"""

    logger.info("Analyzing best performing models...")

    # Get top 3 models
    top_models = results_df.head(3)

    # Create detailed analysis plots
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))

    for idx, (model_name, _) in enumerate(top_models.iterrows()):
        if idx >= 3:
            break

        logger.info(f"Analyzing {model_name}...")

        # Get predictions
        model = models[model_name]
        y_pred = model.predict(X_test)

        # Confusion Matrix
        cm = confusion_matrix(y_test, y_pred)

        # Plot confusion matrix
        ax = axes[0, idx]
        im = ax.imshow(cm, interpolation='nearest', cmap='Blues')
        ax.set_title(f'Confusion Matrix\n{model_name}')
        ax.set_xlabel('Predicted Label')
        ax.set_ylabel('True Label')

        # Add text annotations
        for i in range(cm.shape[0]):
            for j in range(cm.shape[1]):
                ax.text(j, i, cm[i, j], ha="center", va="center",
                       color="white" if cm[i, j] > cm.max() / 2 else "black")

        ax.set_xticks(np.arange(cm.shape[1]))
        ax.set_yticks(np.arange(cm.shape[0]))

        # ROC Curves
        ax = axes[1, idx]

        if model_name in prob_scores:
            y_score = prob_scores[model_name]

            # Plot ROC curve for each class
            for cls in np.unique(y_test):
                try:
                    RocCurveDisplay.from_predictions(
                        (y_test == cls).astype(int),
                        y_score[:, cls],
                        ax=ax,
                        name=f"Class {cls}",
                        alpha=0.8
                    )
                except Exception as e:
                    logger.warning(f"Could not plot ROC for class {cls} in {model_name}: {e}")

            ax.plot([0, 1], [0, 1], linestyle="--", color='gray', alpha=0.8)
            ax.set_title(f'ROC Curves\n{model_name}')
            ax.grid(True, alpha=0.3)
        else:
            ax.text(0.5, 0.5, 'ROC data not available',
                   ha='center', va='center', transform=ax.transAxes)
            ax.set_title(f'ROC Curves\n{model_name}')

    plt.tight_layout()
    plt.savefig(results_dir / "best_models_analysis.png", dpi=300, bbox_inches='tight')
    plt.show()

    # Generate detailed classification report for best model
    best_model_name = results_df.index[0]
    best_model = models[best_model_name]
    y_pred_best = best_model.predict(X_test)

    print(f"\n{'='*60}")
    print(f"DETAILED ANALYSIS: {best_model_name}")
    print(f"{'='*60}")

    # Classification report
    report = classification_report(y_test, y_pred_best, output_dict=True)
    report_df = pd.DataFrame(report).transpose()
    print("\nClassification Report:")
    print(report_df.round(3))

    # Save classification report
    report_df.to_csv(results_dir / f"classification_report_{best_model_name.replace(' ', '_')}.csv")

    # Feature importance analysis (if available)
    if hasattr(best_model, 'feature_importances_'):
        feature_importance = pd.DataFrame({
            'feature': X.columns,
            'importance': best_model.feature_importances_
        }).sort_values('importance', ascending=False)

        print(f"\nTop 10 Feature Importances for {best_model_name}:")
        print(feature_importance.head(10))

        # Plot feature importance
        plt.figure(figsize=(10, 6))
        top_features = feature_importance.head(15)
        plt.barh(range(len(top_features)), top_features['importance'])
        plt.yticks(range(len(top_features)), top_features['feature'])
        plt.xlabel('Feature Importance')
        plt.title(f'Top 15 Feature Importances - {best_model_name}')
        plt.gca().invert_yaxis()
        plt.tight_layout()
        plt.savefig(results_dir / f"feature_importance_{best_model_name.replace(' ', '_')}.png",
                   dpi=300, bbox_inches='tight')
        plt.show()

        # Save feature importance
        feature_importance.to_csv(results_dir / f"feature_importance_{best_model_name.replace(' ', '_')}.csv",
                                 index=False)

    elif hasattr(best_model, 'named_steps') and hasattr(best_model.named_steps.get('clf'), 'feature_importances_'):
        # For pipeline models
        clf = best_model.named_steps['clf']
        feature_importance = pd.DataFrame({
            'feature': X.columns,
            'importance': clf.feature_importances_
        }).sort_values('importance', ascending=False)

        print(f"\nTop 10 Feature Importances for {best_model_name}:")
        print(feature_importance.head(10))

        feature_importance.to_csv(results_dir / f"feature_importance_{best_model_name.replace(' ', '_')}.csv",
                                 index=False)

    return best_model_name, report_df

# Perform detailed analysis
best_model_name, classification_report_df = analyze_best_models(
    results_df, models, prob_scores, X_test, y_test, results_dir
)

# ---------------------------------------------------------------------
# 6. Final Summary and Export
# ---------------------------------------------------------------------

# Create comprehensive summary
summary = {
    'experiment_info': {
        'dataset_shape': X.shape,
        'n_classes': len(np.unique(y)),
        'test_size': TEST_SIZE,
        'cv_folds': CV_FOLDS,
        'random_state': RANDOM_STATE
    },
    'best_model': {
        'name': best_model_name,
        'accuracy': float(results_df.loc[best_model_name, 'Accuracy']),
        'f1_score': float(results_df.loc[best_model_name, 'F1']),
        'roc_auc': float(results_df.loc[best_model_name, 'ROC AUC']),
        'training_time': float(results_df.loc[best_model_name, 'Training_Time'])
    },
    'top_5_models': results_df.head(5)[['Accuracy', 'F1', 'ROC AUC']].to_dict('index')
}

# Save comprehensive summary
save_model_config(summary, results_dir / "experiment_summary.json")

# Export all results
results_df.to_csv(results_dir / "final_model_metrics.csv", index_label="Model")

print(f"\n{'='*60}")
print("EXPERIMENT COMPLETED SUCCESSFULLY!")
print(f"{'='*60}")
print(f"Best Model: {best_model_name}")
print(f"Best Accuracy: {results_df.loc[best_model_name, 'Accuracy']:.3f}")
print(f"Best F1 Score: {results_df.loc[best_model_name, 'F1']:.3f}")
print(f"Best ROC AUC: {results_df.loc[best_model_name, 'ROC AUC']:.3f}")
print(f"\nAll results saved to: {results_dir}")
print(f"Total models evaluated: {len(models)}")

logger.info("Enhanced machine learning comparison completed successfully!")


