from Bio import Entrez
import csv # For CSV writing
import xml.etree.ElementTree as ET # For pretty printing XML (optional)

# --- Configuration ---
Entrez.email = "<EMAIL>"  # Replace with your actual email
Entrez.api_key = "d1dbd398bd51f753ba4f705376a639de6708" # Uncomment and replace if you have an API key

# --- Search Parameters ---
search_term = "(adaptive design OR Bayesian design) AND (clinical trial OR phase I OR phase II) AND (pharmacology OR therapeutics)"
max_results = 20 # Number of results to fetch (be mindful of NCBI limits)

# --- Filenames for Output ---
txt_filename = "pubmed_results.txt"
csv_filename = "pubmed_results.csv"
xml_filename = "pubmed_results_raw.xml"

print(f"Starting PubMed search for: {search_term}")

# --- 1. Perform PubMed Search (Get PMIDs) ---
try:
    handle_search = Entrez.esearch(db="pubmed", term=search_term, retmax=str(max_results))
    record_search = Entrez.read(handle_search)
    handle_search.close()
    pmids = record_search["IdList"]
    print(f"Found {len(pmids)} PMIDs.")
except Exception as e:
    print(f"An error occurred during ESearch: {e}")
    pmids = []

if pmids:
    # --- 2. Fetch Article Details for the PMIDs ---
    print("Fetching details for PMIDs...")
    try:
        handle_fetch = Entrez.efetch(db="pubmed", id=pmids, rettype="medline", retmode="xml")
        # Read the raw XML content first to save it before parsing
        raw_xml_data = handle_fetch.read()
        handle_fetch.close()

        # Re-open the raw XML data for parsing with Entrez.read
        # We need to encode it back to bytes for Entrez.read if it was decoded by read()
        if isinstance(raw_xml_data, str):
            raw_xml_data_bytes = raw_xml_data.encode('utf-8')
        else:
            raw_xml_data_bytes = raw_xml_data

        # --- 3. Save Raw XML Output ---
        print(f"Saving raw XML data to {xml_filename}...")
        with open(xml_filename, "wb") as f_xml: # Write in binary mode
            f_xml.write(raw_xml_data_bytes)
        print("Raw XML data saved.")

        # Now parse the XML data for other formats
        records = Entrez.read(raw_xml_data_bytes) # Use Entrez.read for parsed object

        # --- 4. Process and Save Results ---

        # --- Option A: Save to Plain Text File (.txt) ---
        print(f"Saving results to {txt_filename}...")
        with open(txt_filename, "w", encoding="utf-8") as f_txt:
            for i, pubmed_article in enumerate(records.get('PubmedArticle', [])):
                try:
                    f_txt.write(f"--- Record {i+1} ---\n")
                    article = pubmed_article.get('MedlineCitation', {}).get('Article', {})
                    pmid = pubmed_article.get('MedlineCitation', {}).get('PMID', {}).text
                    title = article.get('ArticleTitle', 'N/A')
                    if isinstance(title, ET.Element): # Handle cases where title might be an Element
                        title = "".join(title.itertext()).strip()

                    abstract_parts = article.get('Abstract', {}).get('AbstractText', [])
                    abstract = ""
                    if abstract_parts:
                        if isinstance(abstract_parts, list):
                            abstract = "\n".join([part.text if hasattr(part, 'text') else str(part) for part in abstract_parts if part is not None])
                        elif hasattr(abstract_parts, 'text'): # Single AbstractText element
                            abstract = abstract_parts.text
                        else: # String directly
                            abstract = str(abstract_parts)
                    else:
                        abstract = "N/A"

                    journal_info = article.get('Journal', {})
                    journal_title = journal_info.get('Title', 'N/A')
                    pub_date_node = article.get('Journal', {}).get('JournalIssue', {}).get('PubDate', {})
                    pub_year = pub_date_node.get('Year', pub_date_node.get('MedlineDate', 'N/A'))


                    f_txt.write(f"PMID: {pmid}\n")
                    f_txt.write(f"Title: {title}\n")
                    f_txt.write(f"Journal: {journal_title}\n")
                    f_txt.write(f"Year: {pub_year}\n")
                    f_txt.write(f"Abstract:\n{abstract}\n\n")
                except Exception as e:
                    f_txt.write(f"Error processing record {i+1}: {e}\n\n")
        print(f"Results saved to {txt_filename}")

        # --- Option B: Save to CSV File (.csv) ---
        print(f"Saving results to {csv_filename}...")
        csv_header = ['PMID', 'Title', 'Abstract', 'Authors', 'Journal', 'PublicationYear', 'DOI']
        with open(csv_filename, "w", newline="", encoding="utf-8") as f_csv:
            writer = csv.DictWriter(f_csv, fieldnames=csv_header)
            writer.writeheader()
            for pubmed_article in records.get('PubmedArticle', []):
                try:
                    row = {}
                    medline_citation = pubmed_article.get('MedlineCitation', {})
                    article = medline_citation.get('Article', {})

                    row['PMID'] = medline_citation.get('PMID', {}).text

                    title_element = article.get('ArticleTitle', None)
                    if title_element is not None:
                         row['Title'] = "".join(title_element.itertext()).strip() if isinstance(title_element, ET.Element) else str(title_element)
                    else:
                        row['Title'] = 'N/A'


                    abstract_parts = article.get('Abstract', {}).get('AbstractText', [])
                    abstract_text = ""
                    if abstract_parts:
                        if isinstance(abstract_parts, list):
                            abstract_text = "\n".join([part.text if hasattr(part, 'text') and part.text else str(part) for part in abstract_parts if part is not None])
                        elif hasattr(abstract_parts, 'text') and abstract_parts.text:
                            abstract_text = abstract_parts.text
                        else:
                             abstract_text = str(abstract_parts)
                    row['Abstract'] = abstract_text if abstract_text else "N/A"


                    author_list = article.get('AuthorList', [])
                    authors = []
                    if author_list: # Check if AuthorList is not None
                        for author in author_list:
                            if isinstance(author, dict): # Common structure
                                last_name = author.get('LastName', '')
                                fore_name = author.get('ForeName', '')
                                initials = author.get('Initials', '')
                                if last_name and fore_name:
                                    authors.append(f"{fore_name} {last_name}")
                                elif last_name and initials:
                                    authors.append(f"{initials} {last_name}")
                                elif last_name:
                                    authors.append(last_name)
                    row['Authors'] = "; ".join(authors) if authors else "N/A"

                    journal_info = article.get('Journal', {})
                    row['Journal'] = journal_info.get('Title', 'N/A')

                    pub_date_node = journal_info.get('JournalIssue', {}).get('PubDate', {})
                    row['PublicationYear'] = pub_date_node.get('Year', pub_date_node.get('MedlineDate', 'N/A'))

                    # Extract DOI (can be in multiple places)
                    doi = "N/A"
                    article_ids = article.get('ELocationID', []) # ELocationID can be a list
                    if not isinstance(article_ids, list): # Sometimes it might not be a list if only one
                        article_ids = [article_ids]

                    for aid in article_ids:
                        if hasattr(aid, 'attributes') and aid.attributes.get('EIdType', '').lower() == 'doi':
                            doi = aid.text
                            break
                    if doi == "N/A": # Fallback to ArticleIdList
                        article_id_list = medline_citation.get('Article', {}).get('ArticleIdList', [])
                        for article_id_element in article_id_list:
                            if hasattr(article_id_element, 'attributes') and article_id_element.attributes.get('IdType') == 'doi':
                                doi = article_id_element.text
                                break
                    row['DOI'] = doi

                    writer.writerow(row)
                except Exception as e:
                    print(f"Error processing record for CSV (PMID: {row.get('PMID', 'Unknown')}): {e}")
                    # Optionally write a row with error info or skip
                    writer.writerow({'PMID': row.get('PMID', 'Error processing'), 'Title': str(e), 'Abstract': '', 'Authors': '', 'Journal': '', 'PublicationYear': '', 'DOI': ''})

        print(f"Results saved to {csv_filename}")

    except Exception as e:
        print(f"An error occurred during EFetch or file writing: {e}")
else:
    print("No PMIDs found to fetch details for.")

print("Script finished.")