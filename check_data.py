import pandas as pd
import numpy as np

# Load and examine the datasets
df_major = pd.read_csv('data_diag.csv')
df_subgroup = pd.read_csv('data_diag_maj_sub.csv')

print('=== DATASET ANALYSIS ===')
print('Major Categories Dataset:')
print(f'  Shape: {df_major.shape}')
print(f'  Unique classes: {sorted(df_major["Diagnosis"].unique())}')
print(f'  Class counts: {dict(df_major["Diagnosis"].value_counts().sort_index())}')

print('\nSubgroup Classifications Dataset:')
print(f'  Shape: {df_subgroup.shape}')
print(f'  Unique classes: {sorted(df_subgroup["Diagnosis"].unique())}')
print(f'  Class counts: {dict(df_subgroup["Diagnosis"].value_counts().sort_index())}')

# Check if features are identical
X_major = df_major.drop('Diagnosis', axis=1)
X_subgroup = df_subgroup.drop('Diagnosis', axis=1)
print(f'\nFeature data identical: {X_major.equals(X_subgroup)}')

# Test a simple model with correct 3-class setup for major categories
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, roc_auc_score
from sklearn.preprocessing import StandardScaler

# Major categories (should have 3 classes: 0, 1, 2)
X = df_major.drop('Diagnosis', axis=1)
y = df_major['Diagnosis']

print(f'\n=== MAJOR CATEGORIES VALIDATION ===')
print(f'Unique y values: {sorted(y.unique())}')
print(f'Expected classes: [0, 1, 2]')
print(f'Correct class count: {len(y.unique()) == 3}')

# Quick test
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
rf = RandomForestClassifier(n_estimators=50, random_state=42)
rf.fit(X_train, y_train)

y_pred = rf.predict(X_test)
y_proba = rf.predict_proba(X_test)

print(f'Test set classes: {sorted(y_test.unique())}')
print(f'Prediction proba shape: {y_proba.shape}')
print(f'Expected proba shape: ({len(y_test)}, 3)')
print(f'Shape match: {y_proba.shape[1] == len(y.unique())}')

# Test AUC calculation
try:
    auc = roc_auc_score(y_test, y_proba, multi_class='ovr', average='macro')
    print(f'AUC calculation successful: {auc:.4f}')
except Exception as e:
    print(f'AUC calculation failed: {e}')
