"""
Extracted Code from Technical Report
Generated: June 07, 2025 at 07:45 AM

This file contains all code blocks extracted from the Technical_Report.md file.
Each code block is separated and labeled for reference.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import roc_auc_score, classification_report, confusion_matrix
import xgboost as xgb
from catboost import CatBoostClassifier
import shap
from scipy import stats

# ============================================================================
# EXTRACTED CODE BLOCKS
# ============================================================================


# ============================================================================
# CODE BLOCK 1
# ============================================================================

# Load datasets with comprehensive validation
df_major = pd.read_csv('data_diag.csv')
df_subgroup = pd.read_csv('data_diag_maj_sub.csv')

# Verify data integrity
assert df_major.shape[0] == df_subgroup.shape[0]  # Same sample size
assert df_major.isnull().sum().sum() == 0  # No missing values
assert df_subgroup.isnull().sum().sum() == 0  # No missing values


# ============================================================================
# CODE BLOCK 2
# ============================================================================

# Separate features and targets for both datasets
X_major = df_major.drop('Diagnosis', axis=1)
y_major = df_major['Diagnosis']
X_subgroup = df_subgroup.drop('Diagnosis', axis=1)
y_subgroup = df_subgroup['Diagnosis']

# Verify feature consistency
assert X_major.equals(X_subgroup)  # Identical feature data


# ============================================================================
# CODE BLOCK 3
# ============================================================================

# Stratified splitting to maintain class proportions
X_train_maj, X_test_maj, y_train_maj, y_test_maj = train_test_split(
    X_major, y_major, test_size=0.2, random_state=42, stratify=y_major
)

X_train_sub, X_test_sub, y_train_sub, y_test_sub = train_test_split(
    X_subgroup, y_subgroup, test_size=0.2, random_state=42, stratify=y_subgroup
)


# ============================================================================
# CODE BLOCK 4
# ============================================================================

# StandardScaler for linear models
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# Preserve original data for tree-based models
X_train_original = X_train.copy()
X_test_original = X_test.copy()


# ============================================================================
# CODE BLOCK 5
# ============================================================================

def compute_statistical_features(X, cell_type_prefix):
    """
    Compute statistical features for a specific cell type
    
    Parameters:
    X: DataFrame containing cell population data
    cell_type_prefix: String ('NE', 'LY', 'MO')
    
    Returns:
    Dictionary of statistical features
    """
    # Identify features for this cell type
    features = [col for col in X.columns if col.startswith(cell_type_prefix)]
    
    # Compute statistical summaries
    stats = {
        f'{cell_type_prefix}_mean': X[features].mean(axis=1),
        f'{cell_type_prefix}_std': X[features].std(axis=1),
        f'{cell_type_prefix}_max': X[features].max(axis=1),
        f'{cell_type_prefix}_min': X[features].min(axis=1),
        f'{cell_type_prefix}_range': X[features].max(axis=1) - X[features].min(axis=1),
        f'{cell_type_prefix}_cv': X[features].std(axis=1) / (X[features].mean(axis=1) + 1e-8)
    }
    
    return stats


# ============================================================================
# CODE BLOCK 6
# ============================================================================

def compute_relational_features(X_eng):
    """
    Compute relational features between cell types
    
    Parameters:
    X_eng: DataFrame with statistical features already computed
    
    Returns:
    Updated DataFrame with relational features
    """
    # Neutrophil to Lymphocyte ratio
    X_eng['NE_LY_ratio'] = X_eng['NE_mean'] / (X_eng['LY_mean'] + 1e-8)
    
    # Neutrophil to Monocyte ratio
    X_eng['NE_MO_ratio'] = X_eng['NE_mean'] / (X_eng['MO_mean'] + 1e-8)
    
    # Lymphocyte to Monocyte ratio
    X_eng['LY_MO_ratio'] = X_eng['LY_mean'] / (X_eng['MO_mean'] + 1e-8)
    
    return X_eng


# ============================================================================
# CODE BLOCK 7
# ============================================================================

def compute_geometric_features(X):
    """
    Compute geometric features from positional coordinates
    
    Parameters:
    X: DataFrame containing original cell population data
    
    Returns:
    Dictionary of geometric features
    """
    geometric_features = {}
    
    # Compute magnitude for each cell type
    for cell_type in ['NE', 'LY', 'MO']:
        x_col = f'{cell_type}X'
        y_col = f'{cell_type}Y'
        z_col = f'{cell_type}Z'
        
        magnitude = np.sqrt(X[x_col]**2 + X[y_col]**2 + X[z_col]**2)
        geometric_features[f'{cell_type}_magnitude'] = magnitude
    
    return geometric_features


# ============================================================================
# CODE BLOCK 8
# ============================================================================

def enhanced_feature_engineering(X):
    """
    Complete feature engineering pipeline
    
    Parameters:
    X: Original DataFrame with 18 cell population parameters
    
    Returns:
    Enhanced DataFrame with engineered features
    """
    X_eng = X.copy()
    
    # Statistical features for each cell type
    for cell_type in ['NE', 'LY', 'MO']:
        stats = compute_statistical_features(X, cell_type)
        for feature_name, feature_values in stats.items():
            X_eng[feature_name] = feature_values
    
    # Relational features
    X_eng = compute_relational_features(X_eng)
    
    # Geometric features
    geometric = compute_geometric_features(X)
    for feature_name, feature_values in geometric.items():
        X_eng[feature_name] = feature_values
    
    return X_eng


# ============================================================================
# CODE BLOCK 9
# ============================================================================

class RandomForestConfig:
    """Configuration for Random Forest classifier"""
    def __init__(self):
        self.n_estimators = 100
        self.max_depth = None
        self.min_samples_split = 2
        self.min_samples_leaf = 1
        self.max_features = 'sqrt'
        self.bootstrap = True
        self.random_state = 42
        self.n_jobs = -1

def train_random_forest(X_train, y_train, config):
    """
    Train Random Forest classifier with specified configuration
    
    Parameters:
    X_train: Training features
    y_train: Training labels
    config: RandomForestConfig object
    
    Returns:
    Trained Random Forest model
    """
    rf_model = RandomForestClassifier(
        n_estimators=config.n_estimators,
        max_depth=config.max_depth,
        min_samples_split=config.min_samples_split,
        min_samples_leaf=config.min_samples_leaf,
        max_features=config.max_features,
        bootstrap=config.bootstrap,
        random_state=config.random_state,
        n_jobs=config.n_jobs
    )
    
    rf_model.fit(X_train, y_train)
    return rf_model


# ============================================================================
# CODE BLOCK 10
# ============================================================================

class XGBoostConfig:
    """Configuration for XGBoost classifier"""
    def __init__(self):
        self.n_estimators = 100
        self.max_depth = 6
        self.learning_rate = 0.1
        self.subsample = 0.8
        self.colsample_bytree = 0.8
        self.reg_alpha = 0.1
        self.reg_lambda = 1.0
        self.random_state = 42
        self.eval_metric = 'mlogloss'

def train_xgboost(X_train, y_train, config):
    """
    Train XGBoost classifier with specified configuration
    
    Parameters:
    X_train: Training features
    y_train: Training labels
    config: XGBoostConfig object
    
    Returns:
    Trained XGBoost model
    """
    xgb_model = xgb.XGBClassifier(
        n_estimators=config.n_estimators,
        max_depth=config.max_depth,
        learning_rate=config.learning_rate,
        subsample=config.subsample,
        colsample_bytree=config.colsample_bytree,
        reg_alpha=config.reg_alpha,
        reg_lambda=config.reg_lambda,
        random_state=config.random_state,
        eval_metric=config.eval_metric
    )
    
    xgb_model.fit(X_train, y_train)
    return xgb_model


# ============================================================================
# CODE BLOCK 11
# ============================================================================

class CatBoostConfig:
    """Configuration for CatBoost classifier"""
    def __init__(self):
        self.iterations = 100
        self.depth = 6
        self.learning_rate = 0.1
        self.l2_leaf_reg = 3.0
        self.random_state = 42
        self.verbose = False

def train_catboost(X_train, y_train, config):
    """
    Train CatBoost classifier with specified configuration
    
    Parameters:
    X_train: Training features
    y_train: Training labels
    config: CatBoostConfig object
    
    Returns:
    Trained CatBoost model
    """
    cb_model = CatBoostClassifier(
        iterations=config.iterations,
        depth=config.depth,
        learning_rate=config.learning_rate,
        l2_leaf_reg=config.l2_leaf_reg,
        random_state=config.random_state,
        verbose=config.verbose
    )
    
    cb_model.fit(X_train, y_train)
    return cb_model


# ============================================================================
# CODE BLOCK 12
# ============================================================================

class LogisticRegressionConfig:
    """Configuration for Logistic Regression classifier"""
    def __init__(self):
        self.C = 1.0
        self.penalty = 'l2'
        self.solver = 'liblinear'
        self.max_iter = 1000
        self.random_state = 42

def train_logistic_regression(X_train, y_train, config):
    """
    Train Logistic Regression classifier with specified configuration
    
    Parameters:
    X_train: Training features (should be scaled)
    y_train: Training labels
    config: LogisticRegressionConfig object
    
    Returns:
    Trained Logistic Regression model
    """
    lr_model = LogisticRegression(
        C=config.C,
        penalty=config.penalty,
        solver=config.solver,
        max_iter=config.max_iter,
        random_state=config.random_state
    )
    
    lr_model.fit(X_train, y_train)
    return lr_model


# ============================================================================
# CODE BLOCK 13
# ============================================================================

class SVMConfig:
    """Configuration for Support Vector Machine classifier"""
    def __init__(self):
        self.C = 1.0
        self.kernel = 'rbf'
        self.gamma = 'scale'
        self.probability = True
        self.random_state = 42

def train_svm(X_train, y_train, config):
    """
    Train SVM classifier with specified configuration
    
    Parameters:
    X_train: Training features (should be scaled)
    y_train: Training labels
    config: SVMConfig object
    
    Returns:
    Trained SVM model
    """
    svm_model = SVC(
        C=config.C,
        kernel=config.kernel,
        gamma=config.gamma,
        probability=config.probability,
        random_state=config.random_state
    )
    
    svm_model.fit(X_train, y_train)
    return svm_model


# ============================================================================
# CODE BLOCK 14
# ============================================================================

def train_all_models(X_train, X_train_scaled, y_train):
    """
    Train all models with appropriate data preprocessing
    
    Parameters:
    X_train: Original training features
    X_train_scaled: Scaled training features
    y_train: Training labels
    
    Returns:
    Dictionary of trained models
    """
    models = {}
    
    # Tree-based models (use original features)
    models['Random Forest'] = train_random_forest(X_train, y_train, RandomForestConfig())
    models['XGBoost'] = train_xgboost(X_train, y_train, XGBoostConfig())
    models['CatBoost'] = train_catboost(X_train, y_train, CatBoostConfig())
    models['Gradient Boosting'] = train_gradient_boosting(X_train, y_train, GradientBoostingConfig())
    
    # Linear models (use scaled features)
    models['Logistic Regression'] = train_logistic_regression(X_train_scaled, y_train, LogisticRegressionConfig())
    models['SVM'] = train_svm(X_train_scaled, y_train, SVMConfig())
    
    return models


# ============================================================================
# CODE BLOCK 15
# ============================================================================

def perform_cross_validation(models, X_train, X_train_scaled, y_train, cv_folds=5):
    """
    Perform stratified cross-validation for all models
    
    Parameters:
    models: Dictionary of trained models
    X_train: Original training features
    X_train_scaled: Scaled training features
    y_train: Training labels
    cv_folds: Number of cross-validation folds
    
    Returns:
    Dictionary of cross-validation results
    """
    cv_results = {}
    skf = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)
    
    for model_name, model in models.items():
        # Select appropriate feature set
        X_cv = X_train_scaled if model_name in ['Logistic Regression', 'SVM'] else X_train
        
        # Perform cross-validation
        cv_scores = cross_val_score(model, X_cv, y_train, cv=skf, scoring='accuracy')
        
        cv_results[model_name] = {
            'mean_accuracy': cv_scores.mean(),
            'std_accuracy': cv_scores.std(),
            'individual_scores': cv_scores
        }
    
    return cv_results


# ============================================================================
# CODE BLOCK 16
# ============================================================================

def calculate_auc_ci(y_true, y_scores, confidence=0.95, n_bootstrap=1000):
    """
    Calculate bootstrap confidence intervals for AUC using One-vs-Rest approach
    
    Parameters:
    y_true: True class labels
    y_scores: Predicted class probabilities
    confidence: Confidence level (default 0.95 for 95% CI)
    n_bootstrap: Number of bootstrap samples
    
    Returns:
    Tuple of (lower_ci, upper_ci, bootstrap_aucs)
    """
    def auc_statistic(y_true, y_scores):
        """Calculate AUC using One-vs-Rest multi-class approach"""
        return roc_auc_score(y_true, y_scores, multi_class='ovr', average='macro')
    
    # Initialize bootstrap results
    n_samples = len(y_true)
    bootstrap_aucs = []
    
    # Set random seed for reproducibility
    np.random.seed(42)
    
    # Generate bootstrap samples
    for iteration in range(n_bootstrap):
        # Bootstrap sample indices with replacement
        indices = np.random.choice(n_samples, size=n_samples, replace=True)
        y_true_boot = y_true[indices]
        y_scores_boot = y_scores[indices]
        
        try:
            # Calculate AUC for bootstrap sample
            auc_boot = auc_statistic(y_true_boot, y_scores_boot)
            bootstrap_aucs.append(auc_boot)
        except ValueError:
            # Handle cases where bootstrap sample lacks class diversity
            continue
    
    # Calculate confidence interval using percentile method
    alpha = 1 - confidence
    lower_percentile = (alpha/2) * 100
    upper_percentile = (1 - alpha/2) * 100
    
    ci_lower = np.percentile(bootstrap_aucs, lower_percentile)
    ci_upper = np.percentile(bootstrap_aucs, upper_percentile)
    
    return ci_lower, ci_upper, bootstrap_aucs


# ============================================================================
# CODE BLOCK 17
# ============================================================================

def calculate_multiclass_auc_detailed(y_true, y_scores, class_names):
    """
    Calculate detailed multi-class AUC metrics
    
    Parameters:
    y_true: True class labels
    y_scores: Predicted class probabilities (n_samples x n_classes)
    class_names: List of class names for interpretation
    
    Returns:
    Dictionary containing detailed AUC metrics
    """
    n_classes = len(class_names)
    auc_results = {}
    
    # Calculate AUC for each class vs rest
    for i, class_name in enumerate(class_names):
        # Create binary labels (current class vs all others)
        y_binary = (y_true == i).astype(int)
        y_scores_binary = y_scores[:, i]
        
        # Calculate binary AUC
        try:
            auc_binary = roc_auc_score(y_binary, y_scores_binary)
            auc_results[f'AUC_class_{class_name}'] = auc_binary
        except ValueError:
            auc_results[f'AUC_class_{class_name}'] = np.nan
    
    # Calculate macro averaged AUC
    valid_aucs = [auc for auc in auc_results.values() if not np.isnan(auc)]
    auc_results['AUC_macro'] = np.mean(valid_aucs) if valid_aucs else np.nan
    
    # Calculate overall multi-class AUC using sklearn
    try:
        auc_results['AUC_ovr_macro'] = roc_auc_score(y_true, y_scores, 
                                                    multi_class='ovr', 
                                                    average='macro')
    except ValueError:
        auc_results['AUC_ovr_macro'] = np.nan
    
    return auc_results


# ============================================================================
# CODE BLOCK 18
# ============================================================================

def compare_model_performance(results_dict, metric='accuracy', alpha=0.05):
    """
    Compare model performance using paired t-tests
    
    Parameters:
    results_dict: Dictionary containing cross-validation results for each model
    metric: Performance metric to compare
    alpha: Significance level
    
    Returns:
    Dictionary containing pairwise comparison results
    """
    from scipy.stats import ttest_rel
    
    model_names = list(results_dict.keys())
    comparison_results = {}
    
    for i, model1 in enumerate(model_names):
        for j, model2 in enumerate(model_names[i+1:], i+1):
            # Extract cross-validation scores
            scores1 = results_dict[model1]['individual_scores']
            scores2 = results_dict[model2]['individual_scores']
            
            # Perform paired t-test
            t_statistic, p_value = ttest_rel(scores1, scores2)
            
            # Determine significance
            is_significant = p_value < alpha
            
            comparison_results[f'{model1}_vs_{model2}'] = {
                't_statistic': t_statistic,
                'p_value': p_value,
                'is_significant': is_significant,
                'mean_difference': np.mean(scores1) - np.mean(scores2)
            }
    
    return comparison_results


# ============================================================================
# CODE BLOCK 19
# ============================================================================

def calculate_effect_size(scores1, scores2):
    """
    Calculate Cohen's d effect size between two sets of scores
    
    Parameters:
    scores1, scores2: Arrays of performance scores
    
    Returns:
    Cohen's d effect size
    """
    # Calculate means
    mean1, mean2 = np.mean(scores1), np.mean(scores2)
    
    # Calculate pooled standard deviation
    n1, n2 = len(scores1), len(scores2)
    pooled_std = np.sqrt(((n1-1)*np.var(scores1, ddof=1) + (n2-1)*np.var(scores2, ddof=1)) / (n1+n2-2))
    
    # Calculate Cohen's d
    cohens_d = (mean1 - mean2) / pooled_std
    
    return cohens_d

def interpret_effect_size(cohens_d):
    """
    Interpret Cohen's d effect size magnitude
    
    Parameters:
    cohens_d: Cohen's d value
    
    Returns:
    String interpretation of effect size
    """
    abs_d = abs(cohens_d)
    
    if abs_d < 0.2:
        return "negligible"
    elif abs_d < 0.5:
        return "small"
    elif abs_d < 0.8:
        return "medium"
    else:
        return "large"


# ============================================================================
# CODE BLOCK 20
# ============================================================================

def plot_auc_with_confidence_intervals(results_df, dataset_name, output_dir):
    """
    Create visualization of AUC values with confidence intervals
    
    Parameters:
    results_df: DataFrame containing model results with CI information
    dataset_name: Name of dataset for plot title
    output_dir: Directory for saving plots
    """
    plt.figure(figsize=(12, 8))
    
    # Extract data for plotting
    models = results_df['model_name']
    aucs = results_df['auc']
    ci_lower = results_df['auc_ci_lower']
    ci_upper = results_df['auc_ci_upper']
    
    # Calculate error bars
    error_lower = aucs - ci_lower
    error nearly_upper = ci_upper - aucs
    
    # Create bar plot with error bars
    bars = plt.bar(range(len(models)), aucs, alpha=0.7, color='skyblue')
    plt.errorbar(range(len(models)), aucs, 
                yerr=[error_lower, error_upper],
                fmt='none', color='black', capsize=5, capthick=2)
    
    # Customize plot
    plt.xlabel('Machine Learning Models')
    plt.ylabel('AUC (Area Under Curve)')
    plt.title(f'Model Performance with 95% Confidence Intervals\nDataset: {dataset_name}')
    plt.xticks(range(len(models)), models, rotation=45, ha='right')
    plt.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for i, (bar, auc, ci_l, ci_u) in enumerate(zip(bars, aucs, ci_lower, ci_upper)):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{auc:.3f}\n[{ci_l:.3f}, {ci_u:.3f}]',
                ha='center', va='bottom', fontsize=9)
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/{dataset_name}_auc_confidence_intervals.png', 
               dpi=300, bbox_inches='tight')
    plt.close()


# ============================================================================
# CODE BLOCK 21
# ============================================================================

def analyze_bootstrap_distributions(bootstrap_results, model_name, output_dir):
    """
    Analyze and visualize bootstrap AUC distributions
    
    Parameters:
    bootstrap_results: Array of bootstrap AUC values
    model_name: Name of the model for labeling
    output_dir: Directory for saving analysis results
    """
    if len(bootstrap_results) == 0:
        return None
    
    # Calculate distribution statistics
    mean_auc = np.mean(bootstrap_results)
    std_auc = np.std(bootstrap_results)
    median_auc = np.median(bootstrap_results)
    
    # Calculate percentiles
    percentiles = [2.5, 5, 10, 25, 75, 90, 95, 97.5]
    percentile_values = np.percentile(bootstrap_results, percentiles)
    
    # Create distribution plot
    plt.figure(figsize=(10, 6))
    plt.hist(bootstrap_results, bins=50, alpha=0.7, color='skyblue', 
             edgecolor='black', density=True)
    
    # Add statistical lines
    plt.axvline(mean_auc, color='red', linestyle='--', linewidth=2, 
                label=f'Mean: {mean_auc:.4f}')
    plt.axvline(median_auc, color='green', linestyle='--', linewidth=2, 
                label=f'Median: {median_auc:.4f}')
    plt.axvline(percentile_values[0], color='orange', linestyle='--', 
                label=f'2.5th percentile: {percentile_values[0]:.4f}')
    plt.axvline(percentile_values[-1], color='orange', linestyle='--', 
                label=f'97.5th percentile: {percentile_values[-1]:.4f}')
    
    plt.xlabel('AUC Value')
    plt.ylabel('Density')
    plt.title(f'Bootstrap Distribution of AUC - {model_name}')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/{model_name.lower().replace(" ", "_")}_bootstrap_distribution.png', 
               dpi=300, bbox_inches='tight')
    plt.close()
    
    # Return summary statistics
    return {
        'mean': mean_auc,
        'std': std_auc,
        'median': median_auc,
        'percentiles': dict(zip(percentiles, percentile_values))
    }


# ============================================================================
# CODE BLOCK 22
# ============================================================================

def perform_shap_analysis_tree(model, X_train, X_test, feature_names, model_name, output_dir):
    """
    Perform SHAP analysis for tree-based models
    
    Parameters:
    model: Trained tree-based model (RandomForest, XGBoost)
    X_train: Training features for background distribution
    X_test: Test features for explanation
    feature_names: List of feature names
    model_name: Name of the model for labeling
    output_dir: Directory for saving visualizations
    
    Returns:
    SHAP values and explainer object
    """
    try:
        # Create TreeExplainer for efficient computation
        explainer = shap.TreeExplainer(model)
        
        # Calculate SHAP values for test set
        shap_values = explainer.shap_values(X_test)
        
        # Handle multi-class output format
        if isinstance(shap_values, list):
            # For multi-class, use the first class or average across classes
            shap_values_plot = shap_values[0] if len(shap_values) > 1 else shap_values
        else:
            shap_values_plot = shap_values
        
        # Generate summary plot
        plt.figure(figsize=(12, 8))
        shap.summary_plot(shap_values_plot, X_test, feature_names=feature_names, show=False)
        plt.title(f'SHAP Summary Plot - {model_name}')
        plt.tight_layout()
        plt.savefig(f'{output_dir}/shap_summary_{model_name.lower().replace(" ", "_")}.png', 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        # Generate feature importance plot
        plt.figure(figsize=(10, 8))
        feature_importance = np.mean(np.abs(shap_values_plot), axis=0)
        feature_df = pd.DataFrame({
            'feature': feature_names,
            'importance': feature_importance
        }).sort_values('importance', ascending=True)
        
        plt.barh(range(len(feature_df)), feature_df['importance'])
        plt.yticks(range(len(feature_df)), feature_df['feature'])
        plt.xlabel('Mean |SHAP Value|')
        plt.title(f'SHAP Feature Importance - {model_name}')
        plt.tight_layout()
        plt.savefig(f'{output_dir}/shap_importance_{model_name.lower().replace(" ", "_")}.png', 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        return shap_values, explainer
        
    except Exception as e:
        print(f"SHAP analysis failed for {model_name}: {str(e)}")
        return None, None


# ============================================================================
# CODE BLOCK 23
# ============================================================================

def perform_shap_analysis_linear(model, X_train, X_test, feature_names, model_name, output_dir):
    """
    Perform SHAP analysis for linear models
    
    Parameters:
    model: Trained linear model (LogisticRegression)
    X_train: Training features for background distribution
    X_test: Test features for explanation
    feature_names: List of feature names
    model_name: Name of the model for labeling
    output_dir: Directory for saving visualizations
    
    Returns:
    SHAP values and explainer object
    """
    try:
        # Create LinearExplainer
        explainer = shap.LinearExplainer(model, X_train)
        
        # Calculate SHAP values
        shap_values = explainer.shap_values(X_test)
        
        # Handle multi-class output
        if isinstance(shap_values, list):
            shap_values_plot = shap_values[0]
        else:
            shap_values_plot = shap_values
        
        # Generate waterfall plot for first prediction
        if len(X_test) > 0:
            plt.figure(figsize=(10, 8))
            shap.waterfall_plot(explainer.expected_value[0] if isinstance(explainer.expected_value, np.ndarray) else explainer.expected_value,
                               shap_values_plot[0], X_test.iloc[0], feature_names=feature_names, show=False)
            plt.title(f'SHAP Waterfall Plot - {model_name} (First Prediction)')
            plt.tight_layout()
            plt.savefig(f'{output_dir}/shap_waterfall_{model_name.lower().replace(" ", "_")}.png', 
                       dpi=300, bbox_inches='tight')
            plt.close()
        
        return shap_values, explainer
        
    except Exception as e:
        print(f"SHAP analysis failed for {model_name}: {str(e)}")
        return None, None


# ============================================================================
# CODE BLOCK 24
# ============================================================================

def analyze_feature_importance(shap_values, feature_names, model_name, top_n=20):
    """
    Analyze and rank feature importance using SHAP values
    
    Parameters:
    shap_values: SHAP values array
    feature_names: List of feature names
    model_name: Name of the model
    top_n: Number of top features to analyze
    
    Returns:
    DataFrame with feature importance analysis
    """
    # Calculate mean absolute SHAP values
    if isinstance(shap_values, list):
        # For multi-class, average across classes
        mean_shap = np.mean([np.mean(np.abs(sv), axis=0) for sv in shap_values], axis=0)
    else:
        mean_shap = np.mean(np.abs(shap_values), axis=0)
    
    # Create feature importance DataFrame
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'mean_abs_shap': mean_shap,
        'importance_rank': range(1, len(feature_names) + 1)
    }).sort_values('mean_abs_shap', ascending=False).reset_index(drop=True)
    
    # Update ranks
    importance_df['importance_rank'] = range(1, len(importance_df) + 1)
    
    # Add feature categories
    def categorize_feature(feature_name):
        if feature_name.startswith('NE'):
            return 'Neutrophil'
        elif feature_name.startswith('LY'):
            return 'Lymphocyte'
        elif feature_name.startswith('MO'):
            return 'Monocyte'
        elif 'ratio' in feature_name.lower():
            return 'Ratio'
        elif 'magnitude' in feature_name.lower():
            return 'Geometric'
        else:
            return 'Statistical'
    
    importance_df['category'] = importance_df['feature'].apply(categorize_feature)
    
    # Calculate category-wise importance
    category_importance = importance_df.groupby('category')['mean_abs_shap'].sum().sort_values(ascending=False)
    
    print(f"\nFeature Importance Analysis - {model_name}")
    print("="*50)
    print(f"Top {top_n} Most Important Features:")
    print(importance_df.head(top_n)[['feature', 'mean_abs_shap', 'category']].to_string(index=False))
    
    print(f"\nCategory-wise Importance:")
    for category, importance in category_importance.items():
        print(f"{category}: {importance:.4f}")
    
    return importance_df, category_importance


# ============================================================================
# CODE BLOCK 25
# ============================================================================

def analyze_shap_interactions(model, X_test, feature_names, model_name, output_dir, max_features=10):
    """
    Analyze SHAP feature interactions
    
    Parameters:
    model: Trained model
    X_test: Test features
    feature_names: List of feature names
    model_name: Name of the model
    output_dir: Directory for saving visualizations
    max_features: Maximum number of features for interaction analysis
    
    Returns:
    Interaction values matrix
    """
    try:
        # Limit features for computational efficiency
        if len(feature_names) > max_features:
            # Select top features based on individual importance
            explainer = shap.TreeExplainer(model) if hasattr(model, 'estimators_') else shap.KernelExplainer(model.predict_proba, X_test[:50])
            shap_values = explainer.shap_values(X_test[:100])
            
            if isinstance(shap_values, list):
                importance = np.mean([np.mean(np.abs(sv), axis=0) for sv in shap_values], axis=0)
            else:
                importance = np.mean(np.abs(shap_values), axis=0)
            
            top_indices = np.argsort(importance)[-max_features:]
            X_test_subset = X_test.iloc[:, top_indices]
            feature_names_subset = [feature_names[i] for i in top_indices]
        else:
            X_test_subset = X_test
            feature_names_subset = feature_names
        
        # Calculate interaction values
        explainer = shap.TreeExplainer(model)
        shap_interaction_values = explainer.shap_interaction_values(X_test_subset[:50])  # Limit samples for efficiency
        
        # Create interaction heatmap
        if shap_interaction_values is not None:
            # Average interaction values across samples
            mean_interactions = np.mean(np.abs(shap_interaction_values), axis=0)
            
            plt.figure(figsize=(12, 10))
            sns.heatmap(mean_interactions, 
                       xticklabels=feature_names_subset,
                       yticklabels=feature_names_subset,
                       annot=False, cmap='RdBu_r', center=0)
            plt.title(f'SHAP Feature Interactions - {model_name}')
            plt.xticks(rotation=45, ha='right')
            plt.yticks(rotation=0)
            plt.tight_layout()
            plt.savefig(f'{output_dir}/shap_interactions_{model_name.lower().replace(" ", "_")}.png', 
                       dpi=300, bbox_inches='tight')
            plt.close()
            
            return mean_interactions
        
    except Exception as e:
        print(f"SHAP interaction analysis failed for {model_name}: {str(e)}")
        return None


# ============================================================================
# CODE BLOCK 26
# ============================================================================

def interpret_shap_results(importance_df, category_importance, model_name):
    """
    Provide clinical interpretation of SHAP results
    
    Parameters:
    importance_df: DataFrame with feature importance rankings
    category_importance: Series with category-wise importance
    model_name: Name of the model
    
    Returns:
    Dictionary with clinical interpretations
    """
    interpretations = {
        'model_name': model_name,
        'top_features': [],
        'category_insights': {},
        'clinical_implications': []
    }
    
    # Interpret top features
    for _, row in importance_df.head(10).iterrows():
        feature = row['feature']
        importance = row['mean_abs_shap']
        category = row['category']
        
        # Generate feature-specific interpretation
        if 'NEY' in feature:
            interpretation = "Neutrophil Y-coordinate: Critical for distinguishing blast cells from mature neutrophils"
        elif 'mean' in feature and 'NE' in feature:
            interpretation = "Neutrophil mean parameters: Reflect overall population characteristics altered in acute leukemia"
        # Additional interpretations...
    
    # Category insights
    for category, importance in category_importance.items():
        if category == 'Neutrophil':
            interpretations['category_insights'][category] = "Dominant role in diagnosis, reflecting myeloid lineage involvement"
        # Additional category insights...
    
    return interpretations

