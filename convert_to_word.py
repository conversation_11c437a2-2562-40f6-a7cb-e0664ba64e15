#!/usr/bin/env python3
"""
Convert executed <PERSON><PERSON><PERSON> notebook to Word document
"""

import json
import base64
import io
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
import matplotlib.pyplot as plt
from PIL import Image

def convert_notebook_to_word(notebook_path, output_path):
    """
    Convert a Jupyter notebook to a Word document
    """
    # Load the notebook
    with open(notebook_path, 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    # Create a new Word document
    doc = Document()
    
    # Add title
    title = doc.add_heading('Comprehensive Machine Learning Analysis of Cell Population Data for Acute Leukemia Diagnosis', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Process each cell
    for cell in notebook['cells']:
        if cell['cell_type'] == 'markdown':
            # Process markdown cells
            source = ''.join(cell['source'])
            
            # Handle headers
            lines = source.split('\n')
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                if line.startswith('# '):
                    doc.add_heading(line[2:], level=1)
                elif line.startswith('## '):
                    doc.add_heading(line[3:], level=2)
                elif line.startswith('### '):
                    doc.add_heading(line[4:], level=3)
                elif line.startswith('#### '):
                    doc.add_heading(line[5:], level=4)
                elif line.startswith('**') and line.endswith('**'):
                    # Bold text
                    p = doc.add_paragraph()
                    run = p.add_run(line[2:-2])
                    run.bold = True
                elif line.startswith('- '):
                    # Bullet point
                    doc.add_paragraph(line[2:], style='List Bullet')
                elif line.startswith('1. ') or line.startswith('2. ') or line.startswith('3. '):
                    # Numbered list
                    doc.add_paragraph(line[3:], style='List Number')
                else:
                    # Regular paragraph
                    if line and not line.startswith('---'):
                        doc.add_paragraph(line)
        
        elif cell['cell_type'] == 'code':
            # Add code block
            if cell['source']:
                code_text = ''.join(cell['source'])
                if code_text.strip():
                    doc.add_heading('Code:', level=4)
                    code_para = doc.add_paragraph(code_text)
                    code_para.style = 'Intense Quote'
            
            # Add outputs
            if 'outputs' in cell and cell['outputs']:
                doc.add_heading('Output:', level=4)
                
                for output in cell['outputs']:
                    if output['output_type'] == 'stream':
                        # Text output
                        if 'text' in output:
                            output_text = ''.join(output['text'])
                            output_para = doc.add_paragraph(output_text)
                            output_para.style = 'Quote'
                    
                    elif output['output_type'] == 'display_data' or output['output_type'] == 'execute_result':
                        # Handle images
                        if 'data' in output and 'image/png' in output['data']:
                            try:
                                # Decode base64 image
                                img_data = base64.b64decode(output['data']['image/png'])
                                img_stream = io.BytesIO(img_data)
                                
                                # Add image to document
                                doc.add_paragraph("Figure:")
                                doc.add_picture(img_stream, width=Inches(6))
                                doc.add_paragraph()  # Add space after image
                            except Exception as e:
                                doc.add_paragraph(f"[Image could not be displayed: {str(e)}]")
                        
                        # Handle text data
                        if 'data' in output and 'text/plain' in output['data']:
                            text_output = ''.join(output['data']['text/plain'])
                            output_para = doc.add_paragraph(text_output)
                            output_para.style = 'Quote'
    
    # Save the document
    doc.save(output_path)
    print(f"Word document saved as: {output_path}")

def main():
    """
    Main function to convert the executed notebook to Word
    """
    try:
        convert_notebook_to_word(
            'Technical_Report_Analysis_Final.ipynb',
            'Technical_Report_Analysis_Final.docx'
        )
        print("Conversion to Word completed successfully!")

    except FileNotFoundError:
        print("Error: Executed notebook file not found. Please run the notebook execution first.")
    except Exception as e:
        print(f"Error during conversion: {str(e)}")

if __name__ == "__main__":
    main()
