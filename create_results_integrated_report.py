#!/usr/bin/env python3
"""
Results-Integrated Technical Report Generator
Creates a comprehensive technical report that includes actual execution results
"""

import os
import json
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
import base64
import markdown

def load_execution_results():
    """Load all execution results from the enhanced_results directory"""
    
    results_dir = Path("enhanced_results")
    if not results_dir.exists():
        print("❌ Enhanced results directory not found. Please run the enhanced ML analysis first.")
        return None
    
    results = {}
    
    try:
        # Load model metrics
        if (results_dir / "enhanced_model_metrics.csv").exists():
            results['model_metrics'] = pd.read_csv(results_dir / "enhanced_model_metrics.csv", index_col=0)
        
        # Load experiment summary
        if (results_dir / "experiment_summary.json").exists():
            with open(results_dir / "experiment_summary.json", 'r') as f:
                results['experiment_summary'] = json.load(f)
        
        # Load hyperparameter tuning results
        if (results_dir / "hyperparameter_tuning_results.json").exists():
            with open(results_dir / "hyperparameter_tuning_results.json", 'r') as f:
                results['hyperparameter_tuning'] = json.load(f)
        
        # Load classification report
        if (results_dir / "classification_report_LightGBM.csv").exists():
            results['classification_report'] = pd.read_csv(results_dir / "classification_report_LightGBM.csv", index_col=0)
        
        # Load feature importance
        if (results_dir / "feature_importance_LightGBM.csv").exists():
            results['feature_importance'] = pd.read_csv(results_dir / "feature_importance_LightGBM.csv")
        
        # Load cross-validation results
        if (results_dir / "initial_cv_results.csv").exists():
            results['cv_results'] = pd.read_csv(results_dir / "initial_cv_results.csv", index_col=0)
        
        print("✅ Successfully loaded execution results")
        return results
        
    except Exception as e:
        print(f"❌ Error loading results: {e}")
        return None

def encode_image_to_base64(image_path):
    """Convert image to base64 for embedding in HTML"""
    try:
        with open(image_path, 'rb') as img_file:
            return base64.b64encode(img_file.read()).decode('utf-8')
    except:
        return None

def create_results_section(results):
    """Create a comprehensive results section with actual data"""
    
    if not results:
        return "## Results\n\nNo execution results available. Please run the machine learning analysis first.\n"
    
    results_md = f"""
## 📊 Execution Results and Findings

*Generated on: {datetime.now().strftime("%B %d, %Y at %I:%M %p")}*

### 🏆 Overall Performance Summary

"""
    
    # Add experiment summary if available
    if 'experiment_summary' in results:
        exp_summary = results['experiment_summary']
        best_model = exp_summary.get('best_model', {})
        
        results_md += f"""
**Best Performing Model:** {best_model.get('name', 'N/A')}
- **Accuracy:** {best_model.get('accuracy', 0):.3f} ({best_model.get('accuracy', 0)*100:.1f}%)
- **F1-Score:** {best_model.get('f1_score', 0):.3f}
- **ROC AUC:** {best_model.get('roc_auc', 0):.3f}
- **Training Time:** {best_model.get('training_time', 0):.2f} seconds

**Dataset Information:**
- **Total Samples:** {exp_summary.get('experiment_info', {}).get('dataset_shape', [0, 0])[0]}
- **Features:** {exp_summary.get('experiment_info', {}).get('dataset_shape', [0, 0])[1]}
- **Classes:** {exp_summary.get('experiment_info', {}).get('n_classes', 0)}
- **Test Size:** {exp_summary.get('experiment_info', {}).get('test_size', 0)*100:.0f}%

"""
    
    # Add detailed model comparison
    if 'model_metrics' in results:
        metrics_df = results['model_metrics']
        
        results_md += """
### 📈 Detailed Model Performance Comparison

| Model | Accuracy | Precision | Recall | F1-Score | ROC AUC | Training Time (s) |
|-------|----------|-----------|--------|----------|---------|-------------------|
"""
        
        for idx, (model_name, row) in enumerate(metrics_df.head(10).iterrows()):
            results_md += f"| {model_name} | {row.get('Accuracy', 0):.3f} | {row.get('Precision', 0):.3f} | {row.get('Recall', 0):.3f} | {row.get('F1', 0):.3f} | {row.get('ROC AUC', 0):.3f} | {row.get('Training_Time', 0):.2f} |\n"
        
        results_md += "\n"
    
    # Add hyperparameter tuning results
    if 'hyperparameter_tuning' in results:
        tuning_results = results['hyperparameter_tuning']
        
        results_md += """
### 🎯 Hyperparameter Tuning Results

"""
        
        for model_name, tuning_data in tuning_results.items():
            if 'best_score' in tuning_data:
                results_md += f"""
**{model_name}:**
- **Best CV Score:** {tuning_data['best_score']:.4f}
- **Best Parameters:** 
"""
                for param, value in tuning_data['best_params'].items():
                    results_md += f"  - {param}: {value}\n"
                results_md += "\n"
    
    # Add classification report
    if 'classification_report' in results:
        class_report = results['classification_report']
        
        results_md += """
### 📋 Detailed Classification Report (Best Model)

| Class | Precision | Recall | F1-Score | Support |
|-------|-----------|--------|----------|---------|
"""
        
        for idx, (class_name, row) in enumerate(class_report.iterrows()):
            if 'precision' in row and 'recall' in row:
                results_md += f"| {class_name} | {row.get('precision', 0):.3f} | {row.get('recall', 0):.3f} | {row.get('f1-score', 0):.3f} | {row.get('support', 0):.0f} |\n"
        
        results_md += "\n"
    
    # Add feature importance
    if 'feature_importance' in results:
        feat_imp = results['feature_importance']
        
        results_md += """
### 🔍 Feature Importance Analysis

**Top 10 Most Important Features:**

| Rank | Feature | Importance Score | Description |
|------|---------|------------------|-------------|
"""
        
        for idx, (_, row) in enumerate(feat_imp.head(10).iterrows(), 1):
            feature_name = row.get('feature', 'Unknown')
            importance = row.get('importance', 0)
            
            # Add feature description based on name
            if 'NE' in feature_name:
                cell_type = "Neutrophil"
            elif 'LY' in feature_name:
                cell_type = "Lymphocyte"
            elif 'MO' in feature_name:
                cell_type = "Monocyte"
            else:
                cell_type = "Combined"
            
            results_md += f"| {idx} | {feature_name} | {importance:.4f} | {cell_type} parameter |\n"
        
        results_md += "\n"
    
    # Add cross-validation results
    if 'cv_results' in results:
        cv_results = results['cv_results']
        
        results_md += """
### 🔄 Cross-Validation Performance

**5-Fold Cross-Validation Results (Top Models):**

| Model | Mean Accuracy | Std Deviation | 95% Confidence Interval |
|-------|---------------|---------------|-------------------------|
"""
        
        # Parse the summary column if it exists
        for model_name, row in cv_results.head(8).iterrows():
            if 'summary' in row:
                summary = str(row['summary'])
                # Extract accuracy from summary string
                import re
                acc_match = re.search(r'Acc: ([\d.]+)±([\d.]+)', summary)
                if acc_match:
                    mean_acc = float(acc_match.group(1))
                    std_acc = float(acc_match.group(2))
                    ci_lower = mean_acc - 1.96 * std_acc
                    ci_upper = mean_acc + 1.96 * std_acc
                    results_md += f"| {model_name} | {mean_acc:.3f} | {std_acc:.3f} | [{ci_lower:.3f}, {ci_upper:.3f}] |\n"
        
        results_md += "\n"
    
    return results_md

def create_enhanced_technical_report():
    """Create the complete technical report with execution results"""
    
    print("🔄 Creating enhanced technical report with execution results...")
    
    # Load execution results
    results = load_execution_results()
    
    # Read original technical report
    original_report_path = Path("Technical_Report.md")
    if not original_report_path.exists():
        print("❌ Original Technical_Report.md not found!")
        return False
    
    with open(original_report_path, 'r', encoding='utf-8') as f:
        original_content = f.read()
    
    # Create results section
    results_section = create_results_section(results)
    
    # Insert results section after the methodology sections
    # Find a good insertion point (after section 7 or before conclusion)
    insertion_point = original_content.find("## 7. Results and Performance Evaluation")
    
    if insertion_point == -1:
        # If section 7 not found, insert before conclusion
        insertion_point = original_content.find("## Conclusion")
    
    if insertion_point == -1:
        # If conclusion not found, append at the end
        enhanced_content = original_content + "\n\n" + results_section
    else:
        # Insert the results section
        enhanced_content = (
            original_content[:insertion_point] + 
            results_section + "\n\n" + 
            original_content[insertion_point:]
        )
    
    # Add execution timestamp and metadata
    header_addition = f"""

---

**📊 ENHANCED REPORT WITH EXECUTION RESULTS**

*This report includes actual execution results from the machine learning analysis.*  
*Generated on: {datetime.now().strftime("%B %d, %Y at %I:%M %p")}*  
*Results integrated from: enhanced_results/ directory*

---

"""
    
    # Insert header addition after the first title
    title_end = enhanced_content.find("\n", enhanced_content.find("#"))
    if title_end != -1:
        enhanced_content = (
            enhanced_content[:title_end] + 
            header_addition + 
            enhanced_content[title_end:]
        )
    
    return enhanced_content

def main():
    """Main function to create results-integrated report"""
    
    # Create enhanced content
    enhanced_content = create_enhanced_technical_report()
    
    if not enhanced_content:
        return False
    
    # Create output directory
    output_dir = Path("technical_report_output")
    output_dir.mkdir(exist_ok=True)
    
    # Save enhanced markdown
    enhanced_md_path = output_dir / "Technical_Report_With_Results.md"
    with open(enhanced_md_path, 'w', encoding='utf-8') as f:
        f.write(enhanced_content)
    
    print(f"✅ Enhanced technical report created: {enhanced_md_path}")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
