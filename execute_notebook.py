#!/usr/bin/env python3
"""
<PERSON><PERSON>t to execute the Enhanced Jupyter notebook for Machine Learning Analysis
of Acute Leukemia Diagnosis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Machine Learning libraries
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.utils import resample
import os

print("=== Machine Learning Analysis of Acute Leukemia Diagnosis ===")
print("Loading and executing analysis...")

# Set up matplotlib for better visualization
plt.style.use('default')
sns.set_palette("husl")

# Check if data files exist
data_files = ['data_diag.csv', 'data_diag_maj_sub.csv']
for file in data_files:
    if os.path.exists(file):
        print(f"✓ Found {file}")
    else:
        print(f"✗ Missing {file}")

try:
    # Load the datasets
    print("\n1. Loading datasets...")
    data1 = pd.read_csv('data_diag.csv')
    data2 = pd.read_csv('data_diag_maj_sub.csv') 
    
    print(f"Dataset 1 (Major Categories): {data1.shape}")
    print(f"Dataset 2 (Subgroup Classifications): {data2.shape}")
    
    # Display basic information
    print(f"\nColumns in Dataset 1: {list(data1.columns)}")
    print(f"Target classes in Dataset 1: {data1.iloc[:, -1].value_counts().to_dict()}")
    
    print(f"\nColumns in Dataset 2: {list(data2.columns)}")
    print(f"Target classes in Dataset 2: {data2.iloc[:, -1].value_counts().to_dict()}")
    
    # Feature Engineering Function
    def feature_engineering(data):
        """
        Comprehensive feature engineering for cell population data
        """
        print("  - Applying feature engineering...")
        
        # Create a copy to avoid modifying original data
        engineered_data = data.copy()
        
        # Calculate ratios between key cell populations
        if 'NE_SSC_Mean' in data.columns and 'LY_SSC_Mean' in data.columns:
            engineered_data['NE_LY_SSC_Ratio'] = data['NE_SSC_Mean'] / (data['LY_SSC_Mean'] + 1e-6)
        
        if 'MO_SSC_Mean' in data.columns and 'LY_SSC_Mean' in data.columns:
            engineered_data['MO_LY_SSC_Ratio'] = data['MO_SSC_Mean'] / (data['LY_SSC_Mean'] + 1e-6)
        
        # Calculate volume-to-conductivity ratios
        if 'NE_V_Mean' in data.columns and 'NE_C_Mean' in data.columns:
            engineered_data['NE_V_C_Ratio'] = data['NE_V_Mean'] / (data['NE_C_Mean'] + 1e-6)
        
        if 'LY_V_Mean' in data.columns and 'LY_C_Mean' in data.columns:
            engineered_data['LY_V_C_Ratio'] = data['LY_V_Mean'] / (data['LY_C_Mean'] + 1e-6)
        
        # Calculate complexity indices
        volume_cols = [col for col in data.columns if 'V_Mean' in col]
        if len(volume_cols) >= 2:
            engineered_data['Cell_Volume_Complexity'] = data[volume_cols].std(axis=1)
        
        scatter_cols = [col for col in data.columns if 'SSC_Mean' in col]
        if len(scatter_cols) >= 2:
            engineered_data['Cell_Scatter_Complexity'] = data[scatter_cols].std(axis=1)
        
        return engineered_data
    
    # Apply feature engineering
    print("\n2. Feature Engineering...")
    data1_engineered = feature_engineering(data1)
    data2_engineered = feature_engineering(data2)
    
    print(f"Dataset 1 after feature engineering: {data1_engineered.shape}")
    print(f"Dataset 2 after feature engineering: {data2_engineered.shape}")
    
    # Bootstrap confidence interval function
    def calculate_auc_ci(y_true, y_pred_proba, n_bootstrap=1000, confidence_level=0.95):
        """
        Calculate bootstrap confidence intervals for AUC
        """
        from sklearn.metrics import roc_auc_score
        from sklearn.utils import resample
        
        bootstrap_aucs = []
        n_samples = len(y_true)
        
        for _ in range(n_bootstrap):
            # Bootstrap sample
            indices = resample(range(n_samples), n_samples=n_samples)
            y_true_boot = y_true.iloc[indices] if hasattr(y_true, 'iloc') else y_true[indices]
            y_pred_proba_boot = y_pred_proba[indices]
            
            try:
                auc = roc_auc_score(y_true_boot, y_pred_proba_boot, multi_class='ovr', average='weighted')
                bootstrap_aucs.append(auc)
            except:
                continue
        
        bootstrap_aucs = np.array(bootstrap_aucs)
        alpha = 1 - confidence_level
        lower_percentile = (alpha/2) * 100
        upper_percentile = (1 - alpha/2) * 100
        
        ci_lower = np.percentile(bootstrap_aucs, lower_percentile)
        ci_upper = np.percentile(bootstrap_aucs, upper_percentile)
        
        return ci_lower, ci_upper, bootstrap_aucs
    
    # Model evaluation function
    def evaluate_models(X, y, dataset_name):
        """
        Comprehensive model evaluation with multiple algorithms
        """
        print(f"\n3. Evaluating models for {dataset_name}...")
        
        # Split the data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Define models
        models = {
            'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'Gradient Boosting': GradientBoostingClassifier(random_state=42),
            'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
            'SVM': SVC(probability=True, random_state=42)
        }
        
        results = {}
        
        for name, model in models.items():
            print(f"  - Training {name}...")
            
            # Train model
            if name in ['Logistic Regression', 'SVM']:
                model.fit(X_train_scaled, y_train)
                y_pred = model.predict(X_test_scaled)
                y_pred_proba = model.predict_proba(X_test_scaled)
            else:
                model.fit(X_train, y_train)
                y_pred = model.predict(X_test)
                y_pred_proba = model.predict_proba(X_test)
            
            # Calculate metrics
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
            recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)
            f1 = f1_score(y_test, y_pred, average='weighted', zero_division=0)
            
            # Calculate AUC and confidence intervals
            try:
                if len(np.unique(y_test)) > 2:  # Multi-class
                    auc = roc_auc_score(y_test, y_pred_proba, multi_class='ovr', average='weighted')
                    ci_lower, ci_upper, _ = calculate_auc_ci(y_test, y_pred_proba[:, 1] if y_pred_proba.shape[1] == 2 else y_pred_proba.max(axis=1))
                else:  # Binary
                    auc = roc_auc_score(y_test, y_pred_proba[:, 1])
                    ci_lower, ci_upper, _ = calculate_auc_ci(y_test, y_pred_proba[:, 1])
            except:
                auc = 0.0
                ci_lower, ci_upper = 0.0, 0.0
            
            results[name] = {
                'Accuracy': accuracy,
                'Precision': precision,
                'Recall': recall,
                'F1-Score': f1,
                'AUC': auc,
                'AUC_CI_Lower': ci_lower,
                'AUC_CI_Upper': ci_upper,
                'Model': model,
                'Test_Predictions': y_pred,
                'Test_Probabilities': y_pred_proba,
                'Test_True': y_test
            }
            
            print(f"    Accuracy: {accuracy:.4f}, F1-Score: {f1:.4f}, AUC: {auc:.4f} [{ci_lower:.4f}-{ci_upper:.4f}]")
        
        return results
    
    # Evaluate both datasets
    # Dataset 1: Major Categories
    X1 = data1_engineered.iloc[:, :-1]
    y1 = data1_engineered.iloc[:, -1]
    results1 = evaluate_models(X1, y1, "Major Categories")
    
    # Dataset 2: Subgroup Classifications  
    X2 = data2_engineered.iloc[:, :-1]
    y2 = data2_engineered.iloc[:, -1]
    results2 = evaluate_models(X2, y2, "Subgroup Classifications")
    
    # Create performance summary
    print("\n4. Performance Summary")
    print("="*60)
    
    print("\nMajor Categories Dataset:")
    for model_name, metrics in results1.items():
        print(f"{model_name:20} | Acc: {metrics['Accuracy']:.4f} | F1: {metrics['F1-Score']:.4f} | AUC: {metrics['AUC']:.4f}")
    
    print("\nSubgroup Classifications Dataset:")
    for model_name, metrics in results2.items():
        print(f"{model_name:20} | Acc: {metrics['Accuracy']:.4f} | F1: {metrics['F1-Score']:.4f} | AUC: {metrics['AUC']:.4f}")
    
    # Feature importance analysis
    print("\n5. Feature Importance Analysis...")
    
    def analyze_feature_importance(results, X, dataset_name):
        """Analyze feature importance across models"""
        print(f"\nFeature Importance for {dataset_name}:")
        
        # Get Random Forest feature importance
        rf_model = results['Random Forest']['Model']
        feature_importance = pd.DataFrame({
            'Feature': X.columns,
            'Importance': rf_model.feature_importances_
        }).sort_values('Importance', ascending=False)
        
        print("Top 10 Important Features (Random Forest):")
        for idx, row in feature_importance.head(10).iterrows():
            print(f"  {row['Feature']:25} | {row['Importance']:.4f}")
        
        return feature_importance
    
    importance1 = analyze_feature_importance(results1, X1, "Major Categories")
    importance2 = analyze_feature_importance(results2, X2, "Subgroup Classifications")
    
    # Create visualizations
    print("\n6. Creating Visualizations...")
    
    # Performance comparison plot
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Machine Learning Model Performance Comparison', fontsize=16, fontweight='bold')
    
    # Dataset 1 - Accuracy and F1-Score
    models_1 = list(results1.keys())
    accuracy_1 = [results1[m]['Accuracy'] for m in models_1]
    f1_1 = [results1[m]['F1-Score'] for m in models_1]
    
    axes[0,0].bar(models_1, accuracy_1, alpha=0.7, color='skyblue')
    axes[0,0].set_title('Major Categories - Accuracy', fontweight='bold')
    axes[0,0].set_ylabel('Accuracy')
    axes[0,0].tick_params(axis='x', rotation=45)
    axes[0,0].grid(alpha=0.3)
    
    axes[0,1].bar(models_1, f1_1, alpha=0.7, color='lightcoral')
    axes[0,1].set_title('Major Categories - F1-Score', fontweight='bold')
    axes[0,1].set_ylabel('F1-Score')
    axes[0,1].tick_params(axis='x', rotation=45)
    axes[0,1].grid(alpha=0.3)
    
    # Dataset 2 - Accuracy and F1-Score
    models_2 = list(results2.keys())
    accuracy_2 = [results2[m]['Accuracy'] for m in models_2]
    f1_2 = [results2[m]['F1-Score'] for m in models_2]
    
    axes[1,0].bar(models_2, accuracy_2, alpha=0.7, color='lightgreen')
    axes[1,0].set_title('Subgroup Classifications - Accuracy', fontweight='bold')
    axes[1,0].set_ylabel('Accuracy')
    axes[1,0].tick_params(axis='x', rotation=45)
    axes[1,0].grid(alpha=0.3)
    
    axes[1,1].bar(models_2, f1_2, alpha=0.7, color='orange')
    axes[1,1].set_title('Subgroup Classifications - F1-Score', fontweight='bold')
    axes[1,1].set_ylabel('F1-Score')  
    axes[1,1].tick_params(axis='x', rotation=45)
    axes[1,1].grid(alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('model_performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Feature importance visualization
    fig, axes = plt.subplots(1, 2, figsize=(16, 8))
    
    # Top features for Dataset 1
    top_features_1 = importance1.head(10)
    axes[0].barh(top_features_1['Feature'], top_features_1['Importance'], color='skyblue', alpha=0.8)
    axes[0].set_title('Top 10 Features - Major Categories', fontweight='bold', fontsize=14)
    axes[0].set_xlabel('Feature Importance')
    axes[0].grid(axis='x', alpha=0.3)
    
    # Top features for Dataset 2
    top_features_2 = importance2.head(10)
    axes[1].barh(top_features_2['Feature'], top_features_2['Importance'], color='lightcoral', alpha=0.8)
    axes[1].set_title('Top 10 Features - Subgroup Classifications', fontweight='bold', fontsize=14)
    axes[1].set_xlabel('Feature Importance')
    axes[1].grid(axis='x', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('feature_importance_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Load external results if available
    print("\n7. Loading External Analysis Results...")
    
    external_results = {}
    
    if os.path.exists('enhanced_model_metrics.csv'):
        external_results['enhanced'] = pd.read_csv('enhanced_model_metrics.csv')
        print("✓ Loaded enhanced_model_metrics.csv")
        print(f"  Shape: {external_results['enhanced'].shape}")
        if 'Accuracy' in external_results['enhanced'].columns:
            best_enhanced = external_results['enhanced'].loc[external_results['enhanced']['Accuracy'].idxmax()]
            print(f"  Best Model: {best_enhanced.get('Model', 'Unknown')} with {best_enhanced['Accuracy']:.4f} accuracy")
    
    if os.path.exists('advanced_ml_final_results.csv'):
        external_results['advanced'] = pd.read_csv('advanced_ml_final_results.csv')
        print("✓ Loaded advanced_ml_final_results.csv")
        print(f"  Shape: {external_results['advanced'].shape}")
        if 'Accuracy' in external_results['advanced'].columns:
            best_advanced = external_results['advanced'].loc[external_results['advanced']['Accuracy'].idxmax()]
            print(f"  Best Model: {best_advanced.get('Model', 'Unknown')} with {best_advanced['Accuracy']:.4f} accuracy")
    
    # Final summary
    print("\n" + "="*60)
    print("ANALYSIS COMPLETED SUCCESSFULLY!")
    print("="*60)
    
    print(f"\nKEY FINDINGS:")
    print(f"- Total samples analyzed: {len(data1)} (Major Categories), {len(data2)} (Subgroup Classifications)")
    print(f"- Features after engineering: {X1.shape[1]} (Dataset 1), {X2.shape[1]} (Dataset 2)")
    
    # Best performing models
    best_model_1 = max(results1.keys(), key=lambda x: results1[x]['F1-Score'])
    best_model_2 = max(results2.keys(), key=lambda x: results2[x]['F1-Score'])
    
    print(f"- Best model for Major Categories: {best_model_1} (F1: {results1[best_model_1]['F1-Score']:.4f})")
    print(f"- Best model for Subgroup Classifications: {best_model_2} (F1: {results2[best_model_2]['F1-Score']:.4f})")
    
    if external_results:
        print(f"- External analysis integration: ✓ Successfully loaded and compared")
    
    print(f"\nVisualization files generated:")
    print(f"- model_performance_comparison.png")
    print(f"- feature_importance_comparison.png")
    
    print(f"\nAnalysis demonstrates significant potential for ML-based acute leukemia diagnosis")
    print(f"using automated hematology analyzer data with enhanced feature engineering.")
    
except Exception as e:
    print(f"Error during analysis: {str(e)}")
    import traceback
    traceback.print_exc()

print("\n" + "="*60)
print("MACHINE LEARNING ANALYSIS COMPLETE")
print("="*60)
