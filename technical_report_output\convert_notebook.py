#!/usr/bin/env python3
"""
Notebook Converter Script
Converts the Jupyter notebook to Word and HTML formats
"""

import subprocess
import sys
from pathlib import Path

def convert_notebook():
    """Convert notebook to multiple formats"""

    notebook_path = "Complete_ML_Analysis.ipynb"

    if not Path(notebook_path).exists():
        print(f"❌ Notebook not found: {notebook_path}")
        return False

    print("🔄 Converting Jupyter notebook to multiple formats...")

    try:
        # Convert to HTML
        print("📄 Converting to HTML...")
        subprocess.run([
            "jupyter", "nbconvert", "--to", "html",
            "--output", "Complete_ML_Analysis_Report.html",
            notebook_path
        ], check=True)
        print("✅ HTML conversion completed")

        # Convert to Word (requires pandoc)
        print("📝 Converting to Word document...")
        try:
            subprocess.run([
                "jupyter", "nbconvert", "--to", "docx",
                "--output", "Complete_ML_Analysis_Report.docx",
                notebook_path
            ], check=True)
            print("✅ Word conversion completed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  Word conversion failed. Install pandoc for Word export:")
            print("   conda install pandoc")
            print("   or visit: https://pandoc.org/installing.html")

        # Convert to PDF (requires additional dependencies)
        print("📑 Converting to PDF...")
        try:
            subprocess.run([
                "jupyter", "nbconvert", "--to", "pdf",
                "--output", "Complete_ML_Analysis_Report.pdf",
                notebook_path
            ], check=True)
            print("✅ PDF conversion completed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  PDF conversion failed. Install required dependencies:")
            print("   conda install -c conda-forge pandoc")
            print("   conda install -c conda-forge texlive-core")

        print("\n🎉 Notebook conversion completed!")
        print("📁 Generated files:")
        print("   - Complete_ML_Analysis_Report.html")
        print("   - Complete_ML_Analysis_Report.docx (if pandoc available)")
        print("   - Complete_ML_Analysis_Report.pdf (if LaTeX available)")

        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ Conversion error: {e}")
        return False
    except FileNotFoundError:
        print("❌ Jupyter not found. Install with: pip install jupyter")
        return False

if __name__ == "__main__":
    success = convert_notebook()
    sys.exit(0 if success else 1)
