import pandas as pd
import numpy as np
from sklearn.feature_selection import Select<PERSON>B<PERSON>, f_classif, RFE
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler, PolynomialFeatures

def create_feature_interactions(X):
    """Create interaction features between pairs of features"""
    print("Creating feature interactions...")
    poly = PolynomialFeatures(degree=2, interaction_only=True, include_bias=False)
    X_inter = poly.fit_transform(X)
    
    # Create feature names for interactions
    feature_names = X.columns.tolist()
    interaction_features = poly.get_feature_names_out(feature_names)
    
    # Return only the interaction terms (not the original features)
    X_interactions = pd.DataFrame(
        X_inter[:, len(feature_names):], 
        columns=interaction_features[len(feature_names):]
    )
    
    return X_interactions

def select_best_features(X, y, k=10, method='univariate'):
    """Select best features using different methods"""
    print(f"Selecting top {k} features using {method}...")
    
    if method == 'univariate':
        selector = SelectKBest(f_classif, k=k)
        X_new = selector.fit_transform(X, y)
        selected_indices = selector.get_support(indices=True)
        
    elif method == 'rfe':
        from sklearn.ensemble import RandomForestClassifier
        estimator = RandomForestClassifier(n_estimators=100, random_state=42)
        selector = RFE(estimator, n_features_to_select=k)
        X_new = selector.fit_transform(X, y)
        selected_indices = selector.get_support(indices=True)
        
    elif method == 'pca':
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        selector = PCA(n_components=k)
        X_new = selector.fit_transform(X_scaled)
        # For PCA, we don't have selected indices, so return transformed data
        return pd.DataFrame(X_new, columns=[f'PC{i+1}' for i in range(k)])
    
    # Get selected feature names
    if isinstance(X, pd.DataFrame):
        selected_features = X.columns[selected_indices]
        return X.iloc[:, selected_indices]
    else:
        return X[:, selected_indices]

# Usage example:
# X_interactions = create_feature_interactions(X)
# X_enhanced = pd.concat([X, X_interactions], axis=1)
# X_selected = select_best_features(X_enhanced, y, k=20, method='univariate')