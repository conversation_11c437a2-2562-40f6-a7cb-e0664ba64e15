import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier

# Load data
df = pd.read_csv('data_diag.csv')
X = df.drop('Diagnosis', axis=1)
y = df['Diagnosis']

print("=== DATASET INFO ===")
print("Shape:", df.shape)
print("Classes:", sorted(y.unique()))
print("Class counts:", dict(y.value_counts().sort_index()))

# Split and train
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

# Train model
rf = RandomForestClassifier(n_estimators=50, random_state=42)
rf.fit(X_train, y_train)

# Get probabilities
y_pred_proba = rf.predict_proba(X_test)

print("\n=== SHAPE ANALYSIS ===")
print("Test set classes:", len(np.unique(y_test)))
print("Probability columns:", y_pred_proba.shape[1])
print("Probabilities shape:", y_pred_proba.shape)

# Verify shapes match
classes_match = len(np.unique(y_test)) == y_pred_proba.shape[1]
print("Shapes match correctly:", classes_match)

if classes_match:
    print("\nRESULT: SUCCESS - No shape mismatch")
    print("The model correctly produces 3 probability columns for 3 classes")
    print("Shape mismatch handling should NOT be triggered")
else:
    print("\nRESULT: ISSUE - Shape mismatch detected")
    print("This would trigger the shape adjustment code")
