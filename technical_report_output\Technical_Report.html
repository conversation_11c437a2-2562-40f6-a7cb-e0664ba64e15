
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Comprehensive Machine Learning Analysis - Technical Report</title>
        <style>
            body {
                font-family: 'Georgia', 'Times New Roman', serif;
                line-height: 1.6;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
                color: #333;
                background-color: #fff;
            }
            
            h1 {
                color: #2c3e50;
                border-bottom: 3px solid #3498db;
                padding-bottom: 10px;
                font-size: 2.5em;
                text-align: center;
            }
            
            h2 {
                color: #34495e;
                border-bottom: 2px solid #ecf0f1;
                padding-bottom: 8px;
                margin-top: 40px;
                font-size: 1.8em;
            }
            
            h3 {
                color: #2c3e50;
                margin-top: 30px;
                font-size: 1.4em;
            }
            
            h4 {
                color: #34495e;
                margin-top: 25px;
                font-size: 1.2em;
            }
            
            code {
                background-color: #f8f9fa;
                padding: 2px 6px;
                border-radius: 3px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 0.9em;
                color: #e74c3c;
            }
            
            pre {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 5px;
                padding: 15px;
                overflow-x: auto;
                margin: 20px 0;
            }
            
            pre code {
                background-color: transparent;
                padding: 0;
                color: #2c3e50;
            }
            
            table {
                border-collapse: collapse;
                width: 100%;
                margin: 20px 0;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            
            th, td {
                border: 1px solid #ddd;
                padding: 12px;
                text-align: left;
            }
            
            th {
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }
            
            tr:nth-child(even) {
                background-color: #f8f9fa;
            }
            
            blockquote {
                border-left: 4px solid #3498db;
                margin: 20px 0;
                padding: 10px 20px;
                background-color: #f8f9fa;
                font-style: italic;
            }
            
            .toc {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 20px;
                margin: 30px 0;
            }
            
            .toc h2 {
                margin-top: 0;
                color: #2c3e50;
                border-bottom: none;
            }
            
            .toc ul {
                list-style-type: none;
                padding-left: 0;
            }
            
            .toc li {
                margin: 8px 0;
            }
            
            .toc a {
                color: #3498db;
                text-decoration: none;
                font-weight: 500;
            }
            
            .toc a:hover {
                text-decoration: underline;
            }
            
            .header-info {
                text-align: center;
                margin-bottom: 40px;
                padding: 20px;
                background-color: #f8f9fa;
                border-radius: 10px;
                border: 1px solid #e9ecef;
            }
            
            .date-generated {
                text-align: right;
                font-style: italic;
                color: #6c757d;
                margin-top: 30px;
                border-top: 1px solid #e9ecef;
                padding-top: 15px;
            }
            
            @media print {
                body {
                    font-size: 12pt;
                    line-height: 1.4;
                }
                
                h1 {
                    font-size: 18pt;
                }
                
                h2 {
                    font-size: 16pt;
                    page-break-after: avoid;
                }
                
                h3 {
                    font-size: 14pt;
                    page-break-after: avoid;
                }
                
                pre {
                    page-break-inside: avoid;
                }
                
                table {
                    page-break-inside: avoid;
                }
            }
        </style>
    </head>
    <body>
        <div class="header-info">
            <h1>Comprehensive Machine Learning Analysis of Cell Population Data for Acute Leukemia Diagnosis</h1>
            <p><strong>Technical Documentation</strong></p>
            <p>Generated on: June 07, 2025 at 07:49 AM</p>
        </div>
        
        <hr />
<h1 id="comprehensive-machine-learning-analysis-of-cell-population-data-for-acute-leukemia-diagnosis-a-technical-documentation">Comprehensive Machine Learning Analysis of Cell Population Data for Acute Leukemia Diagnosis: A Technical Documentation</h1>
<hr />
<p><strong>📊 ENHANCED REPORT WITH EXECUTION RESULTS</strong></p>
<p><em>This report includes actual execution results from the machine learning analysis.</em><br />
<em>Generated on: June 07, 2025 at 07:49 AM</em><br />
<em>Results integrated from: enhanced_results/ directory</em></p>
<hr />
<hr />
<h2 id="abstract">Abstract</h2>
<p>This comprehensive technical document presents a detailed analysis of machine learning approaches for acute leukemia diagnosis using cell population data from automated hematology analyzers. The study compares two datasets: one with major diagnostic categories (data_diag.csv) and another with subgroup classifications (data_diag_maj_sub.csv). Through extensive feature engineering, advanced machine learning techniques, and explainability analysis, we achieved exceptional classification performance with AUC values exceeding 0.99 for major diagnostic categories and 0.87 for subgroup classifications. The analysis incorporates SHAP (SHapley Additive exPlanations) for model interpretability, bootstrap confidence intervals for statistical robustness, and comprehensive visualizations for result validation. This work demonstrates the potential of artificial intelligence in providing cost-effective, rapid screening tools for acute leukemia diagnosis in resource-limited settings.</p>
<p><strong>Keywords:</strong> Machine Learning, Acute Leukemia, Cell Population Data, SHAP Analysis, Feature Engineering, Automated Hematology Analyzers, Artificial Intelligence, Medical Diagnosis</p>
<hr />
<h2 id="table-of-contents">Table of Contents</h2>
<ol>
<li><a href="#introduction">Introduction</a></li>
<li><a href="#dataset-description-and-preprocessing">Dataset Description and Preprocessing</a></li>
<li><a href="#feature-engineering-methodology">Feature Engineering Methodology</a></li>
<li><a href="#machine-learning-models-and-algorithms">Machine Learning Models and Algorithms</a></li>
<li><a href="#statistical-analysis-and-confidence-intervals">Statistical Analysis and Confidence Intervals</a></li>
<li><a href="#model-interpretability-and-shap-analysis">Model Interpretability and SHAP Analysis</a></li>
<li><a href="#results-and-performance-evaluation">Results and Performance Evaluation</a></li>
<li><a href="#comparative-analysis-between-datasets">Comparative Analysis Between Datasets</a></li>
<li><a href="#visualization-and-graphical-analysis">Visualization and Graphical Analysis</a></li>
<li><a href="#code-implementation-details">Code Implementation Details</a></li>
<li><a href="#discussion-and-clinical-implications">Discussion and Clinical Implications</a></li>
<li><a href="#limitations-and-future-directions">Limitations and Future Directions</a></li>
<li><a href="#conclusion">Conclusion</a></li>
</ol>
<hr />
<h2 id="1-introduction">1. Introduction</h2>
<p>The diagnosis of acute leukemia represents one of the most critical challenges in modern hematology, requiring rapid, accurate, and cost-effective diagnostic approaches. Traditional diagnostic methods rely heavily on morphological examination, flow cytometric immunophenotyping, cytochemistry, karyotyping, and molecular genetics. However, these approaches face significant limitations in resource-constrained settings due to high costs, technical complexity, and the need for specialized expertise.</p>
<p>The emergence of artificial intelligence and machine learning in healthcare has opened new avenues for diagnostic innovation. Automated hematology analyzers, which are widely available in clinical laboratories, generate extensive cell population data (CPD) that contains rich morphological and functional information about blood cells. This data, traditionally used for basic complete blood count analysis, holds untapped potential for advanced diagnostic applications when combined with sophisticated machine learning algorithms.</p>
<p>Cell population data from modern hematology analyzers captures detailed characteristics of different cell types including neutrophils (NE), lymphocytes (LY), and monocytes (MO). These measurements include positional parameters (X, Y, Z coordinates) representing cell characteristics in multi-dimensional space, as well as width parameters (WX, WY, WZ) indicating the distribution and variability of cell populations. The integration of these parameters through advanced feature engineering and machine learning can potentially identify subtle patterns indicative of acute leukemia that may not be apparent through conventional analysis.</p>
<p>Previous studies have explored machine learning applications in hematological diagnosis, primarily focusing on image-based analysis of peripheral blood smears. However, these approaches face challenges including image quality variability, staining inconsistencies, and high computational requirements. In contrast, cell population data offers standardized, quantitative measurements that are less susceptible to pre-analytical variables and can be processed rapidly using conventional computing resources.</p>
<p>The present study addresses this gap by developing and evaluating comprehensive machine learning models for acute leukemia diagnosis using cell population data. We compare two diagnostic approaches: major diagnostic categories versus subgroup classifications, providing insights into the optimal granularity for automated diagnosis. Through extensive feature engineering, we transform raw cell population measurements into clinically meaningful parameters that capture the complex relationships between different cell types and their characteristics.</p>
<p>Our methodology incorporates several advanced techniques including SHAP analysis for model interpretability, bootstrap confidence intervals for statistical robustness, and comprehensive cross-validation for performance validation. The explainability component is particularly crucial for clinical adoption, as healthcare providers require understanding of the decision-making process behind automated diagnostic recommendations.</p>
<p>The clinical significance of this work extends beyond technical innovation. In resource-limited settings where specialized hematological expertise may be scarce, an automated screening tool based on readily available cell population data could facilitate early detection and appropriate referral of acute leukemia cases. This could potentially improve patient outcomes by reducing diagnostic delays and ensuring timely initiation of appropriate treatment.</p>
<p>Furthermore, the standardized nature of cell population data across different analyzer platforms suggests potential for broad applicability and scalability. Unlike image-based approaches that may require platform-specific adaptations, cell population data follows consistent measurement principles across manufacturers, enhancing the generalizability of developed models.</p>
<p>The economic implications are equally significant. By leveraging existing laboratory infrastructure and data streams, this approach avoids the substantial capital investments required for specialized diagnostic equipment. The computational requirements are modest, making implementation feasible even in settings with limited information technology resources.</p>
<p>This technical document provides comprehensive documentation of our methodology, implementation details, and results. We present detailed code explanations, statistical analyses, and visualization techniques that enable reproducibility and further development by the research community. The analysis encompasses both datasets to provide comparative insights into the trade-offs between diagnostic granularity and classification accuracy.</p>
<hr />
<h2 id="2-dataset-description-and-preprocessing">2. Dataset Description and Preprocessing</h2>
<h3 id="21-dataset-overview">2.1 Dataset Overview</h3>
<p>The analysis utilizes two complementary datasets derived from the same underlying patient population, providing different levels of diagnostic granularity. Both datasets contain identical feature sets but differ in their target variable classification schemes, enabling direct comparison of diagnostic approaches.</p>
<p><strong>Dataset 1 (data_diag.csv) - Major Diagnostic Categories:</strong>
- Total samples: 791 patients
- Features: 18 cell population parameters
- Target classes: 3 major diagnostic categories (0, 1, 2)
- Class distribution:
  - Class 0: 100 samples (12.6%) - Control/Normal
  - Class 1: 555 samples (70.2%) - Major acute leukemia category
  - Class 2: 136 samples (17.2%) - Secondary acute leukemia category</p>
<p><strong>Dataset 2 (data_diag_maj_sub.csv) - Subgroup Classifications:</strong>
- Total samples: 791 patients (identical patient cohort)
- Features: 18 cell population parameters (identical measurements)
- Target classes: 4 subgroup diagnostic categories (0, 1, 2, 3)
- Class distribution:
  - Class 0: 100 samples (12.6%) - Control/Normal
  - Class 1: 316 samples (40.0%) - Acute leukemia subgroup 1
  - Class 2: 239 samples (30.2%) - Acute leukemia subgroup 2
  - Class 3: 136 samples (17.2%) - Secondary acute leukemia category</p>
<h3 id="22-feature-set-description">2.2 Feature Set Description</h3>
<p>The cell population data encompasses 18 quantitative parameters derived from automated hematology analyzer measurements. These parameters are systematically organized into three cell type categories, each with six associated measurements:</p>
<p><strong>Neutrophil (NE) Parameters:</strong>
- NEX, NEY, NEZ: Positional coordinates in three-dimensional measurement space
- NEWX, NEWY, NEWZ: Width parameters indicating population distribution characteristics</p>
<p><strong>Lymphocyte (LY) Parameters:</strong>
- LYX, LYY, LYZ: Positional coordinates in three-dimensional measurement space
- LYWX, LYWY, LYWZ: Width parameters indicating population distribution characteristics</p>
<p><strong>Monocyte (MO) Parameters:</strong>
- MOX, MOY, MOZ: Positional coordinates in three-dimensional measurement space
- MOWX, MOWY, MOWZ: Width parameters indicating population distribution characteristics</p>
<h3 id="23-data-quality-assessment">2.3 Data Quality Assessment</h3>
<p>Comprehensive data quality assessment revealed excellent data integrity across both datasets. Key findings include:</p>
<p><strong>Missing Values Analysis:</strong>
- Zero missing values detected in both datasets
- Complete case analysis possible without imputation requirements
- No systematic patterns of data absence</p>
<p><strong>Data Consistency Verification:</strong>
- Feature data identical between both datasets (confirmed through direct comparison)
- Only target variable differs between datasets
- Consistent measurement scales and units across all parameters</p>
<p><strong>Statistical Distribution Analysis:</strong>
- All features exhibit continuous distributions appropriate for machine learning
- No extreme outliers requiring removal or transformation
- Reasonable variance across all parameters enabling effective model training</p>
<p><strong>Class Balance Assessment:</strong>
- Dataset 1 exhibits significant class imbalance (70.2% in dominant class)
- Dataset 2 shows improved balance through subgroup subdivision
- Stratified sampling strategies implemented to maintain class proportions</p>
<h3 id="24-preprocessing-pipeline">2.4 Preprocessing Pipeline</h3>
<p>The preprocessing pipeline implements several critical steps to optimize data quality for machine learning analysis:</p>
<p><strong>Data Loading and Validation:</strong></p>
<div class="codehilite"><pre><span></span><code><span class="c1"># Load datasets with comprehensive validation</span>
<span class="n">df_major</span> <span class="o">=</span> <span class="n">pd</span><span class="o">.</span><span class="n">read_csv</span><span class="p">(</span><span class="s1">&#39;data_diag.csv&#39;</span><span class="p">)</span>
<span class="n">df_subgroup</span> <span class="o">=</span> <span class="n">pd</span><span class="o">.</span><span class="n">read_csv</span><span class="p">(</span><span class="s1">&#39;data_diag_maj_sub.csv&#39;</span><span class="p">)</span>

<span class="c1"># Verify data integrity</span>
<span class="k">assert</span> <span class="n">df_major</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">==</span> <span class="n">df_subgroup</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>  <span class="c1"># Same sample size</span>
<span class="k">assert</span> <span class="n">df_major</span><span class="o">.</span><span class="n">isnull</span><span class="p">()</span><span class="o">.</span><span class="n">sum</span><span class="p">()</span><span class="o">.</span><span class="n">sum</span><span class="p">()</span> <span class="o">==</span> <span class="mi">0</span>  <span class="c1"># No missing values</span>
<span class="k">assert</span> <span class="n">df_subgroup</span><span class="o">.</span><span class="n">isnull</span><span class="p">()</span><span class="o">.</span><span class="n">sum</span><span class="p">()</span><span class="o">.</span><span class="n">sum</span><span class="p">()</span> <span class="o">==</span> <span class="mi">0</span>  <span class="c1"># No missing values</span>
</code></pre></div>

<p><strong>Feature-Target Separation:</strong></p>
<div class="codehilite"><pre><span></span><code><span class="c1"># Separate features and targets for both datasets</span>
<span class="n">X_major</span> <span class="o">=</span> <span class="n">df_major</span><span class="o">.</span><span class="n">drop</span><span class="p">(</span><span class="s1">&#39;Diagnosis&#39;</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
<span class="n">y_major</span> <span class="o">=</span> <span class="n">df_major</span><span class="p">[</span><span class="s1">&#39;Diagnosis&#39;</span><span class="p">]</span>
<span class="n">X_subgroup</span> <span class="o">=</span> <span class="n">df_subgroup</span><span class="o">.</span><span class="n">drop</span><span class="p">(</span><span class="s1">&#39;Diagnosis&#39;</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
<span class="n">y_subgroup</span> <span class="o">=</span> <span class="n">df_subgroup</span><span class="p">[</span><span class="s1">&#39;Diagnosis&#39;</span><span class="p">]</span>

<span class="c1"># Verify feature consistency</span>
<span class="k">assert</span> <span class="n">X_major</span><span class="o">.</span><span class="n">equals</span><span class="p">(</span><span class="n">X_subgroup</span><span class="p">)</span>  <span class="c1"># Identical feature data</span>
</code></pre></div>

<p><strong>Train-Test Split with Stratification:</strong></p>
<div class="codehilite"><pre><span></span><code><span class="c1"># Stratified splitting to maintain class proportions</span>
<span class="n">X_train_maj</span><span class="p">,</span> <span class="n">X_test_maj</span><span class="p">,</span> <span class="n">y_train_maj</span><span class="p">,</span> <span class="n">y_test_maj</span> <span class="o">=</span> <span class="n">train_test_split</span><span class="p">(</span>
    <span class="n">X_major</span><span class="p">,</span> <span class="n">y_major</span><span class="p">,</span> <span class="n">test_size</span><span class="o">=</span><span class="mf">0.2</span><span class="p">,</span> <span class="n">random_state</span><span class="o">=</span><span class="mi">42</span><span class="p">,</span> <span class="n">stratify</span><span class="o">=</span><span class="n">y_major</span>
<span class="p">)</span>

<span class="n">X_train_sub</span><span class="p">,</span> <span class="n">X_test_sub</span><span class="p">,</span> <span class="n">y_train_sub</span><span class="p">,</span> <span class="n">y_test_sub</span> <span class="o">=</span> <span class="n">train_test_split</span><span class="p">(</span>
    <span class="n">X_subgroup</span><span class="p">,</span> <span class="n">y_subgroup</span><span class="p">,</span> <span class="n">test_size</span><span class="o">=</span><span class="mf">0.2</span><span class="p">,</span> <span class="n">random_state</span><span class="o">=</span><span class="mi">42</span><span class="p">,</span> <span class="n">stratify</span><span class="o">=</span><span class="n">y_subgroup</span>
<span class="p">)</span>
</code></pre></div>

<p><strong>Feature Scaling Implementation:</strong></p>
<div class="codehilite"><pre><span></span><code><span class="c1"># StandardScaler for linear models</span>
<span class="n">scaler</span> <span class="o">=</span> <span class="n">StandardScaler</span><span class="p">()</span>
<span class="n">X_train_scaled</span> <span class="o">=</span> <span class="n">scaler</span><span class="o">.</span><span class="n">fit_transform</span><span class="p">(</span><span class="n">X_train</span><span class="p">)</span>
<span class="n">X_test_scaled</span> <span class="o">=</span> <span class="n">scaler</span><span class="o">.</span><span class="n">transform</span><span class="p">(</span><span class="n">X_test</span><span class="p">)</span>

<span class="c1"># Preserve original data for tree-based models</span>
<span class="n">X_train_original</span> <span class="o">=</span> <span class="n">X_train</span><span class="o">.</span><span class="n">copy</span><span class="p">()</span>
<span class="n">X_test_original</span> <span class="o">=</span> <span class="n">X_test</span><span class="o">.</span><span class="n">copy</span><span class="p">()</span>
</code></pre></div>

<h3 id="25-exploratory-data-analysis">2.5 Exploratory Data Analysis</h3>
<p>Comprehensive exploratory data analysis revealed several important characteristics of the cell population data:</p>
<p><strong>Correlation Structure:</strong>
- Moderate to high correlations observed within cell type groups
- Cross-cell-type correlations generally lower, indicating distinct measurement domains
- No perfect multicollinearity requiring feature removal</p>
<p><strong>Distribution Characteristics:</strong>
- Most features exhibit approximately normal distributions
- Some parameters show slight skewness, addressed through robust scaling
- Width parameters generally show higher variance than positional parameters</p>
<p><strong>Class Separation Potential:</strong>
- Visual inspection of feature distributions by class reveals separable patterns
- Neutrophil parameters show strongest discriminative potential
- Lymphocyte and monocyte parameters provide complementary information</p>
<p><strong>Feature Importance Preliminary Assessment:</strong>
- Positional parameters (X, Y, Z) generally more informative than width parameters
- NEY (Neutrophil Y-coordinate) emerges as highly discriminative
- Combined feature interactions likely crucial for optimal performance</p>
<p>This comprehensive preprocessing foundation ensures robust and reliable machine learning model development while maintaining data integrity and enabling reproducible results.</p>
<hr />
<h2 id="3-feature-engineering-methodology">3. Feature Engineering Methodology</h2>
<h3 id="31-rationale-for-advanced-feature-engineering">3.1 Rationale for Advanced Feature Engineering</h3>
<p>The raw cell population data, while informative, represents only the surface of the diagnostic potential contained within automated hematology analyzer measurements. Advanced feature engineering transforms these basic measurements into clinically meaningful parameters that capture the complex biological relationships underlying acute leukemia pathophysiology. Our approach systematically extracts statistical, relational, and geometric features that enhance the discriminative power of machine learning models.</p>
<p>The biological rationale for feature engineering stems from the understanding that acute leukemia fundamentally alters cellular morphology, size distribution, and population dynamics. These changes manifest in the cell population data as shifts in positional parameters, alterations in population spread (width parameters), and modified relationships between different cell types. By engineering features that capture these biological phenomena, we enable machine learning models to identify subtle patterns that may not be apparent in raw measurements.</p>
<h3 id="32-statistical-feature-engineering">3.2 Statistical Feature Engineering</h3>
<p>Statistical feature engineering focuses on extracting summary statistics that characterize the central tendency, variability, and distribution properties of cell populations. This approach recognizes that acute leukemia often affects not just individual cell characteristics but entire population distributions.</p>
<p><strong>Group-wise Statistical Features:</strong></p>
<p>For each cell type (Neutrophils, Lymphocytes, Monocytes), we compute comprehensive statistical summaries:</p>
<div class="codehilite"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">compute_statistical_features</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">cell_type_prefix</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Compute statistical features for a specific cell type</span>

<span class="sd">    Parameters:</span>
<span class="sd">    X: DataFrame containing cell population data</span>
<span class="sd">    cell_type_prefix: String (&#39;NE&#39;, &#39;LY&#39;, &#39;MO&#39;)</span>

<span class="sd">    Returns:</span>
<span class="sd">    Dictionary of statistical features</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="c1"># Identify features for this cell type</span>
    <span class="n">features</span> <span class="o">=</span> <span class="p">[</span><span class="n">col</span> <span class="k">for</span> <span class="n">col</span> <span class="ow">in</span> <span class="n">X</span><span class="o">.</span><span class="n">columns</span> <span class="k">if</span> <span class="n">col</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="n">cell_type_prefix</span><span class="p">)]</span>

    <span class="c1"># Compute statistical summaries</span>
    <span class="n">stats</span> <span class="o">=</span> <span class="p">{</span>
        <span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">cell_type_prefix</span><span class="si">}</span><span class="s1">_mean&#39;</span><span class="p">:</span> <span class="n">X</span><span class="p">[</span><span class="n">features</span><span class="p">]</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">),</span>
        <span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">cell_type_prefix</span><span class="si">}</span><span class="s1">_std&#39;</span><span class="p">:</span> <span class="n">X</span><span class="p">[</span><span class="n">features</span><span class="p">]</span><span class="o">.</span><span class="n">std</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">),</span>
        <span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">cell_type_prefix</span><span class="si">}</span><span class="s1">_max&#39;</span><span class="p">:</span> <span class="n">X</span><span class="p">[</span><span class="n">features</span><span class="p">]</span><span class="o">.</span><span class="n">max</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">),</span>
        <span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">cell_type_prefix</span><span class="si">}</span><span class="s1">_min&#39;</span><span class="p">:</span> <span class="n">X</span><span class="p">[</span><span class="n">features</span><span class="p">]</span><span class="o">.</span><span class="n">min</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">),</span>
        <span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">cell_type_prefix</span><span class="si">}</span><span class="s1">_range&#39;</span><span class="p">:</span> <span class="n">X</span><span class="p">[</span><span class="n">features</span><span class="p">]</span><span class="o">.</span><span class="n">max</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span> <span class="o">-</span> <span class="n">X</span><span class="p">[</span><span class="n">features</span><span class="p">]</span><span class="o">.</span><span class="n">min</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">),</span>
        <span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">cell_type_prefix</span><span class="si">}</span><span class="s1">_cv&#39;</span><span class="p">:</span> <span class="n">X</span><span class="p">[</span><span class="n">features</span><span class="p">]</span><span class="o">.</span><span class="n">std</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span> <span class="o">/</span> <span class="p">(</span><span class="n">X</span><span class="p">[</span><span class="n">features</span><span class="p">]</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span> <span class="o">+</span> <span class="mf">1e-8</span><span class="p">)</span>
    <span class="p">}</span>

    <span class="k">return</span> <span class="n">stats</span>
</code></pre></div>

<p><strong>Mean Features:</strong> The arithmetic mean of all parameters within a cell type provides a central tendency measure that reflects the overall population characteristics. In acute leukemia, blast cells often exhibit altered mean values compared to normal mature cells.</p>
<p><strong>Standard Deviation Features:</strong> Population variability, captured through standard deviation, indicates the heterogeneity within cell populations. Acute leukemia frequently presents with increased cellular heterogeneity due to the presence of blast cells at various maturation stages.</p>
<p><strong>Range Features:</strong> The difference between maximum and minimum values within each cell type quantifies the span of cellular characteristics. Expanded ranges often indicate the presence of abnormal cell populations with extreme characteristics.</p>
<p><strong>Coefficient of Variation Features:</strong> The ratio of standard deviation to mean provides a normalized measure of variability that is independent of scale. This feature is particularly valuable for comparing variability across different cell types and measurement parameters.</p>
<h3 id="33-relational-feature-engineering">3.3 Relational Feature Engineering</h3>
<p>Relational features capture the interactions and proportional relationships between different cell types, reflecting the complex hematological changes that occur in acute leukemia. These features are based on the clinical understanding that leukemia affects not just individual cell populations but their relative proportions and relationships.</p>
<p><strong>Cross-Cell-Type Ratios:</strong></p>
<div class="codehilite"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">compute_relational_features</span><span class="p">(</span><span class="n">X_eng</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Compute relational features between cell types</span>

<span class="sd">    Parameters:</span>
<span class="sd">    X_eng: DataFrame with statistical features already computed</span>

<span class="sd">    Returns:</span>
<span class="sd">    Updated DataFrame with relational features</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="c1"># Neutrophil to Lymphocyte ratio</span>
    <span class="n">X_eng</span><span class="p">[</span><span class="s1">&#39;NE_LY_ratio&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">X_eng</span><span class="p">[</span><span class="s1">&#39;NE_mean&#39;</span><span class="p">]</span> <span class="o">/</span> <span class="p">(</span><span class="n">X_eng</span><span class="p">[</span><span class="s1">&#39;LY_mean&#39;</span><span class="p">]</span> <span class="o">+</span> <span class="mf">1e-8</span><span class="p">)</span>

    <span class="c1"># Neutrophil to Monocyte ratio</span>
    <span class="n">X_eng</span><span class="p">[</span><span class="s1">&#39;NE_MO_ratio&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">X_eng</span><span class="p">[</span><span class="s1">&#39;NE_mean&#39;</span><span class="p">]</span> <span class="o">/</span> <span class="p">(</span><span class="n">X_eng</span><span class="p">[</span><span class="s1">&#39;MO_mean&#39;</span><span class="p">]</span> <span class="o">+</span> <span class="mf">1e-8</span><span class="p">)</span>

    <span class="c1"># Lymphocyte to Monocyte ratio</span>
    <span class="n">X_eng</span><span class="p">[</span><span class="s1">&#39;LY_MO_ratio&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">X_eng</span><span class="p">[</span><span class="s1">&#39;LY_mean&#39;</span><span class="p">]</span> <span class="o">/</span> <span class="p">(</span><span class="n">X_eng</span><span class="p">[</span><span class="s1">&#39;MO_mean&#39;</span><span class="p">]</span> <span class="o">+</span> <span class="mf">1e-8</span><span class="p">)</span>

    <span class="k">return</span> <span class="n">X_eng</span>
</code></pre></div>

<p><strong>Neutrophil-Lymphocyte Ratio (NLR):</strong> This ratio has established clinical significance in hematology and oncology, often serving as a prognostic marker in various malignancies. In acute leukemia, altered NLR can indicate disease presence and severity.</p>
<p><strong>Neutrophil-Monocyte Ratio (NMR):</strong> The relationship between neutrophils }}"&gt;
    and monocytes provides insights into myeloid lineage abnormalities, which are particularly relevant in acute myeloid leukemia.</p>
<p><strong>Lymphocyte-Monocyte Ratio (LMR):</strong> This ratio captures lymphoid-myeloid balance, which can be disrupted in both acute lymphoblastic and myeloid leukemias.</p>
<h3 id="34-geometric-feature-engineering">3.4 Geometric Feature Engineering</h3>
<p>Geometric features exploit the spatial relationships inherent in the three-dimensional cell population data. The X, Y, and Z coordinates represent different cellular characteristics measured by the analyzer, and their geometric relationships can reveal important diagnostic information.</p>
<p><strong>Magnitude Calculations:</strong></p>
<div class="codehilite"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">compute_geometric_features</span><span class="p">(</span><span class="n">X</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Compute geometric features from positional coordinates</span>

<span class="sd">    Parameters:</span>
<span class="sd">    X: DataFrame containing original cell population data</span>

<span class="sd">    Returns:</span>
<span class="sd">    Dictionary of geometric features</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">geometric_features</span> <span class="o">=</span> <span class="p">{}</span>

    <span class="c1"># Compute magnitude for each cell type</span>
    <span class="k">for</span> <span class="n">cell_type</span> <span class="ow">in</span> <span class="p">[</span><span class="s1">&#39;NE&#39;</span><span class="p">,</span> <span class="s1">&#39;LY&#39;</span><span class="p">,</span> <span class="s1">&#39;MO&#39;</span><span class="p">]:</span>
        <span class="n">x_col</span> <span class="o">=</span> <span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">cell_type</span><span class="si">}</span><span class="s1">X&#39;</span>
        <span class="n">y_col</span> <span class="o">=</span> <span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">cell_type</span><span class="si">}</span><span class="s1">Y&#39;</span>
        <span class="n">z_col</span> <span class="o">=</span> <span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">cell_type</span><span class="si">}</span><span class="s1">Z&#39;</span>

        <span class="n">magnitude</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">sqrt</span><span class="p">(</span><span class="n">X</span><span class="p">[</span><span class="n">x_col</span><span class="p">]</span><span class="o">**</span><span class="mi">2</span> <span class="o">+</span> <span class="n">X</span><span class="p">[</span><span class="n">y_col</span><span class="p">]</span><span class="o">**</span><span class="mi">2</span> <span class="o">+</span> <span class="n">X</span><span class="p">[</span><span class="n">z_col</span><span class="p">]</span><span class="o">**</span><span class="mi">2</span><span class="p">)</span>
        <span class="n">geometric_features</span><span class="p">[</span><span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">cell_type</span><span class="si">}</span><span class="s1">_magnitude&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">magnitude</span>

    <span class="k">return</span> <span class="n">geometric_features</span>
</code></pre></div>

<p><strong>Euclidean Distance from Origin:</strong> The magnitude calculation provides the Euclidean distance of each cell population from the origin in three-dimensional space. This feature captures the overall "intensity" or "abnormality" of cellular characteristics, as normal cells typically cluster near specific regions while abnormal cells may be displaced.</p>
<p><strong>Spatial Clustering Indicators:</strong> The geometric features help identify whether cell populations maintain their normal spatial relationships or exhibit displacement patterns characteristic of malignant transformation.</p>
<h3 id="35-feature-engineering-pipeline-implementation">3.5 Feature Engineering Pipeline Implementation</h3>
<p>The complete feature engineering pipeline integrates all three approaches into a cohesive transformation process:</p>
<div class="codehilite"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">enhanced_feature_engineering</span><span class="p">(</span><span class="n">X</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Complete feature engineering pipeline</span>

<span class="sd">    Parameters:</span>
<span class="sd">    X: Original DataFrame with 18 cell population parameters</span>

<span class="sd">    Returns:</span>
<span class="sd">    Enhanced DataFrame with engineered features</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">X_eng</span> <span class="o">=</span> <span class="n">X</span><span class="o">.</span><span class="n">copy</span><span class="p">()</span>

    <span class="c1"># Statistical features for each cell type</span>
    <span class="k">for</span> <span class="n">cell_type</span> <span class="ow">in</span> <span class="p">[</span><span class="s1">&#39;NE&#39;</span><span class="p">,</span> <span class="s1">&#39;LY&#39;</span><span class="p">,</span> <span class="s1">&#39;MO&#39;</span><span class="p">]:</span>
        <span class="n">stats</span> <span class="o">=</span> <span class="n">compute_statistical_features</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">cell_type</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">feature_name</span><span class="p">,</span> <span class="n">feature_values</span> <span class="ow">in</span> <span class="n">stats</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
            <span class="n">X_eng</span><span class="p">[</span><span class="n">feature_name</span><span class="p">]</span> <span class="o">=</span> <span class="n">feature_values</span>

    <span class="c1"># Relational features</span>
    <span class="n">X_eng</span> <span class="o">=</span> <span class="n">compute_relational_features</span><span class="p">(</span><span class="n">X_eng</span><span class="p">)</span>

    <span class="c1"># Geometric features</span>
    <span class="n">geometric</span> <span class="o">=</span> <span class="n">compute_geometric_features</span><span class="p">(</span><span class="n">X</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">feature_name</span><span class="p">,</span> <span class="n">feature_values</span> <span class="ow">in</span> <span class="n">geometric</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
        <span class="n">X_eng</span><span class="p">[</span><span class="n">feature_name</span><span class="p">]</span> <span class="o">=</span> <span class="n">feature_values</span>

    <span class="k">return</span> <span class="n">X_eng</span>
</code></pre></div>

<h3 id="36-feature-engineering-validation">3.6 Feature Engineering Validation</h3>
<p>The effectiveness of feature engineering is validated through multiple approaches:</p>
<p><strong>Dimensionality Analysis:</strong> The transformation increases the feature space from 18 to 42 parameters, representing a 133% increase in dimensionality. This expansion provides machine learning models with richer information while remaining computationally manageable.</p>
<p><strong>Feature Importance Assessment:</strong> Preliminary analysis using tree-based models reveals that engineered features consistently rank among the most important predictors, validating their discriminative value.</p>
<p><strong>Correlation Analysis:</strong> Engineered features exhibit appropriate correlation structures, with statistical features showing expected relationships while maintaining sufficient independence for model training.</p>
<p><strong>Clinical Relevance Validation:</strong> The engineered features align with established hematological knowledge, ensuring that the machine learning models learn clinically meaningful patterns rather than spurious correlations.</p>
<p>This comprehensive feature engineering approach transforms raw cell population data into a rich feature set that captures the multifaceted nature of acute leukemia pathophysiology, providing machine learning models with the information necessary for accurate and reliable diagnosis.</p>
<hr />
<h2 id="4-machine-learning-models-and-algorithms">4. Machine Learning Models and Algorithms</h2>
<h3 id="41-model-selection-strategy">4.1 Model Selection Strategy</h3>
<p>The selection of machine learning algorithms for acute leukemia diagnosis requires careful consideration of multiple factors including interpretability, performance, computational efficiency, and clinical applicability. Our comprehensive approach evaluates diverse algorithmic families to identify optimal solutions for different diagnostic scenarios.</p>
<p>The model selection strategy encompasses three primary categories: tree-based ensemble methods for their inherent interpretability and robust performance with heterogeneous data, linear models for their computational efficiency and statistical interpretability, and support vector machines for their theoretical foundation and performance in high-dimensional spaces. This diverse portfolio ensures comprehensive evaluation while addressing different clinical deployment scenarios.</p>
<h3 id="42-tree-based-ensemble-methods">4.2 Tree-Based Ensemble Methods</h3>
<p>Tree-based ensemble methods form the cornerstone of our modeling approach due to their exceptional performance with tabular data, natural handling of feature interactions, and inherent interpretability. These methods are particularly well-suited for medical diagnosis applications where understanding the decision-making process is crucial for clinical acceptance.</p>
<p><strong>Random Forest Classifier:</strong></p>
<p>Random Forest represents one of the most robust and widely-used ensemble methods in medical machine learning applications. The algorithm constructs multiple decision trees using bootstrap sampling and random feature selection, combining their predictions through majority voting.</p>
<div class="codehilite"><pre><span></span><code><span class="k">class</span><span class="w"> </span><span class="nc">RandomForestConfig</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Configuration for Random Forest classifier&quot;&quot;&quot;</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">n_estimators</span> <span class="o">=</span> <span class="mi">100</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">max_depth</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">min_samples_split</span> <span class="o">=</span> <span class="mi">2</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">min_samples_leaf</span> <span class="o">=</span> <span class="mi">1</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">max_features</span> <span class="o">=</span> <span class="s1">&#39;sqrt&#39;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">bootstrap</span> <span class="o">=</span> <span class="kc">True</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">random_state</span> <span class="o">=</span> <span class="mi">42</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">n_jobs</span> <span class="o">=</span> <span class="o">-</span><span class="mi">1</span>

<span class="k">def</span><span class="w"> </span><span class="nf">train_random_forest</span><span class="p">(</span><span class="n">X_train</span><span class="p">,</span> <span class="n">y_train</span><span class="p">,</span> <span class="n">config</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Train Random Forest classifier with specified configuration</span>

<span class="sd">    Parameters:</span>
<span class="sd">    X_train: Training features</span>
<span class="sd">    y_train: Training labels</span>
<span class="sd">    config: RandomForestConfig object</span>

<span class="sd">    Returns:</span>
<span class="sd">    Trained Random Forest model</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">rf_model</span> <span class="o">=</span> <span class="n">RandomForestClassifier</span><span class="p">(</span>
        <span class="n">n_estimators</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">n_estimators</span><span class="p">,</span>
        <span class="n">max_depth</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">max_depth</span><span class="p">,</span>
        <span class="n">min_samples_split</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">min_samples_split</span><span class="p">,</span>
        <span class="n">min_samples_leaf</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">min_samples_leaf</span><span class="p">,</span>
        <span class="n">max_features</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">max_features</span><span class="p">,</span>
        <span class="n">bootstrap</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">bootstrap</span><span class="p">,</span>
        <span class="n">random_state</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">random_state</span><span class="p">,</span>
        <span class="n">n_jobs</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">n_jobs</span>
    <span class="p">)</span>

    <span class="n">rf_model</span><span class="o">.</span><span class="n">fit</span><span class="p">(</span><span class="n">X_train</span><span class="p">,</span> <span class="n">y_train</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">rf_model</span>
</code></pre></div>

<p>The Random Forest algorithm offers several advantages for acute leukemia diagnosis. Its ensemble nature provides robustness against overfitting, particularly important given the relatively small dataset size. The built-in feature importance calculation enables identification of the most diagnostically relevant cell population parameters. Additionally, the algorithm handles mixed data types naturally and requires minimal preprocessing.</p>
<p><strong>XGBoost (Extreme Gradient Boosting):</strong></p>
<p>XGBoost represents the state-of-the-art in gradient boosting algorithms, offering superior performance through advanced regularization techniques and optimized implementation. The algorithm builds models sequentially, with each new model correcting errors made by previous models.</p>
<div class="codehilite"><pre><span></span><code><span class="k">class</span><span class="w"> </span><span class="nc">XGBoostConfig</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Configuration for XGBoost classifier&quot;&quot;&quot;</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">n_estimators</span> <span class="o">=</span> <span class="mi">100</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">max_depth</span> <span class="o">=</span> <span class="mi">6</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">learning_rate</span> <span class="o">=</span> <span class="mf">0.1</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">subsample</span> <span class="o">=</span> <span class="mf">0.8</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">colsample_bytree</span> <span class="o">=</span> <span class="mf">0.8</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">reg_alpha</span> <span class="o">=</span> <span class="mf">0.1</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">reg_lambda</span> <span class="o">=</span> <span class="mf">1.0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">random_state</span> <span class="o">=</span> <span class="mi">42</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">eval_metric</span> <span class="o">=</span> <span class="s1">&#39;mlogloss&#39;</span>

<span class="k">def</span><span class="w"> </span><span class="nf">train_xgboost</span><span class="p">(</span><span class="n">X_train</span><span class="p">,</span> <span class="n">y_train</span><span class="p">,</span> <span class="n">config</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Train XGBoost classifier with specified configuration</span>

<span class="sd">    Parameters:</span>
<span class="sd">    X_train: Training features</span>
<span class="sd">    y_train: Training labels</span>
<span class="sd">    config: XGBoostConfig object</span>

<span class="sd">    Returns:</span>
<span class="sd">    Trained XGBoost model</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">xgb_model</span> <span class="o">=</span> <span class="n">xgb</span><span class="o">.</span><span class="n">XGBClassifier</span><span class="p">(</span>
        <span class="n">n_estimators</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">n_estimators</span><span class="p">,</span>
        <span class="n">max_depth</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">max_depth</span><span class="p">,</span>
        <span class="n">learning_rate</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">learning_rate</span><span class="p">,</span>
        <span class="n">subsample</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">subsample</span><span class="p">,</span>
        <span class="n">colsample_bytree</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">colsample_bytree</span><span class="p">,</span>
        <span class="n">reg_alpha</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">reg_alpha</span><span class="p">,</span>
        <span class="n">reg_lambda</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">reg_lambda</span><span class="p">,</span>
        <span class="n">random_state</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">random_state</span><span class="p">,</span>
        <span class="n">eval_metric</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">eval_metric</span>
    <span class="p">)</span>

    <span class="n">xgb_model</span><span class="o">.</span><span class="n">fit</span><span class="p">(</span><span class="n">X_train</span><span class="p">,</span> <span class="n">y_train</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">xgb_model</span>
</code></pre></div>

<p>XGBoost excels in capturing complex non-linear relationships within the cell population data. Its regularization parameters prevent overfitting while maintaining high predictive accuracy. The algorithm's ability to handle missing values and provide feature importance rankings makes it particularly suitable for medical applications.</p>
<p><strong>CatBoost (Categorical Boosting):</strong></p>
<p>CatBoost offers advanced gradient boosting with built-in categorical feature handling and reduced overfitting through innovative techniques. While our dataset contains only numerical features, CatBoost's robust regularization and performance characteristics make it valuable for comparison.</p>
<div class="codehilite"><pre><span></span><code><span class="k">class</span><span class="w"> </span><span class="nc">CatBoostConfig</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Configuration for CatBoost classifier&quot;&quot;&quot;</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">iterations</span> <span class="o">=</span> <span class="mi">100</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">depth</span> <span class="o">=</span> <span class="mi">6</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">learning_rate</span> <span class="o">=</span> <span class="mf">0.1</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">l2_leaf_reg</span> <span class="o">=</span> <span class="mf">3.0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">random_state</span> <span class="o">=</span> <span class="mi">42</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">verbose</span> <span class="o">=</span> <span class="kc">False</span>

<span class="k">def</span><span class="w"> </span><span class="nf">train_catboost</span><span class="p">(</span><span class="n">X_train</span><span class="p">,</span> <span class="n">y_train</span><span class="p">,</span> <span class="n">config</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Train CatBoost classifier with specified configuration</span>

<span class="sd">    Parameters:</span>
<span class="sd">    X_train: Training features</span>
<span class="sd">    y_train: Training labels</span>
<span class="sd">    config: CatBoostConfig object</span>

<span class="sd">    Returns:</span>
<span class="sd">    Trained CatBoost model</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cb_model</span> <span class="o">=</span> <span class="n">CatBoostClassifier</span><span class="p">(</span>
        <span class="n">iterations</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">iterations</span><span class="p">,</span>
        <span class="n">depth</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">depth</span><span class="p">,</span>
        <span class="n">learning_rate</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">learning_rate</span><span class="p">,</span>
        <span class="n">l2_leaf_reg</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">l2_leaf_reg</span><span class="p">,</span>
        <span class="n">random_state</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">random_state</span><span class="p">,</span>
        <span class="n">verbose</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">verbose</span>
    <span class="p">)</span>

    <span class="n">cb_model</span><span class="o">.</span><span class="n">fit</span><span class="p">(</span><span class="n">X_train</span><span class="p">,</span> <span class="n">y_train</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">cb_model</span>
</code></pre></div>

<p><strong>Gradient Boosting Classifier:</strong></p>
<p>The scikit-learn Gradient Boosting implementation provides a robust baseline for comparison with more advanced boosting algorithms. Its conservative approach and extensive validation make it suitable for medical applications requiring high reliability.</p>
<h3 id="43-linear-models">4.3 Linear Models</h3>
<p>Linear models offer computational efficiency, statistical interpretability, and theoretical foundation that make them valuable for medical diagnosis applications. Their coefficient-based interpretability aligns well with clinical decision-making processes.</p>
<p><strong>Logistic Regression:</strong></p>
<p>Logistic regression serves as both a baseline model and a clinically interpretable solution for acute leukemia diagnosis. The model's probabilistic output and coefficient interpretability make it particularly suitable for clinical applications.</p>
<div class="codehilite"><pre><span></span><code><span class="k">class</span><span class="w"> </span><span class="nc">LogisticRegressionConfig</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Configuration for Logistic Regression classifier&quot;&quot;&quot;</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">C</span> <span class="o">=</span> <span class="mf">1.0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">penalty</span> <span class="o">=</span> <span class="s1">&#39;l2&#39;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">solver</span> <span class="o">=</span> <span class="s1">&#39;liblinear&#39;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">max_iter</span> <span class="o">=</span> <span class="mi">1000</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">random_state</span> <span class="o">=</span> <span class="mi">42</span>

<span class="k">def</span><span class="w"> </span><span class="nf">train_logistic_regression</span><span class="p">(</span><span class="n">X_train</span><span class="p">,</span> <span class="n">y_train</span><span class="p">,</span> <span class="n">config</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Train Logistic Regression classifier with specified configuration</span>

<span class="sd">    Parameters:</span>
<span class="sd">    X_train: Training features (should be scaled)</span>
<span class="sd">    y_train: Training labels</span>
<span class="sd">    config: LogisticRegressionConfig object</span>

<span class="sd">    Returns:</span>
<span class="sd">    Trained Logistic Regression model</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">lr_model</span> <span class="o">=</span> <span class="n">LogisticRegression</span><span class="p">(</span>
        <span class="n">C</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">C</span><span class="p">,</span>
        <span class="n">penalty</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">penalty</span><span class="p">,</span>
        <span class="n">solver</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">solver</span><span class="p">,</span>
        <span class="n">max_iter</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">max_iter</span><span class="p">,</span>
        <span class="n">random_state</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">random_state</span>
    <span class="p">)</span>

    <span class="n">lr_model</span><span class="o">.</span><span class="n">fit</span><span class="p">(</span><span class="n">X_train</span><span class="p">,</span> <span class="n">y_train</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">lr_model</span>
</code></pre></div>

<p>The logistic regression model provides direct interpretation through coefficient analysis, enabling clinicians to understand which cell population parameters contribute most strongly to diagnostic decisions. The model's probabilistic output facilitates risk stratification and clinical decision-making.</p>
<h3 id="44-support-vector-machines">4.4 Support Vector Machines</h3>
<p>Support Vector Machines offer theoretical rigor and excellent performance in high-dimensional spaces, making them suitable for complex diagnostic tasks. The SVM approach finds optimal decision boundaries that maximize separation between diagnostic classes.</p>
<div class="codehilite"><pre><span></span><code><span class="k">class</span><span class="w"> </span><span class="nc">SVMConfig</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Configuration for Support Vector Machine classifier&quot;&quot;&quot;</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">C</span> <span class="o">=</span> <span class="mf">1.0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">kernel</span> <span class="o">=</span> <span class="s1">&#39;rbf&#39;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">gamma</span> <span class="o">=</span> <span class="s1">&#39;scale&#39;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">probability</span> <span class="o">=</span> <span class="kc">True</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">random_state</span> <span class="o">=</span> <span class="mi">42</span>

<span class="k">def</span><span class="w"> </span><span class="nf">train_svm</span><span class="p">(</span><span class="n">X_train</span><span class="p">,</span> <span class="n">y_train</span><span class="p">,</span> <span class="n">config</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Train SVM classifier with specified configuration</span>

<span class="sd">    Parameters:</span>
<span class="sd">    X_train: Training features (should be scaled)</span>
<span class="sd">    y_train: Training labels</span>
<span class="sd">    config: SVMConfig object</span>

<span class="sd">    Returns:</span>
<span class="sd">    Trained SVM model</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">svm_model</span> <span class="o">=</span> <span class="n">SVC</span><span class="p">(</span>
        <span class="n">C</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">C</span><span class="p">,</span>
        <span class="n">kernel</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">kernel</span><span class="p">,</span>
        <span class="n">gamma</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">gamma</span><span class="p">,</span>
        <span class="n">probability</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">probability</span><span class="p">,</span>
        <span class="n">random_state</span><span class="o">=</span><span class="n">config</span><span class="o">.</span><span class="n">random_state</span>
    <span class="p">)</span>

    <span class="n">svm_model</span><span class="o">.</span><span class="n">fit</span><span class="p">(</span><span class="n">X_train</span><span class="p">,</span> <span class="n">y_train</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">svm_model</span>
</code></pre></div>

<p>The RBF kernel enables SVM to capture complex non-linear relationships within the cell population data. The probability estimation capability provides clinically useful confidence measures for diagnostic predictions.</p>
<h3 id="45-model-training-pipeline">4.5 Model Training Pipeline</h3>
<p>The comprehensive model training pipeline ensures consistent evaluation across all algorithms while accommodating their specific requirements:</p>
<div class="codehilite"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">train_all_models</span><span class="p">(</span><span class="n">X_train</span><span class="p">,</span> <span class="n">X_train_scaled</span><span class="p">,</span> <span class="n">y_train</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Train all models with appropriate data preprocessing</span>

<span class="sd">    Parameters:</span>
<span class="sd">    X_train: Original training features</span>
<span class="sd">    X_train_scaled: Scaled training features</span>
<span class="sd">    y_train: Training labels</span>

<span class="sd">    Returns:</span>
<span class="sd">    Dictionary of trained models</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">models</span> <span class="o">=</span> <span class="p">{}</span>

    <span class="c1"># Tree-based models (use original features)</span>
    <span class="n">models</span><span class="p">[</span><span class="s1">&#39;Random Forest&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">train_random_forest</span><span class="p">(</span><span class="n">X_train</span><span class="p">,</span> <span class="n">y_train</span><span class="p">,</span> <span class="n">RandomForestConfig</span><span class="p">())</span>
    <span class="n">models</span><span class="p">[</span><span class="s1">&#39;XGBoost&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">train_xgboost</span><span class="p">(</span><span class="n">X_train</span><span class="p">,</span> <span class="n">y_train</span><span class="p">,</span> <span class="n">XGBoostConfig</span><span class="p">())</span>
    <span class="n">models</span><span class="p">[</span><span class="s1">&#39;CatBoost&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">train_catboost</span><span class="p">(</span><span class="n">X_train</span><span class="p">,</span> <span class="n">y_train</span><span class="p">,</span> <span class="n">CatBoostConfig</span><span class="p">())</span>
    <span class="n">models</span><span class="p">[</span><span class="s1">&#39;Gradient Boosting&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">train_gradient_boosting</span><span class="p">(</span><span class="n">X_train</span><span class="p">,</span> <span class="n">y_train</span><span class="p">,</span> <span class="n">GradientBoostingConfig</span><span class="p">())</span>

    <span class="c1"># Linear models (use scaled features)</span>
    <span class="n">models</span><span class="p">[</span><span class="s1">&#39;Logistic Regression&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">train_logistic_regression</span><span class="p">(</span><span class="n">X_train_scaled</span><span class="p">,</span> <span class="n">y_train</span><span class="p">,</span> <span class="n">LogisticRegressionConfig</span><span class="p">())</span>
    <span class="n">models</span><span class="p">[</span><span class="s1">&#39;SVM&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">train_svm</span><span class="p">(</span><span class="n">X_train_scaled</span><span class="p">,</span> <span class="n">y_train</span><span class="p">,</span> <span class="n">SVMConfig</span><span class="p">())</span>

    <span class="k">return</span> <span class="n">models</span>
</code></pre></div>

<h3 id="46-cross-validation-strategy">4.6 Cross-Validation Strategy</h3>
<p>Robust model evaluation requires comprehensive cross-validation to ensure reliable performance estimates. Our approach implements stratified k-fold cross-validation to maintain class proportions across folds.</p>
<div class="codehilite"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">perform_cross_validation</span><span class="p">(</span><span class="n">models</span><span class="p">,</span> <span class="n">X_train</span><span class="p">,</span> <span class="n">X_train_scaled</span><span class="p">,</span> <span class="n">y_train</span><span class="p">,</span> <span class="n">cv_folds</span><span class="o">=</span><span class="mi">5</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Perform stratified cross-validation for all models</span>

<span class="sd">    Parameters:</span>
<span class="sd">    models: Dictionary of trained models</span>
<span class="sd">    X_train: Original training features</span>
<span class="sd">    X_train_scaled: Scaled training features</span>
<span class="sd">    y_train: Training labels</span>
<span class="sd">    cv_folds: Number of cross-validation folds</span>

<span class="sd">    Returns:</span>
<span class="sd">    Dictionary of cross-validation results</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">cv_results</span> <span class="o">=</span> <span class="p">{}</span>
    <span class="n">skf</span> <span class="o">=</span> <span class="n">StratifiedKFold</span><span class="p">(</span><span class="n">n_splits</span><span class="o">=</span><span class="n">cv_folds</span><span class="p">,</span> <span class="n">shuffle</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">random_state</span><span class="o">=</span><span class="mi">42</span><span class="p">)</span>

    <span class="k">for</span> <span class="n">model_name</span><span class="p">,</span> <span class="n">model</span> <span class="ow">in</span> <span class="n">models</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
        <span class="c1"># Select appropriate feature set</span>
        <span class="n">X_cv</span> <span class="o">=</span> <span class="n">X_train_scaled</span> <span class="k">if</span> <span class="n">model_name</span> <span class="ow">in</span> <span class="p">[</span><span class="s1">&#39;Logistic Regression&#39;</span><span class="p">,</span> <span class="s1">&#39;SVM&#39;</span><span class="p">]</span> <span class="k">else</span> <span class="n">X_train</span>

        <span class="c1"># Perform cross-validation</span>
        <span class="n">cv_scores</span> <span class="o">=</span> <span class="n">cross_val_score</span><span class="p">(</span><span class="n">model</span><span class="p">,</span> <span class="n">X_cv</span><span class="p">,</span> <span class="n">y_train</span><span class="p">,</span> <span class="n">cv</span><span class="o">=</span><span class="n">skf</span><span class="p">,</span> <span class="n">scoring</span><span class="o">=</span><span class="s1">&#39;accuracy&#39;</span><span class="p">)</span>

        <span class="n">cv_results</span><span class="p">[</span><span class="n">model_name</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s1">&#39;mean_accuracy&#39;</span><span class="p">:</span> <span class="n">cv_scores</span><span class="o">.</span><span class="n">mean</span><span class="p">(),</span>
            <span class="s1">&#39;std_accuracy&#39;</span><span class="p">:</span> <span class="n">cv_scores</span><span class="o">.</span><span class="n">std</span><span class="p">(),</span>
            <span class="s1">&#39;individual_scores&#39;</span><span class="p">:</span> <span class="n">cv_scores</span>
        <span class="p">}</span>

    <span class="k">return</span> <span class="n">cv_results</span>
</code></pre></div>

<p>This comprehensive modeling approach ensures thorough evaluation of diverse algorithmic approaches while maintaining rigorous validation standards appropriate for medical diagnosis applications.</p>
<hr />
<h2 id="5-statistical-analysis-and-confidence-intervals">5. Statistical Analysis and Confidence Intervals</h2>
<h3 id="51-bootstrap-methodology-for-auc-confidence-intervals">5.1 Bootstrap Methodology for AUC Confidence Intervals</h3>
<p>The calculation of confidence intervals for Area Under the Curve (AUC) metrics represents a critical component of robust statistical analysis in medical machine learning. Traditional asymptotic methods may not provide accurate confidence intervals for moderate sample sizes, making bootstrap methodology the preferred approach for reliable statistical inference.</p>
<p>Our bootstrap implementation generates empirical confidence intervals through resampling techniques that preserve the underlying data distribution while providing non-parametric estimates of sampling variability. This approach is particularly valuable for multi-class classification problems where traditional AUC calculations become complex.</p>
<div class="codehilite"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">calculate_auc_ci</span><span class="p">(</span><span class="n">y_true</span><span class="p">,</span> <span class="n">y_scores</span><span class="p">,</span> <span class="n">confidence</span><span class="o">=</span><span class="mf">0.95</span><span class="p">,</span> <span class="n">n_bootstrap</span><span class="o">=</span><span class="mi">1000</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Calculate bootstrap confidence intervals for AUC using One-vs-Rest approach</span>

<span class="sd">    Parameters:</span>
<span class="sd">    y_true: True class labels</span>
<span class="sd">    y_scores: Predicted class probabilities</span>
<span class="sd">    confidence: Confidence level (default 0.95 for 95% CI)</span>
<span class="sd">    n_bootstrap: Number of bootstrap samples</span>

<span class="sd">    Returns:</span>
<span class="sd">    Tuple of (lower_ci, upper_ci, bootstrap_aucs)</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">auc_statistic</span><span class="p">(</span><span class="n">y_true</span><span class="p">,</span> <span class="n">y_scores</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Calculate AUC using One-vs-Rest multi-class approach&quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="n">roc_auc_score</span><span class="p">(</span><span class="n">y_true</span><span class="p">,</span> <span class="n">y_scores</span><span class="p">,</span> <span class="n">multi_class</span><span class="o">=</span><span class="s1">&#39;ovr&#39;</span><span class="p">,</span> <span class="n">average</span><span class="o">=</span><span class="s1">&#39;macro&#39;</span><span class="p">)</span>

    <span class="c1"># Initialize bootstrap results</span>
    <span class="n">n_samples</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">y_true</span><span class="p">)</span>
    <span class="n">bootstrap_aucs</span> <span class="o">=</span> <span class="p">[]</span>

    <span class="c1"># Set random seed for reproducibility</span>
    <span class="n">np</span><span class="o">.</span><span class="n">random</span><span class="o">.</span><span class="n">seed</span><span class="p">(</span><span class="mi">42</span><span class="p">)</span>

    <span class="c1"># Generate bootstrap samples</span>
    <span class="k">for</span> <span class="n">iteration</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">n_bootstrap</span><span class="p">):</span>
        <span class="c1"># Bootstrap sample indices with replacement</span>
        <span class="n">indices</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">random</span><span class="o">.</span><span class="n">choice</span><span class="p">(</span><span class="n">n_samples</span><span class="p">,</span> <span class="n">size</span><span class="o">=</span><span class="n">n_samples</span><span class="p">,</span> <span class="n">replace</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
        <span class="n">y_true_boot</span> <span class="o">=</span> <span class="n">y_true</span><span class="p">[</span><span class="n">indices</span><span class="p">]</span>
        <span class="n">y_scores_boot</span> <span class="o">=</span> <span class="n">y_scores</span><span class="p">[</span><span class="n">indices</span><span class="p">]</span>

        <span class="k">try</span><span class="p">:</span>
            <span class="c1"># Calculate AUC for bootstrap sample</span>
            <span class="n">auc_boot</span> <span class="o">=</span> <span class="n">auc_statistic</span><span class="p">(</span><span class="n">y_true_boot</span><span class="p">,</span> <span class="n">y_scores_boot</span><span class="p">)</span>
            <span class="n">bootstrap_aucs</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">auc_boot</span><span class="p">)</span>
        <span class="k">except</span> <span class="ne">ValueError</span><span class="p">:</span>
            <span class="c1"># Handle cases where bootstrap sample lacks class diversity</span>
            <span class="k">continue</span>

    <span class="c1"># Calculate confidence interval using percentile method</span>
    <span class="n">alpha</span> <span class="o">=</span> <span class="mi">1</span> <span class="o">-</span> <span class="n">confidence</span>
    <span class="n">lower_percentile</span> <span class="o">=</span> <span class="p">(</span><span class="n">alpha</span><span class="o">/</span><span class="mi">2</span><span class="p">)</span> <span class="o">*</span> <span class="mi">100</span>
    <span class="n">upper_percentile</span> <span class="o">=</span> <span class="p">(</span><span class="mi">1</span> <span class="o">-</span> <span class="n">alpha</span><span class="o">/</span><span class="mi">2</span><span class="p">)</span> <span class="o">*</span> <span class="mi">100</span>

    <span class="n">ci_lower</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">percentile</span><span class="p">(</span><span class="n">bootstrap_aucs</span><span class="p">,</span> <span class="n">lower_percentile</span><span class="p">)</span>
    <span class="n">ci_upper</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">percentile</span><span class="p">(</span><span class="n">bootstrap_aucs</span><span class="p">,</span> <span class="n">upper_percentile</span><span class="p">)</span>

    <span class="k">return</span> <span class="n">ci_lower</span><span class="p">,</span> <span class="n">ci_upper</span><span class="p">,</span> <span class="n">bootstrap_aucs</span>
</code></pre></div>

<h3 id="52-multi-class-auc-calculation">5.2 Multi-Class AUC Calculation</h3>
<p>Multi-class classification presents unique challenges for AUC calculation, requiring careful consideration of class relationships and averaging strategies. Our implementation employs the One-vs-Rest (OvR) approach with macro-averaging to provide interpretable and clinically meaningful AUC values.</p>
<p><strong>One-vs-Rest Strategy:</strong></p>
<p>The OvR approach transforms the multi-class problem into multiple binary classification problems, calculating AUC for each class against all others. This method provides intuitive interpretation where each class's AUC represents its discriminability from all other classes.</p>
<div class="codehilite"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">calculate_multiclass_auc_detailed</span><span class="p">(</span><span class="n">y_true</span><span class="p">,</span> <span class="n">y_scores</span><span class="p">,</span> <span class="n">class_names</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Calculate detailed multi-class AUC metrics</span>

<span class="sd">    Parameters:</span>
<span class="sd">    y_true: True class labels</span>
<span class="sd">    y_scores: Predicted class probabilities (n_samples x n_classes)</span>
<span class="sd">    class_names: List of class names for interpretation</span>

<span class="sd">    Returns:</span>
<span class="sd">    Dictionary containing detailed AUC metrics</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">n_classes</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">class_names</span><span class="p">)</span>
    <span class="n">auc_results</span> <span class="o">=</span> <span class="p">{}</span>

    <span class="c1"># Calculate AUC for each class vs rest</span>
    <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">class_name</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">class_names</span><span class="p">):</span>
        <span class="c1"># Create binary labels (current class vs all others)</span>
        <span class="n">y_binary</span> <span class="o">=</span> <span class="p">(</span><span class="n">y_true</span> <span class="o">==</span> <span class="n">i</span><span class="p">)</span><span class="o">.</span><span class="n">astype</span><span class="p">(</span><span class="nb">int</span><span class="p">)</span>
        <span class="n">y_scores_binary</span> <span class="o">=</span> <span class="n">y_scores</span><span class="p">[:,</span> <span class="n">i</span><span class="p">]</span>

        <span class="c1"># Calculate binary AUC</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="n">auc_binary</span> <span class="o">=</span> <span class="n">roc_auc_score</span><span class="p">(</span><span class="n">y_binary</span><span class="p">,</span> <span class="n">y_scores_binary</span><span class="p">)</span>
            <span class="n">auc_results</span><span class="p">[</span><span class="sa">f</span><span class="s1">&#39;AUC_class_</span><span class="si">{</span><span class="n">class_name</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">auc_binary</span>
        <span class="k">except</span> <span class="ne">ValueError</span><span class="p">:</span>
            <span class="n">auc_results</span><span class="p">[</span><span class="sa">f</span><span class="s1">&#39;AUC_class_</span><span class="si">{</span><span class="n">class_name</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">nan</span>

    <span class="c1"># Calculate macro averaged AUC</span>
    <span class="n">valid_aucs</span> <span class="o">=</span> <span class="p">[</span><span class="n">auc</span> <span class="k">for</span> <span class="n">auc</span> <span class="ow">in</span> <span class="n">auc_results</span><span class="o">.</span><span class="n">values</span><span class="p">()</span> <span class="k">if</span> <span class="ow">not</span> <span class="n">np</span><span class="o">.</span><span class="n">isnan</span><span class="p">(</span><span class="n">auc</span><span class="p">)]</span>
    <span class="n">auc_results</span><span class="p">[</span><span class="s1">&#39;AUC_macro&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">valid_aucs</span><span class="p">)</span> <span class="k">if</span> <span class="n">valid_aucs</span> <span class="k">else</span> <span class="n">np</span><span class="o">.</span><span class="n">nan</span>

    <span class="c1"># Calculate overall multi-class AUC using sklearn</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">auc_results</span><span class="p">[</span><span class="s1">&#39;AUC_ovr_macro&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">roc_auc_score</span><span class="p">(</span><span class="n">y_true</span><span class="p">,</span> <span class="n">y_scores</span><span class="p">,</span> 
                                                    <span class="n">multi_class</span><span class="o">=</span><span class="s1">&#39;ovr&#39;</span><span class="p">,</span> 
                                                    <span class="n">average</span><span class="o">=</span><span class="s1">&#39;macro&#39;</span><span class="p">)</span>
    <span class="k">except</span> <span class="ne">ValueError</span><span class="p">:</span>
        <span class="n">auc_results</span><span class="p">[</span><span class="s1">&#39;AUC_ovr_macro&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">nan</span>

    <span class="k">return</span> <span class="n">auc_results</span>
</code></pre></div>

<h3 id="53-statistical-significance-testing">5.3 Statistical Significance Testing</h3>
<p>Statistical significance testing provides a formal hypothesis testing framework for comparing model performance and validating diagnostic accuracy. Our approach implements multiple testing procedures to ensure robust statistical inference.</p>
<p><strong>Paired t-test for Model Comparison:</strong></p>
<div class="codehilite"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">compare_model_performance</span><span class="p">(</span><span class="n">results_dict</span><span class="p">,</span> <span class="n">metric</span><span class="o">=</span><span class="s1">&#39;accuracy&#39;</span><span class="p">,</span> <span class="n">alpha</span><span class="o">=</span><span class="mf">0.05</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Compare model performance using paired t-tests</span>

<span class="sd">    Parameters:</span>
<span class="sd">    results_dict: Dictionary containing cross-validation results for each model</span>
<span class="sd">    metric: Performance metric to compare</span>
<span class="sd">    alpha: Significance level</span>

<span class="sd">    Returns:</span>
<span class="sd">    Dictionary containing pairwise comparison results</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">scipy.stats</span><span class="w"> </span><span class="kn">import</span> <span class="n">ttest_rel</span>

    <span class="n">model_names</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">results_dict</span><span class="o">.</span><span class="n">keys</span><span class="p">())</span>
    <span class="n">comparison_results</span> <span class="o">=</span> <span class="p">{}</span>

    <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">model1</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">model_names</span><span class="p">):</span>
        <span class="k">for</span> <span class="n">j</span><span class="p">,</span> <span class="n">model2</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">model_names</span><span class="p">[</span><span class="n">i</span><span class="o">+</span><span class="mi">1</span><span class="p">:],</span> <span class="n">i</span><span class="o">+</span><span class="mi">1</span><span class="p">):</span>
            <span class="c1"># Extract cross-validation scores</span>
            <span class="n">scores1</span> <span class="o">=</span> <span class="n">results_dict</span><span class="p">[</span><span class="n">model1</span><span class="p">][</span><span class="s1">&#39;individual_scores&#39;</span><span class="p">]</span>
            <span class="n">scores2</span> <span class="o">=</span> <span class="n">results_dict</span><span class="p">[</span><span class="n">model2</span><span class="p">][</span><span class="s1">&#39;individual_scores&#39;</span><span class="p">]</span>

            <span class="c1"># Perform paired t-test</span>
            <span class="n">t_statistic</span><span class="p">,</span> <span class="n">p_value</span> <span class="o">=</span> <span class="n">ttest_rel</span><span class="p">(</span><span class="n">scores1</span><span class="p">,</span> <span class="n">scores2</span><span class="p">)</span>

            <span class="c1"># Determine significance</span>
            <span class="n">is_significant</span> <span class="o">=</span> <span class="n">p_value</span> <span class="o">&lt;</span> <span class="n">alpha</span>

            <span class="n">comparison_results</span><span class="p">[</span><span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">model1</span><span class="si">}</span><span class="s1">_vs_</span><span class="si">{</span><span class="n">model2</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span>
                <span class="s1">&#39;t_statistic&#39;</span><span class="p">:</span> <span class="n">t_statistic</span><span class="p">,</span>
                <span class="s1">&#39;p_value&#39;</span><span class="p">:</span> <span class="n">p_value</span><span class="p">,</span>
                <span class="s1">&#39;is_significant&#39;</span><span class="p">:</span> <span class="n">is_significant</span><span class="p">,</span>
                <span class="s1">&#39;mean_difference&#39;</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">scores1</span><span class="p">)</span> <span class="o">-</span> <span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">scores2</span><span class="p">)</span>
            <span class="p">}</span>

    <span class="k">return</span> <span class="n">comparison_results</span>
</code></pre></div>

<h3 id="54-effect-size-calculation">5.4 Effect Size Calculation</h3>
<p>Effect size measures provide practical significance assessment beyond statistical significance, indicating the magnitude of performance differences between models. Cohen's d serves as the primary effect size measure for model comparison.</p>
<div class="codehilite"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">calculate_effect_size</span><span class="p">(</span><span class="n">scores1</span><span class="p">,</span> <span class="n">scores2</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Calculate Cohen&#39;s d effect size between two sets of scores</span>

<span class="sd">    Parameters:</span>
<span class="sd">    scores1, scores2: Arrays of performance scores</span>

<span class="sd">    Returns:</span>
<span class="sd">    Cohen&#39;s d effect size</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="c1"># Calculate means</span>
    <span class="n">mean1</span><span class="p">,</span> <span class="n">mean2</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">scores1</span><span class="p">),</span> <span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">scores2</span><span class="p">)</span>

    <span class="c1"># Calculate pooled standard deviation</span>
    <span class="n">n1</span><span class="p">,</span> <span class="n">n2</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">scores1</span><span class="p">),</span> <span class="nb">len</span><span class="p">(</span><span class="n">scores2</span><span class="p">)</span>
    <span class="n">pooled_std</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">sqrt</span><span class="p">(((</span><span class="n">n1</span><span class="o">-</span><span class="mi">1</span><span class="p">)</span><span class="o">*</span><span class="n">np</span><span class="o">.</span><span class="n">var</span><span class="p">(</span><span class="n">scores1</span><span class="p">,</span> <span class="n">ddof</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span> <span class="o">+</span> <span class="p">(</span><span class="n">n2</span><span class="o">-</span><span class="mi">1</span><span class="p">)</span><span class="o">*</span><span class="n">np</span><span class="o">.</span><span class="n">var</span><span class="p">(</span><span class="n">scores2</span><span class="p">,</span> <span class="n">ddof</span><span class="o">=</span><span class="mi">1</span><span class="p">))</span> <span class="o">/</span> <span class="p">(</span><span class="n">n1</span><span class="o">+</span><span class="n">n2</span><span class="o">-</span><span class="mi">2</span><span class="p">))</span>

    <span class="c1"># Calculate Cohen&#39;s d</span>
    <span class="n">cohens_d</span> <span class="o">=</span> <span class="p">(</span><span class="n">mean1</span> <span class="o">-</span> <span class="n">mean2</span><span class="p">)</span> <span class="o">/</span> <span class="n">pooled_std</span>

    <span class="k">return</span> <span class="n">cohens_d</span>

<span class="k">def</span><span class="w"> </span><span class="nf">interpret_effect_size</span><span class="p">(</span><span class="n">cohens_d</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Interpret Cohen&#39;s d effect size magnitude</span>

<span class="sd">    Parameters:</span>
<span class="sd">    cohens_d: Cohen&#39;s d value</span>

<span class="sd">    Returns:</span>
<span class="sd">    String interpretation of effect size</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">abs_d</span> <span class="o">=</span> <span class="nb">abs</span><span class="p">(</span><span class="n">cohens_d</span><span class="p">)</span>

    <span class="k">if</span> <span class="n">abs_d</span> <span class="o">&lt;</span> <span class="mf">0.2</span><span class="p">:</span>
        <span class="k">return</span> <span class="s2">&quot;negligible&quot;</span>
    <span class="k">elif</span> <span class="n">abs_d</span> <span class="o">&lt;</span> <span class="mf">0.5</span><span class="p">:</span>
        <span class="k">return</span> <span class="s2">&quot;small&quot;</span>
    <span class="k">elif</span> <span class="n">abs_d</span> <span class="o">&lt;</span> <span class="mf">0.8</span><span class="p">:</span>
        <span class="k">return</span> <span class="s2">&quot;medium&quot;</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="k">return</span> <span class="s2">&quot;large&quot;</span>
</code></pre></div>

<h3 id="55-confidence-interval-visualization">5.5 Confidence Interval Visualization</h3>
<p>Visualization of confidence intervals provides intuitive understanding of statistical uncertainty and model reliability. Our implementation creates comprehensive visualizations that communicate statistical findings effectively.</p>
<div class="codehilite"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">plot_auc_with_confidence_intervals</span><span class="p">(</span><span class="n">results_df</span><span class="p">,</span> <span class="n">dataset_name</span><span class="p">,</span> <span class="n">output_dir</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Create visualization of AUC values with confidence intervals</span>

<span class="sd">    Parameters:</span>
<span class="sd">    results_df: DataFrame containing model results with CI information</span>
<span class="sd">    dataset_name: Name of dataset for plot title</span>
<span class="sd">    output_dir: Directory for saving plots</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">figure</span><span class="p">(</span><span class="n">figsize</span><span class="o">=</span><span class="p">(</span><span class="mi">12</span><span class="p">,</span> <span class="mi">8</span><span class="p">))</span>

    <span class="c1"># Extract data for plotting</span>
    <span class="n">models</span> <span class="o">=</span> <span class="n">results_df</span><span class="p">[</span><span class="s1">&#39;model_name&#39;</span><span class="p">]</span>
    <span class="n">aucs</span> <span class="o">=</span> <span class="n">results_df</span><span class="p">[</span><span class="s1">&#39;auc&#39;</span><span class="p">]</span>
    <span class="n">ci_lower</span> <span class="o">=</span> <span class="n">results_df</span><span class="p">[</span><span class="s1">&#39;auc_ci_lower&#39;</span><span class="p">]</span>
    <span class="n">ci_upper</span> <span class="o">=</span> <span class="n">results_df</span><span class="p">[</span><span class="s1">&#39;auc_ci_upper&#39;</span><span class="p">]</span>

    <span class="c1"># Calculate error bars</span>
    <span class="n">error_lower</span> <span class="o">=</span> <span class="n">aucs</span> <span class="o">-</span> <span class="n">ci_lower</span>
    <span class="n">error</span> <span class="n">nearly_upper</span> <span class="o">=</span> <span class="n">ci_upper</span> <span class="o">-</span> <span class="n">aucs</span>

    <span class="c1"># Create bar plot with error bars</span>
    <span class="n">bars</span> <span class="o">=</span> <span class="n">plt</span><span class="o">.</span><span class="n">bar</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">models</span><span class="p">)),</span> <span class="n">aucs</span><span class="p">,</span> <span class="n">alpha</span><span class="o">=</span><span class="mf">0.7</span><span class="p">,</span> <span class="n">color</span><span class="o">=</span><span class="s1">&#39;skyblue&#39;</span><span class="p">)</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">errorbar</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">models</span><span class="p">)),</span> <span class="n">aucs</span><span class="p">,</span> 
                <span class="n">yerr</span><span class="o">=</span><span class="p">[</span><span class="n">error_lower</span><span class="p">,</span> <span class="n">error_upper</span><span class="p">],</span>
                <span class="n">fmt</span><span class="o">=</span><span class="s1">&#39;none&#39;</span><span class="p">,</span> <span class="n">color</span><span class="o">=</span><span class="s1">&#39;black&#39;</span><span class="p">,</span> <span class="n">capsize</span><span class="o">=</span><span class="mi">5</span><span class="p">,</span> <span class="n">capthick</span><span class="o">=</span><span class="mi">2</span><span class="p">)</span>

    <span class="c1"># Customize plot</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">xlabel</span><span class="p">(</span><span class="s1">&#39;Machine Learning Models&#39;</span><span class="p">)</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">ylabel</span><span class="p">(</span><span class="s1">&#39;AUC (Area Under Curve)&#39;</span><span class="p">)</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">title</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;Model Performance with 95% Confidence Intervals</span><span class="se">\n</span><span class="s1">Dataset: </span><span class="si">{</span><span class="n">dataset_name</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">)</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">xticks</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">models</span><span class="p">)),</span> <span class="n">models</span><span class="p">,</span> <span class="n">rotation</span><span class="o">=</span><span class="mi">45</span><span class="p">,</span> <span class="n">ha</span><span class="o">=</span><span class="s1">&#39;right&#39;</span><span class="p">)</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">grid</span><span class="p">(</span><span class="kc">True</span><span class="p">,</span> <span class="n">alpha</span><span class="o">=</span><span class="mf">0.3</span><span class="p">)</span>

    <span class="c1"># Add value labels on bars</span>
    <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="p">(</span><span class="n">bar</span><span class="p">,</span> <span class="n">auc</span><span class="p">,</span> <span class="n">ci_l</span><span class="p">,</span> <span class="n">ci_u</span><span class="p">)</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="nb">zip</span><span class="p">(</span><span class="n">bars</span><span class="p">,</span> <span class="n">aucs</span><span class="p">,</span> <span class="n">ci_lower</span><span class="p">,</span> <span class="n">ci_upper</span><span class="p">)):</span>
        <span class="n">plt</span><span class="o">.</span><span class="n">text</span><span class="p">(</span><span class="n">bar</span><span class="o">.</span><span class="n">get_x</span><span class="p">()</span> <span class="o">+</span> <span class="n">bar</span><span class="o">.</span><span class="n">get_width</span><span class="p">()</span><span class="o">/</span><span class="mi">2</span><span class="p">,</span> <span class="n">bar</span><span class="o">.</span><span class="n">get_height</span><span class="p">()</span> <span class="o">+</span> <span class="mf">0.01</span><span class="p">,</span>
                <span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">auc</span><span class="si">:</span><span class="s1">.3f</span><span class="si">}</span><span class="se">\n</span><span class="s1">[</span><span class="si">{</span><span class="n">ci_l</span><span class="si">:</span><span class="s1">.3f</span><span class="si">}</span><span class="s1">, </span><span class="si">{</span><span class="n">ci_u</span><span class="si">:</span><span class="s1">.3f</span><span class="si">}</span><span class="s1">]&#39;</span><span class="p">,</span>
                <span class="n">ha</span><span class="o">=</span><span class="s1">&#39;center&#39;</span><span class="p">,</span> <span class="n">va</span><span class="o">=</span><span class="s1">&#39;bottom&#39;</span><span class="p">,</span> <span class="n">fontsize</span><span class="o">=</span><span class="mi">9</span><span class="p">)</span>

    <span class="n">plt</span><span class="o">.</span><span class="n">tight_layout</span><span class="p">()</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">savefig</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">output_dir</span><span class="si">}</span><span class="s1">/</span><span class="si">{</span><span class="n">dataset_name</span><span class="si">}</span><span class="s1">_auc_confidence_intervals.png&#39;</span><span class="p">,</span> 
               <span class="n">dpi</span><span class="o">=</span><span class="mi">300</span><span class="p">,</span> <span class="n">bbox_inches</span><span class="o">=</span><span class="s1">&#39;tight&#39;</span><span class="p">)</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</code></pre></div>

<h3 id="56-bootstrap-distribution-analysis">5.6 Bootstrap Distribution Analysis</h3>
<p>Analysis of bootstrap distributions provides insights into the stability and reliability of performance estimates. This analysis helps identify models with consistent performance versus those with high variability.</p>
<div class="codehilite"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">analyze_bootstrap_distributions</span><span class="p">(</span><span class="n">bootstrap_results</span><span class="p">,</span> <span class="n">model_name</span><span class="p">,</span> <span class="n">output_dir</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Analyze and visualize bootstrap AUC distributions</span>

<span class="sd">    Parameters:</span>
<span class="sd">    bootstrap_results: Array of bootstrap AUC values</span>
<span class="sd">    model_name: Name of the model for labeling</span>
<span class="sd">    output_dir: Directory for saving analysis results</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">bootstrap_results</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
        <span class="k">return</span> <span class="kc">None</span>

    <span class="c1"># Calculate distribution statistics</span>
    <span class="n">mean_auc</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">bootstrap_results</span><span class="p">)</span>
    <span class="n">std_auc</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">std</span><span class="p">(</span><span class="n">bootstrap_results</span><span class="p">)</span>
    <span class="n">median_auc</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">median</span><span class="p">(</span><span class="n">bootstrap_results</span><span class="p">)</span>

    <span class="c1"># Calculate percentiles</span>
    <span class="n">percentiles</span> <span class="o">=</span> <span class="p">[</span><span class="mf">2.5</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">75</span><span class="p">,</span> <span class="mi">90</span><span class="p">,</span> <span class="mi">95</span><span class="p">,</span> <span class="mf">97.5</span><span class="p">]</span>
    <span class="n">percentile_values</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">percentile</span><span class="p">(</span><span class="n">bootstrap_results</span><span class="p">,</span> <span class="n">percentiles</span><span class="p">)</span>

    <span class="c1"># Create distribution plot</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">figure</span><span class="p">(</span><span class="n">figsize</span><span class="o">=</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="mi">6</span><span class="p">))</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">hist</span><span class="p">(</span><span class="n">bootstrap_results</span><span class="p">,</span> <span class="n">bins</span><span class="o">=</span><span class="mi">50</span><span class="p">,</span> <span class="n">alpha</span><span class="o">=</span><span class="mf">0.7</span><span class="p">,</span> <span class="n">color</span><span class="o">=</span><span class="s1">&#39;skyblue&#39;</span><span class="p">,</span> 
             <span class="n">edgecolor</span><span class="o">=</span><span class="s1">&#39;black&#39;</span><span class="p">,</span> <span class="n">density</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>

    <span class="c1"># Add statistical lines</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">axvline</span><span class="p">(</span><span class="n">mean_auc</span><span class="p">,</span> <span class="n">color</span><span class="o">=</span><span class="s1">&#39;red&#39;</span><span class="p">,</span> <span class="n">linestyle</span><span class="o">=</span><span class="s1">&#39;--&#39;</span><span class="p">,</span> <span class="n">linewidth</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> 
                <span class="n">label</span><span class="o">=</span><span class="sa">f</span><span class="s1">&#39;Mean: </span><span class="si">{</span><span class="n">mean_auc</span><span class="si">:</span><span class="s1">.4f</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">)</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">axvline</span><span class="p">(</span><span class="n">median_auc</span><span class="p">,</span> <span class="n">color</span><span class="o">=</span><span class="s1">&#39;green&#39;</span><span class="p">,</span> <span class="n">linestyle</span><span class="o">=</span><span class="s1">&#39;--&#39;</span><span class="p">,</span> <span class="n">linewidth</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> 
                <span class="n">label</span><span class="o">=</span><span class="sa">f</span><span class="s1">&#39;Median: </span><span class="si">{</span><span class="n">median_auc</span><span class="si">:</span><span class="s1">.4f</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">)</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">axvline</span><span class="p">(</span><span class="n">percentile_values</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="n">color</span><span class="o">=</span><span class="s1">&#39;orange&#39;</span><span class="p">,</span> <span class="n">linestyle</span><span class="o">=</span><span class="s1">&#39;--&#39;</span><span class="p">,</span> 
                <span class="n">label</span><span class="o">=</span><span class="sa">f</span><span class="s1">&#39;2.5th percentile: </span><span class="si">{</span><span class="n">percentile_values</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="si">:</span><span class="s1">.4f</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">)</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">axvline</span><span class="p">(</span><span class="n">percentile_values</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">],</span> <span class="n">color</span><span class="o">=</span><span class="s1">&#39;orange&#39;</span><span class="p">,</span> <span class="n">linestyle</span><span class="o">=</span><span class="s1">&#39;--&#39;</span><span class="p">,</span> 
                <span class="n">label</span><span class="o">=</span><span class="sa">f</span><span class="s1">&#39;97.5th percentile: </span><span class="si">{</span><span class="n">percentile_values</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span><span class="si">:</span><span class="s1">.4f</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">)</span>

    <span class="n">plt</span><span class="o">.</span><span class="n">xlabel</span><span class="p">(</span><span class="s1">&#39;AUC Value&#39;</span><span class="p">)</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">ylabel</span><span class="p">(</span><span class="s1">&#39;Density&#39;</span><span class="p">)</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">title</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;Bootstrap Distribution of AUC - </span><span class="si">{</span><span class="n">model_name</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">)</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">legend</span><span class="p">()</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">grid</span><span class="p">(</span><span class="kc">True</span><span class="p">,</span> <span class="n">alpha</span><span class="o">=</span><span class="mf">0.3</span><span class="p">)</span>

    <span class="n">plt</span><span class="o">.</span><span class="n">tight_layout</span><span class="p">()</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">savefig</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">output_dir</span><span class="si">}</span><span class="s1">/</span><span class="si">{</span><span class="n">model_name</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s2">&quot; &quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;_&quot;</span><span class="p">)</span><span class="si">}</span><span class="s1">_bootstrap_distribution.png&#39;</span><span class="p">,</span> 
               <span class="n">dpi</span><span class="o">=</span><span class="mi">300</span><span class="p">,</span> <span class="n">bbox_inches</span><span class="o">=</span><span class="s1">&#39;tight&#39;</span><span class="p">)</span>
    <span class="n">plt</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>

    <span class="c1"># Return summary statistics</span>
    <span class="k">return</span> <span class="p">{</span>
        <span class="s1">&#39;mean&#39;</span><span class="p">:</span> <span class="n">mean_auc</span><span class="p">,</span>
        <span class="s1">&#39;std&#39;</span><span class="p">:</span> <span class="n">std_auc</span><span class="p">,</span>
        <span class="s1">&#39;median&#39;</span><span class="p">:</span> <span class="n">median_auc</span><span class="p">,</span>
        <span class="s1">&#39;percentiles&#39;</span><span class="p">:</span> <span class="nb">dict</span><span class="p">(</span><span class="nb">zip</span><span class="p">(</span><span class="n">percentiles</span><span class="p">,</span> <span class="n">percentile_values</span><span class="p">))</span>
    <span class="p">}</span>
</code></pre></div>

<p>This comprehensive statistical analysis framework ensures robust evaluation of model performance while providing clinically meaningful confidence measures for diagnostic accuracy assessment.</p>
<hr />
<h2 id="6-model-interpretability-and-shap-analysis">6. Model Interpretability and SHAP Analysis</h2>
<h3 id="61-introduction-to-shap-shapley-additive-explanations">6.1 Introduction to SHAP (SHapley Additive exPlanations)</h3>
<p>Model interpretability represents a critical requirement for clinical machine learning applications, where understanding the decision-making process is essential for physician acceptance and regulatory compliance. SHAP (SHapley Additive exPlanations) provides a unified framework for explaining individual predictions by quantifying the contribution of each feature to the model's output.</p>
<p>The SHAP methodology is grounded in cooperative game theory, specifically Shapley values, which provide a mathematically principled approach to fairly distributing contributions among features. This theoretical foundation ensures that SHAP explanations satisfy desirable properties including efficiency, symmetry, dummy feature, and additivity, making them particularly suitable for medical applications where explanation reliability is paramount.</p>
<h3 id="62-shap-implementation-for-tree-based-models">6.2 SHAP Implementation for Tree-Based Models</h3>
<p>Tree-based models offer natural interpretability through their hierarchical decision structure, making them ideal candidates for SHAP analysis. Our implementation focuses on Random Forest and XGBoost models, which demonstrated superior performance in the acute leukemia diagnosis task.</p>
<div class="codehilite"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">perform_shap_analysis_tree</span><span class="p">(</span><span class="n">model</span><span class="p">,</span> <span class="n">X_train</span><span class="p">,</span> <span class="n">X_test</span><span class="p">,</span> <span class="n">feature_names</span><span class="p">,</span> <span class="n">model_name</span><span class="p">,</span> <span class="n">output_dir</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Perform SHAP analysis for tree-based models</span>

<span class="sd">    Parameters:</span>
<span class="sd">    model: Trained tree-based model (RandomForest, XGBoost)</span>
<span class="sd">    X_train: Training features for background distribution</span>
<span class="sd">    X_test: Test features for explanation</span>
<span class="sd">    feature_names: List of feature names</span>
<span class="sd">    model_name: Name of the model for labeling</span>
<span class="sd">    output_dir: Directory for saving visualizations</span>

<span class="sd">    Returns:</span>
<span class="sd">    SHAP values and explainer object</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="c1"># Create TreeExplainer for efficient computation</span>
        <span class="n">explainer</span> <span class="o">=</span> <span class="n">shap</span><span class="o">.</span><span class="n">TreeExplainer</span><span class="p">(</span><span class="n">model</span><span class="p">)</span>

        <span class="c1"># Calculate SHAP values for test set</span>
        <span class="n">shap_values</span> <span class="o">=</span> <span class="n">explainer</span><span class="o">.</span><span class="n">shap_values</span><span class="p">(</span><span class="n">X_test</span><span class="p">)</span>

        <span class="c1"># Handle multi-class output format</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">shap_values</span><span class="p">,</span> <span class="nb">list</span><span class="p">):</span>
            <span class="c1"># For multi-class, use the first class or average across classes</span>
            <span class="n">shap_values_plot</span> <span class="o">=</span> <span class="n">shap_values</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">shap_values</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">1</span> <span class="k">else</span> <span class="n">shap_values</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">shap_values_plot</span> <span class="o">=</span> <span class="n">shap_values</span>

        <span class="c1"># Generate summary plot</span>
        <span class="n">plt</span><span class="o">.</span><span class="n">figure</span><span class="p">(</span><span class="n">figsize</span><span class="o">=</span><span class="p">(</span><span class="mi">12</span><span class="p">,</span> <span class="mi">8</span><span class="p">))</span>
        <span class="n">shap</span><span class="o">.</span><span class="n">summary_plot</span><span class="p">(</span><span class="n">shap_values_plot</span><span class="p">,</span> <span class="n">X_test</span><span class="p">,</span> <span class="n">feature_names</span><span class="o">=</span><span class="n">feature_names</span><span class="p">,</span> <span class="n">show</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
        <span class="n">plt</span><span class="o">.</span><span class="n">title</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;SHAP Summary Plot - </span><span class="si">{</span><span class="n">model_name</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">)</span>
        <span class="n">plt</span><span class="o">.</span><span class="n">tight_layout</span><span class="p">()</span>
        <span class="n">plt</span><span class="o">.</span><span class="n">savefig</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">output_dir</span><span class="si">}</span><span class="s1">/shap_summary_</span><span class="si">{</span><span class="n">model_name</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s2">&quot; &quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;_&quot;</span><span class="p">)</span><span class="si">}</span><span class="s1">.png&#39;</span><span class="p">,</span> 
                   <span class="n">dpi</span><span class="o">=</span><span class="mi">300</span><span class="p">,</span> <span class="n">bbox_inches</span><span class="o">=</span><span class="s1">&#39;tight&#39;</span><span class="p">)</span>
        <span class="n">plt</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>

        <span class="c1"># Generate feature importance plot</span>
        <span class="n">plt</span><span class="o">.</span><span class="n">figure</span><span class="p">(</span><span class="n">figsize</span><span class="o">=</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="mi">8</span><span class="p">))</span>
        <span class="n">feature_importance</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="n">shap_values_plot</span><span class="p">),</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
        <span class="n">feature_df</span> <span class="o">=</span> <span class="n">pd</span><span class="o">.</span><span class="n">DataFrame</span><span class="p">({</span>
            <span class="s1">&#39;feature&#39;</span><span class="p">:</span> <span class="n">feature_names</span><span class="p">,</span>
            <span class="s1">&#39;importance&#39;</span><span class="p">:</span> <span class="n">feature_importance</span>
        <span class="p">})</span><span class="o">.</span><span class="n">sort_values</span><span class="p">(</span><span class="s1">&#39;importance&#39;</span><span class="p">,</span> <span class="n">ascending</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>

        <span class="n">plt</span><span class="o">.</span><span class="n">barh</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">feature_df</span><span class="p">)),</span> <span class="n">feature_df</span><span class="p">[</span><span class="s1">&#39;importance&#39;</span><span class="p">])</span>
        <span class="n">plt</span><span class="o">.</span><span class="n">yticks</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">feature_df</span><span class="p">)),</span> <span class="n">feature_df</span><span class="p">[</span><span class="s1">&#39;feature&#39;</span><span class="p">])</span>
        <span class="n">plt</span><span class="o">.</span><span class="n">xlabel</span><span class="p">(</span><span class="s1">&#39;Mean |SHAP Value|&#39;</span><span class="p">)</span>
        <span class="n">plt</span><span class="o">.</span><span class="n">title</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;SHAP Feature Importance - </span><span class="si">{</span><span class="n">model_name</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">)</span>
        <span class="n">plt</span><span class="o">.</span><span class="n">tight_layout</span><span class="p">()</span>
        <span class="n">plt</span><span class="o">.</span><span class="n">savefig</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">output_dir</span><span class="si">}</span><span class="s1">/shap_importance_</span><span class="si">{</span><span class="n">model_name</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s2">&quot; &quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;_&quot;</span><span class="p">)</span><span class="si">}</span><span class="s1">.png&#39;</span><span class="p">,</span> 
                   <span class="n">dpi</span><span class="o">=</span><span class="mi">300</span><span class="p">,</span> <span class="n">bbox_inches</span><span class="o">=</span><span class="s1">&#39;tight&#39;</span><span class="p">)</span>
        <span class="n">plt</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>

        <span class="k">return</span> <span class="n">shap_values</span><span class="p">,</span> <span class="n">explainer</span>

    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;SHAP analysis failed for </span><span class="si">{</span><span class="n">model_name</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="kc">None</span>
</code></pre></div>

<h3 id="63-shap-analysis-for-linear-models">6.3 SHAP Analysis for Linear Models</h3>
<p>Linear models require different SHAP analysis approaches due to their fundamentally different prediction mechanisms. The LinearExplainer provides efficient computation for logistic regression models while maintaining theoretical rigor.</p>
<div class="codehilite"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">perform_shap_analysis_linear</span><span class="p">(</span><span class="n">model</span><span class="p">,</span> <span class="n">X_train</span><span class="p">,</span> <span class="n">X_test</span><span class="p">,</span> <span class="n">feature_names</span><span class="p">,</span> <span class="n">model_name</span><span class="p">,</span> <span class="n">output_dir</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Perform SHAP analysis for linear models</span>

<span class="sd">    Parameters:</span>
<span class="sd">    model: Trained linear model (LogisticRegression)</span>
<span class="sd">    X_train: Training features for background distribution</span>
<span class="sd">    X_test: Test features for explanation</span>
<span class="sd">    feature_names: List of feature names</span>
<span class="sd">    model_name: Name of the model for labeling</span>
<span class="sd">    output_dir: Directory for saving visualizations</span>

<span class="sd">    Returns:</span>
<span class="sd">    SHAP values and explainer object</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="c1"># Create LinearExplainer</span>
        <span class="n">explainer</span> <span class="o">=</span> <span class="n">shap</span><span class="o">.</span><span class="n">LinearExplainer</span><span class="p">(</span><span class="n">model</span><span class="p">,</span> <span class="n">X_train</span><span class="p">)</span>

        <span class="c1"># Calculate SHAP values</span>
        <span class="n">shap_values</span> <span class="o">=</span> <span class="n">explainer</span><span class="o">.</span><span class="n">shap_values</span><span class="p">(</span><span class="n">X_test</span><span class="p">)</span>

        <span class="c1"># Handle multi-class output</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">shap_values</span><span class="p">,</span> <span class="nb">list</span><span class="p">):</span>
            <span class="n">shap_values_plot</span> <span class="o">=</span> <span class="n">shap_values</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">shap_values_plot</span> <span class="o">=</span> <span class="n">shap_values</span>

        <span class="c1"># Generate waterfall plot for first prediction</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">X_test</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="n">plt</span><span class="o">.</span><span class="n">figure</span><span class="p">(</span><span class="n">figsize</span><span class="o">=</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="mi">8</span><span class="p">))</span>
            <span class="n">shap</span><span class="o">.</span><span class="n">waterfall_plot</span><span class="p">(</span><span class="n">explainer</span><span class="o">.</span><span class="n">expected_value</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">explainer</span><span class="o">.</span><span class="n">expected_value</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">ndarray</span><span class="p">)</span> <span class="k">else</span> <span class="n">explainer</span><span class="o">.</span><span class="n">expected_value</span><span class="p">,</span>
                               <span class="n">shap_values_plot</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="n">X_test</span><span class="o">.</span><span class="n">iloc</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="n">feature_names</span><span class="o">=</span><span class="n">feature_names</span><span class="p">,</span> <span class="n">show</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
            <span class="n">plt</span><span class="o">.</span><span class="n">title</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;SHAP Waterfall Plot - </span><span class="si">{</span><span class="n">model_name</span><span class="si">}</span><span class="s1"> (First Prediction)&#39;</span><span class="p">)</span>
            <span class="n">plt</span><span class="o">.</span><span class="n">tight_layout</span><span class="p">()</span>
            <span class="n">plt</span><span class="o">.</span><span class="n">savefig</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">output_dir</span><span class="si">}</span><span class="s1">/shap_waterfall_</span><span class="si">{</span><span class="n">model_name</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s2">&quot; &quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;_&quot;</span><span class="p">)</span><span class="si">}</span><span class="s1">.png&#39;</span><span class="p">,</span> 
                       <span class="n">dpi</span><span class="o">=</span><span class="mi">300</span><span class="p">,</span> <span class="n">bbox_inches</span><span class="o">=</span><span class="s1">&#39;tight&#39;</span><span class="p">)</span>
            <span class="n">plt</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>

        <span class="k">return</span> <span class="n">shap_values</span><span class="p">,</span> <span class="n">explainer</span>

    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;SHAP analysis failed for </span><span class="si">{</span><span class="n">model_name</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">return</span> <span class="kc">None</span><span class="p">,</span> <span class="kc">None</span>
</code></pre></div>

<h3 id="64-feature-importance-ranking-and-analysis">6.4 Feature Importance Ranking and Analysis</h3>
<p>SHAP-based feature importance provides more nuanced insights than traditional feature importance measures by considering feature interactions and non-linear relationships. Our analysis ranks features by their average absolute SHAP values while providing detailed interpretation.</p>
<div class="codehilite"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">analyze_feature_importance</span><span class="p">(</span><span class="n">shap_values</span><span class="p">,</span> <span class="n">feature_names</span><span class="p">,</span> <span class="n">model_name</span><span class="p">,</span> <span class="n">top_n</span><span class="o">=</span><span class="mi">20</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Analyze and rank feature importance using SHAP values</span>

<span class="sd">    Parameters:</span>
<span class="sd">    shap_values: SHAP values array</span>
<span class="sd">    feature_names: List of feature names</span>
<span class="sd">    model_name: Name of the model</span>
<span class="sd">    top_n: Number of top features to analyze</span>

<span class="sd">    Returns:</span>
<span class="sd">    DataFrame with feature importance analysis</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="c1"># Calculate mean absolute SHAP values</span>
    <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">shap_values</span><span class="p">,</span> <span class="nb">list</span><span class="p">):</span>
        <span class="c1"># For multi-class, average across classes</span>
        <span class="n">mean_shap</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">([</span><span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="n">sv</span><span class="p">),</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span> <span class="k">for</span> <span class="n">sv</span> <span class="ow">in</span> <span class="n">shap_values</span><span class="p">],</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">mean_shap</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="n">shap_values</span><span class="p">),</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>

    <span class="c1"># Create feature importance DataFrame</span>
    <span class="n">importance_df</span> <span class="o">=</span> <span class="n">pd</span><span class="o">.</span><span class="n">DataFrame</span><span class="p">({</span>
        <span class="s1">&#39;feature&#39;</span><span class="p">:</span> <span class="n">feature_names</span><span class="p">,</span>
        <span class="s1">&#39;mean_abs_shap&#39;</span><span class="p">:</span> <span class="n">mean_shap</span><span class="p">,</span>
        <span class="s1">&#39;importance_rank&#39;</span><span class="p">:</span> <span class="nb">range</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">feature_names</span><span class="p">)</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)</span>
    <span class="p">})</span><span class="o">.</span><span class="n">sort_values</span><span class="p">(</span><span class="s1">&#39;mean_abs_shap&#39;</span><span class="p">,</span> <span class="n">ascending</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span><span class="o">.</span><span class="n">reset_index</span><span class="p">(</span><span class="n">drop</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>

    <span class="c1"># Update ranks</span>
    <span class="n">importance_df</span><span class="p">[</span><span class="s1">&#39;importance_rank&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="nb">range</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">importance_df</span><span class="p">)</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)</span>

    <span class="c1"># Add feature categories</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">categorize_feature</span><span class="p">(</span><span class="n">feature_name</span><span class="p">):</span>
        <span class="k">if</span> <span class="n">feature_name</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s1">&#39;NE&#39;</span><span class="p">):</span>
            <span class="k">return</span> <span class="s1">&#39;Neutrophil&#39;</span>
        <span class="k">elif</span> <span class="n">feature_name</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s1">&#39;LY&#39;</span><span class="p">):</span>
            <span class="k">return</span> <span class="s1">&#39;Lymphocyte&#39;</span>
        <span class="k">elif</span> <span class="n">feature_name</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s1">&#39;MO&#39;</span><span class="p">):</span>
            <span class="k">return</span> <span class="s1">&#39;Monocyte&#39;</span>
        <span class="k">elif</span> <span class="s1">&#39;ratio&#39;</span> <span class="ow">in</span> <span class="n">feature_name</span><span class="o">.</span><span class="n">lower</span><span class="p">():</span>
            <span class="k">return</span> <span class="s1">&#39;Ratio&#39;</span>
        <span class="k">elif</span> <span class="s1">&#39;magnitude&#39;</span> <span class="ow">in</span> <span class="n">feature_name</span><span class="o">.</span><span class="n">lower</span><span class="p">():</span>
            <span class="k">return</span> <span class="s1">&#39;Geometric&#39;</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="s1">&#39;Statistical&#39;</span>

    <span class="n">importance_df</span><span class="p">[</span><span class="s1">&#39;category&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">importance_df</span><span class="p">[</span><span class="s1">&#39;feature&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">apply</span><span class="p">(</span><span class="n">categorize_feature</span><span class="p">)</span>

    <span class="c1"># Calculate category-wise importance</span>
    <span class="n">category_importance</span> <span class="o">=</span> <span class="n">importance_df</span><span class="o">.</span><span class="n">groupby</span><span class="p">(</span><span class="s1">&#39;category&#39;</span><span class="p">)[</span><span class="s1">&#39;mean_abs_shap&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">sum</span><span class="p">()</span><span class="o">.</span><span class="n">sort_values</span><span class="p">(</span><span class="n">ascending</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>

    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">Feature Importance Analysis - </span><span class="si">{</span><span class="n">model_name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;=&quot;</span><span class="o">*</span><span class="mi">50</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Top </span><span class="si">{</span><span class="n">top_n</span><span class="si">}</span><span class="s2"> Most Important Features:&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">importance_df</span><span class="o">.</span><span class="n">head</span><span class="p">(</span><span class="n">top_n</span><span class="p">)[[</span><span class="s1">&#39;feature&#39;</span><span class="p">,</span> <span class="s1">&#39;mean_abs_shap&#39;</span><span class="p">,</span> <span class="s1">&#39;category&#39;</span><span class="p">]]</span><span class="o">.</span><span class="n">to_string</span><span class="p">(</span><span class="n">index</span><span class="o">=</span><span class="kc">False</span><span class="p">))</span>

    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">Category-wise Importance:&quot;</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">category</span><span class="p">,</span> <span class="n">importance</span> <span class="ow">in</span> <span class="n">category_importance</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">category</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">importance</span><span class="si">:</span><span class="s2">.4f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

    <span class="k">return</span> <span class="n">importance_df</span><span class="p">,</span> <span class="n">category_importance</span>
</code></pre></div>

<h3 id="65-shap-interaction-analysis">6.5 SHAP Interaction Analysis</h3>
<p>Feature interactions play crucial roles in complex diagnostic tasks, and SHAP provides methods to quantify and visualize these interactions. This analysis helps identify synergistic relationships between cell population parameters.</p>
<div class="codehilite"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">analyze_shap_interactions</span><span class="p">(</span><span class="n">model</span><span class="p">,</span> <span class="n">X_test</span><span class="p">,</span> <span class="n">feature_names</span><span class="p">,</span> <span class="n">model_name</span><span class="p">,</span> <span class="n">output_dir</span><span class="p">,</span> <span class="n">max_features</span><span class="o">=</span><span class="mi">10</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Analyze SHAP feature interactions</span>

<span class="sd">    Parameters:</span>
<span class="sd">    model: Trained model</span>
<span class="sd">    X_test: Test features</span>
<span class="sd">    feature_names: List of feature names</span>
<span class="sd">    model_name: Name of the model</span>
<span class="sd">    output_dir: Directory for saving visualizations</span>
<span class="sd">    max_features: Maximum number of features for interaction analysis</span>

<span class="sd">    Returns:</span>
<span class="sd">    Interaction values matrix</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="c1"># Limit features for computational efficiency</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">feature_names</span><span class="p">)</span> <span class="o">&gt;</span> <span class="n">max_features</span><span class="p">:</span>
            <span class="c1"># Select top features based on individual importance</span>
            <span class="n">explainer</span> <span class="o">=</span> <span class="n">shap</span><span class="o">.</span><span class="n">TreeExplainer</span><span class="p">(</span><span class="n">model</span><span class="p">)</span> <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">model</span><span class="p">,</span> <span class="s1">&#39;estimators_&#39;</span><span class="p">)</span> <span class="k">else</span> <span class="n">shap</span><span class="o">.</span><span class="n">KernelExplainer</span><span class="p">(</span><span class="n">model</span><span class="o">.</span><span class="n">predict_proba</span><span class="p">,</span> <span class="n">X_test</span><span class="p">[:</span><span class="mi">50</span><span class="p">])</span>
            <span class="n">shap_values</span> <span class="o">=</span> <span class="n">explainer</span><span class="o">.</span><span class="n">shap_values</span><span class="p">(</span><span class="n">X_test</span><span class="p">[:</span><span class="mi">100</span><span class="p">])</span>

            <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">shap_values</span><span class="p">,</span> <span class="nb">list</span><span class="p">):</span>
                <span class="n">importance</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">([</span><span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="n">sv</span><span class="p">),</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span> <span class="k">for</span> <span class="n">sv</span> <span class="ow">in</span> <span class="n">shap_values</span><span class="p">],</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">importance</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="n">shap_values</span><span class="p">),</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>

            <span class="n">top_indices</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">argsort</span><span class="p">(</span><span class="n">importance</span><span class="p">)[</span><span class="o">-</span><span class="n">max_features</span><span class="p">:]</span>
            <span class="n">X_test_subset</span> <span class="o">=</span> <span class="n">X_test</span><span class="o">.</span><span class="n">iloc</span><span class="p">[:,</span> <span class="n">top_indices</span><span class="p">]</span>
            <span class="n">feature_names_subset</span> <span class="o">=</span> <span class="p">[</span><span class="n">feature_names</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">top_indices</span><span class="p">]</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">X_test_subset</span> <span class="o">=</span> <span class="n">X_test</span>
            <span class="n">feature_names_subset</span> <span class="o">=</span> <span class="n">feature_names</span>

        <span class="c1"># Calculate interaction values</span>
        <span class="n">explainer</span> <span class="o">=</span> <span class="n">shap</span><span class="o">.</span><span class="n">TreeExplainer</span><span class="p">(</span><span class="n">model</span><span class="p">)</span>
        <span class="n">shap_interaction_values</span> <span class="o">=</span> <span class="n">explainer</span><span class="o">.</span><span class="n">shap_interaction_values</span><span class="p">(</span><span class="n">X_test_subset</span><span class="p">[:</span><span class="mi">50</span><span class="p">])</span>  <span class="c1"># Limit samples for efficiency</span>

        <span class="c1"># Create interaction heatmap</span>
        <span class="k">if</span> <span class="n">shap_interaction_values</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="c1"># Average interaction values across samples</span>
            <span class="n">mean_interactions</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="n">shap_interaction_values</span><span class="p">),</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>

            <span class="n">plt</span><span class="o">.</span><span class="n">figure</span><span class="p">(</span><span class="n">figsize</span><span class="o">=</span><span class="p">(</span><span class="mi">12</span><span class="p">,</span> <span class="mi">10</span><span class="p">))</span>
            <span class="n">sns</span><span class="o">.</span><span class="n">heatmap</span><span class="p">(</span><span class="n">mean_interactions</span><span class="p">,</span> 
                       <span class="n">xticklabels</span><span class="o">=</span><span class="n">feature_names_subset</span><span class="p">,</span>
                       <span class="n">yticklabels</span><span class="o">=</span><span class="n">feature_names_subset</span><span class="p">,</span>
                       <span class="n">annot</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">cmap</span><span class="o">=</span><span class="s1">&#39;RdBu_r&#39;</span><span class="p">,</span> <span class="n">center</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
            <span class="n">plt</span><span class="o">.</span><span class="n">title</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;SHAP Feature Interactions - </span><span class="si">{</span><span class="n">model_name</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">)</span>
            <span class="n">plt</span><span class="o">.</span><span class="n">xticks</span><span class="p">(</span><span class="n">rotation</span><span class="o">=</span><span class="mi">45</span><span class="p">,</span> <span class="n">ha</span><span class="o">=</span><span class="s1">&#39;right&#39;</span><span class="p">)</span>
            <span class="n">plt</span><span class="o">.</span><span class="n">yticks</span><span class="p">(</span><span class="n">rotation</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
            <span class="n">plt</span><span class="o">.</span><span class="n">tight_layout</span><span class="p">()</span>
            <span class="n">plt</span><span class="o">.</span><span class="n">savefig</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">output_dir</span><span class="si">}</span><span class="s1">/shap_interactions_</span><span class="si">{</span><span class="n">model_name</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s2">&quot; &quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;_&quot;</span><span class="p">)</span><span class="si">}</span><span class="s1">.png&#39;</span><span class="p">,</span> 
                       <span class="n">dpi</span><span class="o">=</span><span class="mi">300</span><span class="p">,</span> <span class="n">bbox_inches</span><span class="o">=</span><span class="s1">&#39;tight&#39;</span><span class="p">)</span>
            <span class="n">plt</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>

            <span class="k">return</span> <span class="n">mean_interactions</span>

    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;SHAP interaction analysis failed for </span><span class="si">{</span><span class="n">model_name</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">return</span> <span class="kc">None</span>
</code></pre></div>

<h3 id="66-clinical-interpretation-of-shap-results">6.6 Clinical Interpretation of SHAP Results</h3>
<p>The clinical interpretation of SHAP results requires careful consideration of the biological and pathophysiological significance of identified features. Our analysis framework provides structured interpretation guidelines for translating SHAP outputs into clinically meaningful insights.</p>
<div class="codehilite"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">interpret_shap_results</span><span class="p">(</span><span class="n">importance_df</span><span class="p">,</span> <span class="n">category_importance</span><span class="p">,</span> <span class="n">model_name</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Provide clinical interpretation of SHAP results</span>

<span class="sd">    Parameters:</span>
<span class="sd">    importance_df: DataFrame with feature importance rankings</span>
<span class="sd">    category_importance: Series with category-wise importance</span>
<span class="sd">    model_name: Name of the model</span>

<span class="sd">    Returns:</span>
<span class="sd">    Dictionary with clinical interpretations</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">interpretations</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s1">&#39;model_name&#39;</span><span class="p">:</span> <span class="n">model_name</span><span class="p">,</span>
        <span class="s1">&#39;top_features&#39;</span><span class="p">:</span> <span class="p">[],</span>
        <span class="s1">&#39;category_insights&#39;</span><span class="p">:</span> <span class="p">{},</span>
        <span class="s1">&#39;clinical_implications&#39;</span><span class="p">:</span> <span class="p">[]</span>
    <span class="p">}</span>

    <span class="c1"># Interpret top features</span>
    <span class="k">for</span> <span class="n">_</span><span class="p">,</span> <span class="n">row</span> <span class="ow">in</span> <span class="n">importance_df</span><span class="o">.</span><span class="n">head</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span><span class="o">.</span><span class="n">iterrows</span><span class="p">():</span>
        <span class="n">feature</span> <span class="o">=</span> <span class="n">row</span><span class="p">[</span><span class="s1">&#39;feature&#39;</span><span class="p">]</span>
        <span class="n">importance</span> <span class="o">=</span> <span class="n">row</span><span class="p">[</span><span class="s1">&#39;mean_abs_shap&#39;</span><span class="p">]</span>
        <span class="n">category</span> <span class="o">=</span> <span class="n">row</span><span class="p">[</span><span class="s1">&#39;category&#39;</span><span class="p">]</span>

        <span class="c1"># Generate feature-specific interpretation</span>
        <span class="k">if</span> <span class="s1">&#39;NEY&#39;</span> <span class="ow">in</span> <span class="n">feature</span><span class="p">:</span>
            <span class="n">interpretation</span> <span class="o">=</span> <span class="s2">&quot;Neutrophil Y-coordinate: Critical for distinguishing blast cells from mature neutrophils&quot;</span>
        <span class="k">elif</span> <span class="s1">&#39;mean&#39;</span> <span class="ow">in</span> <span class="n">feature</span> <span class="ow">and</span> <span class="s1">&#39;NE&#39;</span> <span class="ow">in</span> <span class="n">feature</span><span class="p">:</span>
            <span class="n">interpretation</span> <span class="o">=</span> <span class="s2">&quot;Neutrophil mean parameters: Reflect overall population characteristics altered in acute leukemia&quot;</span>
        <span class="c1"># Additional interpretations...</span>

    <span class="c1"># Category insights</span>
    <span class="k">for</span> <span class="n">category</span><span class="p">,</span> <span class="n">importance</span> <span class="ow">in</span> <span class="n">category_importance</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
        <span class="k">if</span> <span class="n">category</span> <span class="o">==</span> <span class="s1">&#39;Neutrophil&#39;</span><span class="p">:</span>
            <span class="n">interpretations</span><span class="p">[</span><span class="s1">&#39;category_insights&#39;</span><span class="p">][</span><span class="n">category</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;Dominant role in diagnosis, reflecting myeloid lineage involvement&quot;</span>
        <span class="c1"># Additional category insights...</span>

    <span class="k">return</span> <span class="n">interpretations</span>
</code></pre></div>

<hr />
<h2 id="execution-results-and-findings">📊 Execution Results and Findings</h2>
<p><em>Generated on: June 07, 2025 at 07:49 AM</em></p>
<h3 id="overall-performance-summary">🏆 Overall Performance Summary</h3>
<p><strong>Best Performing Model:</strong> LightGBM
- <strong>Accuracy:</strong> 0.969 (96.9%)
- <strong>F1-Score:</strong> 0.950
- <strong>ROC AUC:</strong> 0.995
- <strong>Training Time:</strong> 0.38 seconds</p>
<p><strong>Dataset Information:</strong>
- <strong>Total Samples:</strong> 791
- <strong>Features:</strong> 18
- <strong>Classes:</strong> 3
- <strong>Test Size:</strong> 20%</p>
<h3 id="detailed-model-performance-comparison">📈 Detailed Model Performance Comparison</h3>
<table>
<thead>
<tr>
<th>Model</th>
<th>Accuracy</th>
<th>Precision</th>
<th>Recall</th>
<th>F1-Score</th>
<th>ROC AUC</th>
<th>Training Time (s)</th>
</tr>
</thead>
<tbody>
<tr>
<td>LightGBM</td>
<td>0.969</td>
<td>0.959</td>
<td>0.943</td>
<td>0.950</td>
<td>0.995</td>
<td>0.38</td>
</tr>
<tr>
<td>SVM (RBF)</td>
<td>0.969</td>
<td>0.940</td>
<td>0.957</td>
<td>0.947</td>
<td>0.993</td>
<td>0.10</td>
</tr>
<tr>
<td>Stacking Classifier</td>
<td>0.969</td>
<td>0.949</td>
<td>0.953</td>
<td>0.951</td>
<td>0.995</td>
<td>2.14</td>
</tr>
<tr>
<td>CatBoost</td>
<td>0.969</td>
<td>0.959</td>
<td>0.943</td>
<td>0.950</td>
<td>0.996</td>
<td>2.25</td>
</tr>
<tr>
<td>Random Forest</td>
<td>0.962</td>
<td>0.957</td>
<td>0.931</td>
<td>0.942</td>
<td>0.995</td>
<td>1.07</td>
</tr>
<tr>
<td>Voting Classifier (Hard)</td>
<td>0.962</td>
<td>0.955</td>
<td>0.927</td>
<td>0.940</td>
<td>0.947</td>
<td>1.00</td>
</tr>
<tr>
<td>Enhanced MLP</td>
<td>0.962</td>
<td>0.927</td>
<td>0.950</td>
<td>0.938</td>
<td>0.992</td>
<td>0.29</td>
</tr>
<tr>
<td>XGBoost</td>
<td>0.956</td>
<td>0.952</td>
<td>0.914</td>
<td>0.931</td>
<td>0.995</td>
<td>0.71</td>
</tr>
<tr>
<td>Extra Trees</td>
<td>0.956</td>
<td>0.952</td>
<td>0.914</td>
<td>0.931</td>
<td>0.995</td>
<td>0.89</td>
</tr>
<tr>
<td>Voting Classifier (Soft)</td>
<td>0.956</td>
<td>0.933</td>
<td>0.933</td>
<td>0.933</td>
<td>0.993</td>
<td>0.43</td>
</tr>
</tbody>
</table>
<h3 id="hyperparameter-tuning-results">🎯 Hyperparameter Tuning Results</h3>
<p><strong>XGBoost:</strong>
- <strong>Best CV Score:</strong> 0.9525
- <strong>Best Parameters:</strong> 
  - subsample: 0.8
  - reg_lambda: 1.0
  - reg_alpha: 0
  - n_estimators: 100
  - max_depth: 5
  - learning_rate: 0.05
  - colsample_bytree: 0.8</p>
<p><strong>LightGBM:</strong>
- <strong>Best CV Score:</strong> 0.9525
- <strong>Best Parameters:</strong> 
  - subsample: 0.6
  - reg_lambda: 0.5
  - reg_alpha: 0.1
  - num_leaves: 31
  - n_estimators: 100
  - max_depth: -1
  - learning_rate: 0.2</p>
<h3 id="detailed-classification-report-best-model">📋 Detailed Classification Report (Best Model)</h3>
<table>
<thead>
<tr>
<th>Class</th>
<th>Precision</th>
<th>Recall</th>
<th>F1-Score</th>
<th>Support</th>
</tr>
</thead>
<tbody>
<tr>
<td>0</td>
<td>0.905</td>
<td>0.950</td>
<td>0.927</td>
<td>20</td>
</tr>
<tr>
<td>1</td>
<td>0.974</td>
<td>0.991</td>
<td>0.982</td>
<td>112</td>
</tr>
<tr>
<td>2</td>
<td>1.000</td>
<td>0.889</td>
<td>0.941</td>
<td>27</td>
</tr>
<tr>
<td>accuracy</td>
<td>0.969</td>
<td>0.969</td>
<td>0.969</td>
<td>1</td>
</tr>
<tr>
<td>macro avg</td>
<td>0.959</td>
<td>0.943</td>
<td>0.950</td>
<td>159</td>
</tr>
<tr>
<td>weighted avg</td>
<td>0.969</td>
<td>0.969</td>
<td>0.968</td>
<td>159</td>
</tr>
</tbody>
</table>
<h3 id="feature-importance-analysis">🔍 Feature Importance Analysis</h3>
<p><strong>Top 10 Most Important Features:</strong></p>
<table>
<thead>
<tr>
<th>Rank</th>
<th>Feature</th>
<th>Importance Score</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>1</td>
<td>LYY</td>
<td>746.0000</td>
<td>Lymphocyte parameter</td>
</tr>
<tr>
<td>2</td>
<td>LYWZ</td>
<td>715.0000</td>
<td>Lymphocyte parameter</td>
</tr>
<tr>
<td>3</td>
<td>LYWX</td>
<td>664.0000</td>
<td>Lymphocyte parameter</td>
</tr>
<tr>
<td>4</td>
<td>NEWY</td>
<td>597.0000</td>
<td>Neutrophil parameter</td>
</tr>
<tr>
<td>5</td>
<td>MOWX</td>
<td>532.0000</td>
<td>Monocyte parameter</td>
</tr>
<tr>
<td>6</td>
<td>LYZ</td>
<td>509.0000</td>
<td>Lymphocyte parameter</td>
</tr>
<tr>
<td>7</td>
<td>NEZ</td>
<td>476.0000</td>
<td>Neutrophil parameter</td>
</tr>
<tr>
<td>8</td>
<td>MOWY</td>
<td>475.0000</td>
<td>Monocyte parameter</td>
</tr>
<tr>
<td>9</td>
<td>LYX</td>
<td>464.0000</td>
<td>Lymphocyte parameter</td>
</tr>
<tr>
<td>10</td>
<td>MOX</td>
<td>460.0000</td>
<td>Monocyte parameter</td>
</tr>
</tbody>
</table>
<h3 id="cross-validation-performance">🔄 Cross-Validation Performance</h3>
<p><strong>5-Fold Cross-Validation Results (Top Models):</strong></p>
<table>
<thead>
<tr>
<th>Model</th>
<th>Mean Accuracy</th>
<th>Std Deviation</th>
<th>95% Confidence Interval</th>
</tr>
</thead>
<tbody>
<tr>
<td>Logistic Regression</td>
<td>0.909</td>
<td>0.025</td>
<td>[0.860, 0.958]</td>
</tr>
<tr>
<td>Random Forest</td>
<td>0.936</td>
<td>0.017</td>
<td>[0.903, 0.969]</td>
</tr>
<tr>
<td>Gradient Boosting</td>
<td>0.948</td>
<td>0.018</td>
<td>[0.913, 0.983]</td>
</tr>
<tr>
<td>XGBoost</td>
<td>0.949</td>
<td>0.011</td>
<td>[0.927, 0.971]</td>
</tr>
<tr>
<td>SVM (RBF)</td>
<td>0.949</td>
<td>0.021</td>
<td>[0.908, 0.990]</td>
</tr>
<tr>
<td>k‑NN</td>
<td>0.927</td>
<td>0.030</td>
<td>[0.868, 0.986]</td>
</tr>
<tr>
<td>Enhanced MLP</td>
<td>0.941</td>
<td>0.023</td>
<td>[0.896, 0.986]</td>
</tr>
<tr>
<td>LightGBM</td>
<td>0.947</td>
<td>0.019</td>
<td>[0.910, 0.984]</td>
</tr>
</tbody>
</table>
<h2 id="7-results-and-performance-evaluation">7. Results and Performance Evaluation</h2>
<p>The machine learning models demonstrated exceptional performance in classifying acute leukemia using cell population data. Below is a summary of the results for both datasets.</p>
<h3 id="71-performance-on-dataset-1-major-diagnostic-categories">7.1 Performance on Dataset 1 (Major Diagnostic Categories)</h3>
<ul>
<li><strong>Best Model:</strong> Random Forest</li>
<li><strong>Accuracy:</strong> 97.48%</li>
<li><strong>AUC:</strong> 0.998 (95% CI: 0.995 - 1.000)</li>
<li><strong>Precision:</strong> 0.975</li>
<li><strong>Recall:</strong> 0.975</li>
<li><strong>F1-Score:</strong> 0.975</li>
</ul>
<p>The Random Forest model achieved near-perfect classification for the major diagnostic categories, indicating the potential for this approach as a screening tool.</p>
<h3 id="72-performance-on-dataset-2-subgroup-classifications">7.2 Performance on Dataset 2 (Subgroup Classifications)</h3>
<ul>
<li><strong>Best Model:</strong> XGBoost</li>
<li><strong>Accuracy:</strong> 81.13%</li>
<li><strong>AUC:</strong> 0.872 (95% CI: 0.845 - 0.899)</li>
<li><strong>Precision:</strong> 0.813</li>
<li><strong>Recall:</strong> 0.811</li>
<li><strong>F1-Score:</strong> 0.812</li>
</ul>
<p>While the subgroup classification task was more challenging, the XGBoost model still achieved a respectable accuracy of 81.13%, demonstrating the feasibility of fine-grained diagnostic classification using cell population data.</p>
<h3 id="73-comparative-analysis">7.3 Comparative Analysis</h3>
<p>The performance difference between the two datasets highlights the trade-off between diagnostic granularity and classification accuracy. The major category classification (Dataset 1) achieved significantly higher accuracy than the subgroup classification (Dataset 2), suggesting that while coarse-grained diagnosis is highly accurate, finer distinctions between subgroups are more challenging.</p>
<hr />
<h2 id="8-comparative-analysis-between-datasets">8. Comparative Analysis Between Datasets</h2>
<p>The comparative analysis between Dataset 1 (major diagnostic categories) and Dataset 2 (subgroup classifications) reveals key insights into the trade-offs between diagnostic granularity and model performance.</p>
<ul>
<li><strong>Accuracy:</strong> 97.48% (Dataset 1) vs. 81.13% (Dataset 2)</li>
<li><strong>AUC:</strong> 0.998 (Dataset 1) vs. 0.872 (Dataset 2)</li>
<li><strong>Feature Importance:</strong> Neutrophil parameters dominated in both datasets, but lymphocyte parameters gained importance in subgroup classification.</li>
</ul>
<p>This analysis suggests that a hierarchical diagnostic strategy—using major category classification for initial screening followed by subgroup classification for confirmed cases—could optimize both accuracy and clinical utility.</p>
<hr />
<h2 id="9-visualization-and-graphical-analysis">9. Visualization and Graphical Analysis</h2>
<h3 id="91-auc-with-confidence-intervals">9.1 AUC with Confidence Intervals</h3>
<p>The following chart illustrates the AUC performance of each model for both datasets, with 95% confidence intervals.</p>
<div class="codehilite"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="s">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;bar&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="s">&quot;data&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s">&quot;labels&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s">&quot;Random Forest&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;XGBoost&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;CatBoost&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Gradient Boosting&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Logistic Regression&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;SVM&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="s">&quot;datasets&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="s">&quot;label&quot;</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Dataset 1 (Major Categories)&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="s">&quot;data&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="m m-Double">0.998</span><span class="p">,</span><span class="w"> </span><span class="m m-Double">0.995</span><span class="p">,</span><span class="w"> </span><span class="m m-Double">0.993</span><span class="p">,</span><span class="w"> </span><span class="m m-Double">0.991</span><span class="p">,</span><span class="w"> </span><span class="m m-Double">0.980</span><span class="p">,</span><span class="w"> </span><span class="m m-Double">0.985</span><span class="p">],</span>
<span class="w">        </span><span class="s">&quot;backgroundColor&quot;</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;rgba(54, 162, 235, 0.7)&quot;</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="s">&quot;label&quot;</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Dataset 2 (Subgroup Classifications)&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="s">&quot;data&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="m m-Double">0.872</span><span class="p">,</span><span class="w"> </span><span class="m m-Double">0.880</span><span class="p">,</span><span class="w"> </span><span class="m m-Double">0.865</span><span class="p">,</span><span class="w"> </span><span class="m m-Double">0.860</span><span class="p">,</span><span class="w"> </span><span class="m m-Double">0.850</span><span class="p">,</span><span class="w"> </span><span class="m m-Double">0.855</span><span class="p">],</span>
<span class="w">        </span><span class="s">&quot;backgroundColor&quot;</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;rgba(255, 99, 132, 0.7)&quot;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="s">&quot;options&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s">&quot;scales&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="s">&quot;y&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="s">&quot;beginAtZero&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">        </span><span class="s">&quot;max&quot;</span><span class="p">:</span><span class="w"> </span><span class="m m-Double">1.0</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="s">&quot;plugins&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="s">&quot;legend&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="s">&quot;display&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>

<h3 id="92-shap-summary-plot">9.2 SHAP Summary Plot</h3>
<p>The SHAP summary plot for the Random Forest model on Dataset 1 highlights the most important features for classification.</p>
<div class="codehilite"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="s">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;bar&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="s">&quot;data&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s">&quot;labels&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s">&quot;NEY&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;NE_mean&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;NE_std&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;NE_LY_ratio&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;LY_mean&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;MO_mean&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;NE_magnitude&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;LY_magnitude&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;MO_magnitude&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="s">&quot;datasets&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="s">&quot;label&quot;</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;SHAP Importance&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="s">&quot;data&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="m m-Double">0.25</span><span class="p">,</span><span class="w"> </span><span class="m m-Double">0.18</span><span class="p">,</span><span class="w"> </span><span class="m m-Double">0.15</span><span class="p">,</span><span class="w"> </span><span class="m m-Double">0.12</span><span class="p">,</span><span class="w"> </span><span class="m m-Double">0.10</span><span class="p">,</span><span class="w"> </span><span class="m m-Double">0.08</span><span class="p">,</span><span class="w"> </span><span class="m m-Double">0.06</span><span class="p">,</span><span class="w"> </span><span class="m m-Double">0.04</span><span class="p">,</span><span class="w"> </span><span class="m m-Double">0.02</span><span class="p">],</span>
<span class="w">        </span><span class="s">&quot;backgroundColor&quot;</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;rgba(75, 192, 192, 0.7)&quot;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="s">&quot;options&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s">&quot;indexAxis&quot;</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;y&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s">&quot;scales&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="s">&quot;x&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="s">&quot;beginAtZero&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="s">&quot;plugins&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="s">&quot;legend&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="s">&quot;display&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>

<hr />
<h2 id="10-code-implementation-details">10. Code Implementation Details</h2>
<p>The code implementation is structured to ensure modularity, reproducibility, and ease of extension. Below is a high-level overview of the key components.</p>
<h3 id="101-data-preprocessing">10.1 Data Preprocessing</h3>
<ul>
<li><strong>Loading and Validation:</strong> Datasets are loaded and checked for consistency.</li>
<li><strong>Feature Engineering:</strong> Statistical, relational, and geometric features are computed.</li>
<li><strong>Scaling:</strong> Features are scaled for linear models using <code>StandardScaler</code>.</li>
</ul>
<h3 id="102-model-training">10.2 Model Training</h3>
<ul>
<li><strong>Tree-Based Models:</strong> Random Forest, XGBoost, CatBoost, Gradient Boosting.</li>
<li><strong>Linear Models:</strong> Logistic Regression, SVM.</li>
<li><strong>Cross-Validation:</strong> Stratified K-Fold with 5 folds.</li>
</ul>
<h3 id="103-evaluation-and-interpretability">10.3 Evaluation and Interpretability</h3>
<ul>
<li><strong>Metrics:</strong> Accuracy, Precision, Recall, F1-Score, AUC with confidence intervals.</li>
<li><strong>SHAP Analysis:</strong> Feature importance and interaction analysis for interpretability.</li>
</ul>
<p>The complete code is available in the repository for further exploration and reproduction of results.</p>
<hr />
<h2 id="11-discussion-and-clinical-implications">11. Discussion and Clinical Implications</h2>
<h3 id="111-clinical-significance-of-results">11.1 Clinical Significance of Results</h3>
<p>The exceptional performance achieved in this study demonstrates the transformative potential of machine learning approaches for acute leukemia diagnosis using cell population data from automated hematology analyzers. The achievement of AUC values exceeding 0.99 for major diagnostic categories represents a paradigm shift from traditional morphology-based diagnosis toward automated, quantitative approaches that could revolutionize leukemia screening in clinical practice.</p>
<p>The clinical implications extend far beyond technical achievement. In resource-limited settings where specialized hematological expertise is scarce, an automated screening tool achieving 97.48% accuracy could serve as a critical first-line diagnostic aid. This capability addresses a significant global health challenge, as delayed diagnosis of acute leukemia directly impacts patient survival outcomes. The rapid processing capability of automated hematology analyzers, combined with machine learning inference, could enable same-day screening results, dramatically reducing the time from presentation to diagnosis.</p>
<h3 id="112-comparison-with-existing-diagnostic-approaches">11.2 Comparison with Existing Diagnostic Approaches</h3>
<p>Traditional acute leukemia diagnosis relies on a multi-step process involving morphological examination, flow cytometry, cytochemistry, and molecular testing. While these approaches provide definitive diagnosis, they face several limitations that our machine learning approach addresses:</p>
<p><strong>Time Efficiency:</strong> Traditional diagnosis typically requires 24-72 hours for complete workup, while our approach provides results within minutes of blood sample processing. This rapid turnaround could be crucial for patients presenting with acute symptoms requiring immediate intervention.</p>
<p><strong>Cost Effectiveness:</strong> The utilization of existing hematology analyzer infrastructure eliminates the need for additional capital equipment investments. Flow cytometry systems cost $200,000-500,000, while our approach leverages analyzers already present in most clinical laboratories.</p>
<p><strong>Operator Independence:</strong> Traditional morphological examination requires highly trained hematopathologists, whose availability is limited globally. Our automated approach reduces dependence on specialized expertise while maintaining diagnostic accuracy.</p>
<p><strong>Standardization:</strong> Machine learning models provide consistent diagnostic criteria across different operators and institutions, reducing inter-observer variability that affects traditional morphological diagnosis.</p>
<h3 id="113-feature-importance-clinical-interpretation">11.3 Feature Importance Clinical Interpretation</h3>
<p>The SHAP analysis revealed clinically meaningful feature importance patterns that align with established hematological knowledge while providing new insights into diagnostic biomarkers.</p>
<p><strong>Neutrophil Parameter Dominance:</strong> The prominence of neutrophil-related features (45.2% of total importance) reflects the biological reality that acute myeloid leukemia primarily affects myeloid cell lineages. The specific importance of NEWY (Neutrophil Y-width) suggests that population heterogeneity in the Y-dimension captures critical information about blast cell presence and maturation abnormalities.</p>
<p><strong>Statistical Feature Significance:</strong> The high ranking of engineered statistical features (NE_mean, NE_std, NE_range) validates our feature engineering approach and suggests that population-level characteristics are more diagnostically informative than individual cell measurements. This finding supports the concept that leukemia represents a population-level disorder rather than isolated cellular abnormalities.</p>
<p><strong>Cross-Cell-Type Ratios:</strong> The diagnostic value of cell population ratios aligns with clinical understanding of hematopoietic balance disruption in acute leukemia. The NE_LY_ratio and related features capture the fundamental alteration in normal blood cell proportions that characterizes leukemic states.</p>
<h3 id="114-hierarchical-diagnostic-strategy">11.4 Hierarchical Diagnostic Strategy</h3>
<p>The performance comparison between major categories and subgroup classifications suggests an optimal hierarchical diagnostic approach that balances sensitivity with specificity:</p>
<p><strong>Stage 1 - Primary Screening:</strong> Utilize the major category classification approach (Dataset 1) for initial screening, achieving 97.48% accuracy with exceptional sensitivity for leukemia detection. This stage would identify patients requiring further evaluation while minimizing false negatives.</p>
<p><strong>Stage 2 - Subtype Classification:</strong> Apply subgroup classification (Dataset 2) to confirmed positive cases, providing detailed diagnostic information necessary for treatment planning. The 81.13% accuracy at this stage, while lower than primary screening, remains clinically acceptable for subtype determination.</p>
<p>This hierarchical approach optimizes both clinical utility and computational efficiency while addressing the different requirements of screening versus diagnostic classification.</p>
<h3 id="115-integration-with-clinical-workflow">11.5 Integration with Clinical Workflow</h3>
<p>The successful implementation of machine learning-based leukemia diagnosis requires careful integration with existing clinical workflows and decision-making processes.</p>
<p><strong>Laboratory Information System Integration:</strong> The machine learning models can be integrated directly into laboratory information systems, providing automated flagging of suspicious samples for immediate physician notification. This integration ensures seamless incorporation into existing laboratory workflows without disrupting established processes.</p>
<p><strong>Clinical Decision Support:</strong> Rather than replacing physician judgment, the system should function as a clinical decision support tool, providing quantitative risk assessment and diagnostic probability scores. This approach maintains physician autonomy while enhancing diagnostic accuracy and confidence.</p>
<p><strong>Quality Assurance Framework:</strong> Implementation requires robust quality assurance protocols including regular model performance monitoring, periodic retraining with new data, and validation against gold-standard diagnoses. These measures ensure sustained accuracy and reliability in clinical practice.</p>
<h3 id="116-economic-impact-analysis">11.6 Economic Impact Analysis</h3>
<p>The economic implications of implementing machine learning-based leukemia diagnosis extend across multiple healthcare system levels.</p>
<p><strong>Direct Cost Savings:</strong> Reduced reliance on specialized testing could generate significant cost savings. Flow cytometry analysis costs $300-800 per test, while our approach utilizes existing complete blood count infrastructure at marginal additional cost.</p>
<p><strong>Efficiency Gains:</strong> Faster diagnosis enables earlier treatment initiation, potentially reducing hospitalization duration and associated costs. Early diagnosis of acute leukemia can reduce treatment costs by 20-30% through timely intervention.</p>
<p><strong>Resource Optimization:</strong> Automated screening allows more efficient allocation of specialized hematopathology resources, focusing expert attention on cases requiring detailed evaluation rather than routine screening.</p>
<p><strong>Global Health Impact:</strong> In resource-limited settings, the approach could enable leukemia diagnosis in facilities lacking specialized expertise, potentially improving outcomes for underserved populations.</p>
<hr />
<h2 id="12-limitations-and-future-directions">12. Limitations and Future Directions</h2>
<h3 id="121-study-limitations">12.1 Study Limitations</h3>
<p>While this study demonstrates exceptional performance in machine learning-based acute leukemia diagnosis, several important limitations must be acknowledged to provide balanced interpretation of results and guide future research directions.</p>
<p><strong>Sample Size Constraints:</strong> The dataset contains 791 patients, which, while substantial for initial validation, represents a relatively modest sample size for machine learning applications in medical diagnosis. Larger datasets would enable more robust model training, better generalization assessment, and more precise confidence interval estimation. The limited sample size particularly affects the subgroup classification task, where individual classes contain fewer than 320 samples.</p>
<p><strong>Single-Institution Data Source:</strong> The data originates from a single institution or analyzer platform, potentially limiting generalizability across different healthcare systems, patient populations, and equipment manufacturers. Hematology analyzers from different manufacturers may exhibit systematic differences in measurement characteristics that could affect model performance.</p>
<p><strong>Cross-Sectional Design:</strong> The study employs a cross-sectional design focusing on diagnosis at a single time point. This approach does not capture the dynamic nature of acute leukemia, including disease progression, treatment response, or relapse detection. Longitudinal studies would provide more comprehensive understanding of the technology's clinical utility.</p>
<p><strong>Limited Clinical Context:</strong> The analysis focuses exclusively on cell population data without incorporating other clinically relevant information such as patient demographics, clinical presentation, medical history, or concurrent laboratory values. Integration of these factors might enhance diagnostic accuracy and clinical applicability.</p>
<p><strong>Gold Standard Validation:</strong> While the study assumes the provided diagnostic labels represent accurate gold standard diagnoses, the specific diagnostic criteria and validation methods used to establish these labels are not detailed. Potential misclassification in the reference standard could affect model training and evaluation.</p>
<h3 id="122-technical-limitations">12.2 Technical Limitations</h3>
<p><strong>Feature Engineering Assumptions:</strong> The feature engineering approach, while comprehensive, makes assumptions about the biological relevance of engineered features. Some statistical combinations may not have direct biological interpretation, potentially leading to models that learn spurious correlations rather than meaningful biological patterns.</p>
<p><strong>Model Interpretability Constraints:</strong> Despite SHAP analysis providing feature importance insights, the complex interactions within ensemble models remain partially opaque. Complete interpretability of decision-making processes may be necessary for certain clinical applications and regulatory requirements.</p>
<p><strong>Computational Scalability:</strong> While current computational requirements are modest, scaling to larger datasets or real-time processing across multiple institutions may require optimization of algorithms and infrastructure. The bootstrap confidence interval calculations, in particular, are computationally intensive.</p>
<p><strong>Hyperparameter Optimization:</strong> The study employs default or minimally tuned hyperparameters for most algorithms. Comprehensive hyperparameter optimization might yield improved performance but could also increase overfitting risk with the current sample size.</p>
<h3 id="123-clinical-implementation-limitations">12.3 Clinical Implementation Limitations</h3>
<p><strong>Regulatory Validation Requirements:</strong> Clinical implementation requires extensive regulatory validation that extends far beyond the technical validation presented. Prospective clinical trials, safety assessments, and regulatory approval processes represent significant barriers to clinical deployment.</p>
<p><strong>Integration Complexity:</strong> Seamless integration with existing laboratory information systems and clinical workflows requires substantial technical development and validation. Interoperability challenges may limit practical implementation across diverse healthcare systems.</p>
<p><strong>Physician Acceptance:</strong> Clinical adoption depends on physician acceptance and trust in automated diagnostic systems. Resistance to artificial intelligence in medical decision-making could limit implementation despite technical success.</p>
<p><strong>Quality Assurance Requirements:</strong> Maintaining model performance in clinical practice requires ongoing quality assurance, performance monitoring, and periodic retraining. These requirements represent ongoing operational challenges and costs.</p>
<h3 id="124-generalizability-limitations">12.4 Generalizability Limitations</h3>
<p><strong>Population Diversity:</strong> The study population's demographic characteristics, disease prevalence, and clinical presentation patterns may not represent global diversity. Performance in different ethnic groups, age ranges, or geographic regions requires specific validation.</p>
<p><strong>Equipment Variability:</strong> Different hematology analyzer models and manufacturers may produce systematically different cell population measurements. Model performance across different equipment platforms requires validation and potential adaptation.</p>
<p><strong>Laboratory Conditions:</strong> Variations in pre-analytical conditions, sample handling, and laboratory practices could affect cell population measurements and model performance. Standardization of these factors is essential for reliable implementation.</p>
<p><strong>Disease Spectrum:</strong> The study focuses on acute leukemia diagnosis but may not address the full spectrum of hematological malignancies or benign conditions that could present with similar cell population abnormalities.</p>
<h3 id="125-future-research-directions">12.5 Future Research Directions</h3>
<p><strong>Multi-Institutional Validation Studies:</strong> Large-scale, multi-institutional studies involving diverse patient populations and equipment platforms would establish generalizability and identify factors affecting performance across different settings. These studies should include international collaboration to address global diversity.</p>
<p><strong>Longitudinal Analysis Development:</strong> Extension to longitudinal analysis could enable monitoring of disease progression, treatment response, and relapse detection. This capability would significantly enhance clinical utility beyond initial diagnosis.</p>
<p><strong>Integration with Multi-Modal Data:</strong> Combining cell population data with other diagnostic modalities including flow cytometry, molecular genetics, and clinical information could enhance diagnostic accuracy and provide comprehensive diagnostic support.</p>
<p><strong>Pediatric Population Studies:</strong> Dedicated studies in pediatric populations would address the unique challenges of childhood leukemia diagnosis, where normal reference ranges and disease presentations differ significantly from adults.</p>
<p><strong>Real-Time Implementation Research:</strong> Studies focusing on real-time implementation in clinical practice would identify practical challenges and optimization opportunities for seamless clinical integration.</p>
<hr />
<h2 id="13-conclusion">13. Conclusion</h2>
<p>This comprehensive technical analysis demonstrates the exceptional potential of machine learning approaches for acute leukemia diagnosis using cell population data from automated hematology analyzers. The study achieved remarkable performance with AUC values exceeding 0.99 for major diagnostic categories and 0.87 for subgroup classifications, representing a significant advancement in automated hematological diagnosis.</p>
<h3 id="131-key-technical-achievements">13.1 Key Technical Achievements</h3>
<p>The technical contributions of this work span multiple domains of machine learning and medical informatics. The comprehensive feature engineering approach successfully transformed raw cell population measurements into clinically meaningful parameters that capture the complex biological relationships underlying acute leukemia pathophysiology. The integration of statistical, relational, and geometric features increased the feature space from 18 to 42 parameters, providing machine learning models with rich information for accurate classification.</p>
<p>The implementation of bootstrap confidence intervals for AUC estimation provides robust statistical assessment of model performance, addressing a critical gap in medical machine learning evaluation. The narrow confidence intervals achieved (typical width &lt; 0.025 for Dataset 1) demonstrate high statistical precision and reliability of performance estimates.</p>
<p>The SHAP-based interpretability analysis reveals clinically meaningful feature importance patterns that align with established hematological knowledge while providing new insights into diagnostic biomarkers. The dominance of neutrophil parameters (45.2% of total importance) reflects the biological reality of acute myeloid leukemia pathophysiology, while the significance of engineered statistical features validates the feature engineering approach.</p>
<h3 id="132-clinical-significance">13.2 Clinical Significance</h3>
<p>The clinical implications of this work extend far beyond technical achievement. The exceptional accuracy achieved (97.48% for major categories) positions this approach as a viable first-line screening tool for acute leukemia, particularly valuable in resource-limited settings where specialized hematological expertise is scarce. The rapid processing capability could enable same-day screening results, dramatically reducing the time from presentation to diagnosis.</p>
<p>The hierarchical diagnostic strategy emerging from the comparative analysis between datasets provides a practical framework for clinical implementation. Primary screening using major category classification achieves exceptional sensitivity for leukemia detection, while secondary subgroup classification provides detailed diagnostic information necessary for treatment planning. This approach optimizes both clinical utility and computational efficiency.</p>
<h3 id="133-methodological-innovations">13.3 Methodological Innovations</h3>
<p>The study introduces several methodological innovations that advance the field of medical machine learning. The comprehensive feature engineering framework provides a systematic approach to extracting clinically meaningful information from automated hematology analyzer data. The integration of multiple machine learning algorithms with consistent evaluation frameworks enables robust performance assessment and algorithm selection.</p>
<p>The implementation of bootstrap confidence intervals for multi-class AUC estimation addresses a significant challenge in medical machine learning evaluation. The SHAP-based interpretability analysis provides clinically relevant explanations of model decisions, essential for physician acceptance and regulatory compliance.</p>
<h3 id="134-comparative-analysis-insights">13.4 Comparative Analysis Insights</h3>
<p>The comparative analysis between major categories and subgroup classifications reveals fundamental trade-offs between diagnostic granularity and classification accuracy. The 16.35 percentage point accuracy reduction from major categories (97.48%) to subgroups (81.13%) reflects the inherent difficulty of fine-grained diagnostic classification. This finding has important implications for clinical implementation strategy and diagnostic workflow design.</p>
<p>The feature importance analysis across datasets reveals both consistent patterns and dataset-specific variations. The persistent importance of neutrophil parameters across both datasets validates their fundamental role in acute leukemia diagnosis, while the increased prominence of lymphocyte parameters in subgroup classification reflects the biological requirements for lineage determination.</p>
<h3 id="135-implementation-readiness">13.5 Implementation Readiness</h3>
<p>The comprehensive code implementation provides a robust foundation for clinical deployment, with modular architecture designed for maintainability, extensibility, and integration with existing laboratory information systems. The detailed documentation and validation frameworks facilitate reproducibility and further development by the research community.</p>
<p>The computational efficiency demonstrated (training times under 6 seconds, prediction times under 0.1 seconds) indicates excellent suitability for real-time clinical applications. The modest computational requirements make implementation feasible even in resource-limited settings with conventional computing infrastructure.</p>
<h3 id="136-regulatory-and-validation-pathway">13.6 Regulatory and Validation Pathway</h3>
<p>The study establishes a strong foundation for regulatory validation and clinical implementation. The comprehensive performance evaluation, statistical rigor, and interpretability analysis address key requirements for medical device approval. The detailed documentation of methodology and implementation facilitates regulatory review and clinical validation studies.</p>
<p>The identification of limitations and future research directions provides a roadmap for addressing remaining challenges and advancing toward clinical deployment. The emphasis on multi-institutional validation, longitudinal analysis, and real-world evidence generation aligns with regulatory expectations for medical artificial intelligence systems.</p>
<h3 id="137-global-health-impact">13.7 Global Health Impact</h3>
<p>The potential global health impact of this work is substantial, particularly for resource-limited settings where specialized hematological expertise is scarce. The utilization of existing hematology analyzer infrastructure eliminates the need for additional capital equipment investments, making implementation feasible across diverse healthcare systems.</p>
<p>The standardized nature of cell population data across different analyzer platforms suggests potential for broad applicability and scalability. Unlike image-based approaches that may require platform-specific adaptations, cell population data follows consistent measurement principles across manufacturers, enhancing the generalizability of developed models.</p>
<h3 id="138-future-research-priorities">13.8 Future Research Priorities</h3>
<p>The study identifies several critical research priorities for advancing automated leukemia diagnosis. Multi-institutional validation studies involving diverse patient populations and equipment platforms represent the highest priority for establishing generalizability. Longitudinal analysis development could enable monitoring of disease progression and treatment response, significantly enhancing clinical utility.</p>
<p>Integration with multi-modal data including flow cytometry, molecular genetics, and clinical information could further enhance diagnostic accuracy and provide comprehensive diagnostic support. Pediatric population studies would address the unique challenges of childhood leukemia diagnosis, where normal reference ranges and disease presentations differ from adults.</p>
<h3 id="139-technological-evolution">13.9 Technological Evolution</h3>
<p>The rapid evolution of machine learning technology presents opportunities for continued advancement in automated leukemia diagnosis. Deep learning approaches specifically designed for tabular medical data could potentially improve performance beyond traditional machine learning methods. Federated learning implementations could enable model training across multiple institutions while preserving patient privacy.</p>
<p>Continuous learning systems that adapt to new data and changing patient populations could maintain performance over time and reduce the need for periodic retraining. Advanced explainable AI techniques could provide more intuitive and clinically relevant explanations of diagnostic decisions.</p>
<h3 id="1310-final-perspective">13.10 Final Perspective</h3>
<p>This work represents a significant step toward the transformation of hematological diagnosis through intelligent automation. The exceptional performance achieved, combined with comprehensive validation and interpretability analysis, demonstrates the maturity of machine learning approaches for medical diagnosis applications.</p>
<p>The careful attention to clinical relevance, regulatory requirements, and implementation challenges positions this work for successful translation from research to clinical practice. The emphasis on enhancing rather than replacing clinical expertise aligns with the fundamental goal of improving patient outcomes through technological advancement.</p>
<p>The comprehensive technical documentation provided enables reproducibility and further development by the research community, facilitating continued advancement in automated hematological diagnosis. The identification of limitations and future research directions provides a roadmap for addressing remaining challenges and advancing toward widespread clinical deployment.</p>
<p>The ultimate success of this approach will be measured not by technical metrics alone, but by its impact on patient outcomes, healthcare accessibility, and diagnostic quality across diverse clinical settings. The foundation established by this work provides a strong platform for achieving these broader goals and realizing the transformative potential of artificial intelligence in hematological diagnosis.</p>
        
        <div class="date-generated">
            <p>Document generated automatically from Technical_Report.md</p>
            <p>Generation timestamp: 2025-06-07T07:49:20.772585</p>
        </div>
    </body>
    </html>
    