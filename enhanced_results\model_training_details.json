{"Logistic Regression": {"training_time": 0.013022, "prediction_time": 0.0, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159}, "Random Forest": {"training_time": 1.073218, "prediction_time": 0.105899, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159, "oob_score": 0.9382911392405063, "has_feature_importance": true}, "Gradient Boosting": {"training_time": 1.551172, "prediction_time": 0.012045, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159, "oob_score": 0.0008251144813036027, "has_feature_importance": true}, "XGBoost": {"training_time": 0.710143, "prediction_time": 0.016561, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159, "has_feature_importance": true}, "SVM (RBF)": {"training_time": 0.102425, "prediction_time": 0.008, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159}, "k‑NN": {"training_time": 0.00201, "prediction_time": 0.0, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159}, "Enhanced MLP": {"training_time": 0.287652, "prediction_time": 0.0, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159}, "LightGBM": {"training_time": 0.3774, "prediction_time": 0.015624, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159, "has_feature_importance": true}, "CatBoost": {"training_time": 2.249257, "prediction_time": 0.0, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159, "has_feature_importance": true}, "Gaussian Process": {"training_time": 26.387534, "prediction_time": 0.015661, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159}, "Extra Trees": {"training_time": 0.885782, "prediction_time": 0.094112, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159, "oob_score": 0.9430379746835443, "has_feature_importance": true}, "Stacking Classifier": {"training_time": 2.137225, "prediction_time": 0.047627, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159}, "Voting Classifier (Hard)": {"training_time": 1.001768, "prediction_time": 0.097561, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159}, "Voting Classifier (Soft)": {"training_time": 0.429085, "prediction_time": 0.042838, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159}, "Tuned XGBoost": {"training_time": 0.176837, "prediction_time": 0.015624, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159, "has_feature_importance": true}, "Tuned LightGBM": {"training_time": 0.121392, "prediction_time": 0.0, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159, "has_feature_importance": true}}