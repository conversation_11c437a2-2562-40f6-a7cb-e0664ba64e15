from sklearn.ensemble import VotingClassifier, StackingClassifier
import numpy as np

def create_voting_ensemble(models, voting='soft'):
    """Create a voting ensemble from the best models"""
    estimators = [(name, model) for name, model in models.items()]
    return VotingClassifier(estimators=estimators, voting=voting)

def create_stacking_ensemble(base_models, meta_model, cv=5):
    """Create a stacking ensemble with specified base models and meta-learner"""
    return StackingClassifier(
        estimators=base_models,
        final_estimator=meta_model,
        cv=cv
    )

def get_top_models(results_df, models, top_n=3):
    """Get the top N performing models based on accuracy"""
    top_models = results_df.index[:top_n].tolist()
    return [(name, models[name]) for name in top_models]

# Usage example:
top_models = get_top_models(results_df, models, top_n=3)
voting_ensemble = create_voting_ensemble(dict(top_models))
stacking_ensemble = create_stacking_ensemble(
    top_models, 
    LogisticRegression(C=0.1, random_state=RANDOM_STATE)
)