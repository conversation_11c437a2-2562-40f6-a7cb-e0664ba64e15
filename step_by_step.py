#!/usr/bin/env python3
"""
Step-by-step analysis execution
"""

import pandas as pd
import numpy as np
import sys

print("Step 1: Loading libraries...")
try:
    from sklearn.model_selection import train_test_split
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.metrics import accuracy_score, f1_score
    from sklearn.preprocessing import LabelEncoder
    print("Libraries loaded successfully")
except Exception as e:
    print(f"Library loading error: {e}")
    sys.exit(1)

print("Step 2: Loading data...")
try:
    data1 = pd.read_csv('data_diag.csv')
    data2 = pd.read_csv('data_diag_maj_sub.csv')
    print(f"Data loaded - Dataset 1: {data1.shape}, Dataset 2: {data2.shape}")
except Exception as e:
    print(f"Data loading error: {e}")
    sys.exit(1)

print("Step 3: Basic data analysis...")
print(f"Dataset 1 columns: {list(data1.columns)}")
print(f"Dataset 1 target values: {data1.iloc[:, -1].value_counts().to_dict()}")

print("Step 4: Preparing for ML...")
X1 = data1.iloc[:, :-1]
y1 = data1.iloc[:, -1]

if y1.dtype == 'object':
    le = LabelEncoder()
    y1_encoded = le.fit_transform(y1)
    print(f"Labels encoded: {list(le.classes_)}")
else:
    y1_encoded = y1

print("Step 5: Training a simple model...")
X_train, X_test, y_train, y_test = train_test_split(X1, y1_encoded, test_size=0.2, random_state=42)

rf = RandomForestClassifier(n_estimators=10, random_state=42)
rf.fit(X_train, y_train)

y_pred = rf.predict(X_test)
accuracy = accuracy_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred, average='weighted')

print(f"Model trained successfully!")
print(f"Results - Accuracy: {accuracy:.4f}, F1-Score: {f1:.4f}")

print("All steps completed successfully!")
