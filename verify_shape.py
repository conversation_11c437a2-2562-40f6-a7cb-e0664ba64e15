import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import roc_auc_score
import warnings
warnings.filterwarnings('ignore')

print("Starting verification script...")

# Load major categories data
df_major = pd.read_csv('data_diag.csv')
X_major = df_major.drop('Diagnosis', axis=1)
y_major = df_major['Diagnosis']

print('=== VERIFICATION: MAJOR CATEGORIES DATASET ===')
print(f'Dataset shape: {df_major.shape}')
print(f'Classes in y_major: {sorted(y_major.unique())}')
print(f'Class counts: {dict(y_major.value_counts().sort_index())}')
print(f'Number of classes: {len(y_major.unique())}')

# Split data
X_train, X_test, y_train, y_test = train_test_split(
    X_major, y_major, test_size=0.2, random_state=42, stratify=y_major
)

print(f'\nTraining set shape: {X_train.shape}')
print(f'Test set shape: {X_test.shape}')
print(f'Training classes: {sorted(y_train.unique())}')
print(f'Test classes: {sorted(y_test.unique())}')

# Train a model
rf = RandomForestClassifier(n_estimators=100, random_state=42)
rf.fit(X_train, y_train)

# Get predictions
y_pred_proba = rf.predict_proba(X_test)

print(f'\n=== PREDICTION ANALYSIS ===')
print(f'Prediction probabilities shape: {y_pred_proba.shape}')
print(f'Expected shape: ({len(y_test)}, {len(y_major.unique())})')
print(f'Shape matches expected: {y_pred_proba.shape[1] == len(y_major.unique())}')

# Check probability structure
print(f'\nProbability column structure:')
print(f'Min probabilities per row: {y_pred_proba.min(axis=1).min():.6f}')
print(f'Max probabilities per row: {y_pred_proba.max(axis=1).max():.6f}')
print(f'Row sums (should be 1.0): min={y_pred_proba.sum(axis=1).min():.6f}, max={y_pred_proba.sum(axis=1).max():.6f}')

# Test AUC calculation
try:
    n_classes_true = len(np.unique(y_test))
    n_cols_scores = y_pred_proba.shape[1]
    
    print(f'\n=== AUC CALCULATION TEST ===')
    print(f'Classes in y_test: {n_classes_true}')
    print(f'Probability columns: {n_cols_scores}')
    print(f'Shape match: {n_classes_true == n_cols_scores}')
    
    if n_classes_true == n_cols_scores:
        auc = roc_auc_score(y_test, y_pred_proba, multi_class='ovr', average='macro')
        print(f'✅ AUC calculation SUCCESS: {auc:.4f}')
    else:
        print(f'❌ SHAPE MISMATCH DETECTED: {n_classes_true} != {n_cols_scores}')
        
        if n_cols_scores > n_classes_true:
            # Apply the fix
            y_pred_proba_adjusted = y_pred_proba[:, :n_classes_true]
            row_sums = y_pred_proba_adjusted.sum(axis=1, keepdims=True)
            row_sums[row_sums == 0] = 1
            y_pred_proba_adjusted = y_pred_proba_adjusted / row_sums
            
            auc = roc_auc_score(y_test, y_pred_proba_adjusted, multi_class='ovr', average='macro')
            print(f'✅ AUC calculation after FIX: {auc:.4f}')
        else:
            print(f'❌ Cannot fix: insufficient columns')
    
except Exception as e:
    print(f'❌ AUC calculation FAILED: {e}')

print('\n=== CONCLUSION ===')
if y_pred_proba.shape[1] == 3:
    print('✅ CORRECT: Model produces exactly 3 probability columns for 3-class problem')
    print('✅ NO SHAPE MISMATCH should occur - this is the expected behavior')
else:
    print(f'❌ ISSUE: Model produces {y_pred_proba.shape[1]} columns instead of 3')
    print('❌ This suggests there may be an issue with model training or data')
