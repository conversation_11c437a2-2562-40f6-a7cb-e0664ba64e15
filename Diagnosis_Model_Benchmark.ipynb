{"cells": [{"cell_type": "markdown", "id": "67eb183e", "metadata": {}, "source": ["# Diagnosis Prediction Benchmark\n", "This Jupyter Notebook replicates the machine learning benchmarking process for predicting the 'Diagnosis' label."]}, {"cell_type": "code", "execution_count": 1, "id": "d1588e6c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.metrics import (accuracy_score, precision_score, recall_score, f1_score,\n", "                             roc_auc_score, confusion_matrix, RocCurveDisplay, ConfusionMatrixDisplay)\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier\n", "from sklearn.svm import SVC\n", "from sklearn.neighbors import KNeighborsClassifier\n", "from sklearn.neural_network import MLPClassifier"]}, {"cell_type": "markdown", "id": "95a18e20", "metadata": {}, "source": ["## Load and prepare the data"]}, {"cell_type": "code", "execution_count": 2, "id": "a860acd6", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\"data_diag.csv\")\n", "X = df.drop(columns=[\"Diagnosis\"])\n", "y = df[\"Diagnosis\"]\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, stratify=y, random_state=42)"]}, {"cell_type": "markdown", "id": "6d1a119d", "metadata": {}, "source": ["## Define models"]}, {"cell_type": "code", "execution_count": 3, "id": "f82b5c32", "metadata": {}, "outputs": [], "source": ["models = {\n", "    \"Logistic Regression\": Pipeline([\n", "        (\"scaler\", StandardScaler()),\n", "        (\"clf\", LogisticRegression(max_iter=1000, multi_class=\"multinomial\", random_state=42))\n", "    ]),\n", "    \"Random Forest\": RandomForestClassifier(n_estimators=300, random_state=42),\n", "    \"Gradient Boosting\": GradientBoostingClassifier(random_state=42),\n", "    \"SVM (RBF)\": Pipeline([\n", "        (\"scaler\", StandardScaler()),\n", "        (\"clf\", SVC(kernel=\"rbf\", probability=True, random_state=42))\n", "    ]),\n", "    \"k-NN\": Pipeline([\n", "        (\"scaler\", StandardScaler()),\n", "        (\"clf\", KNeighborsClassifier(n_neighbors=5))\n", "    ]),\n", "    \"MLP (ANN)\": Pi<PERSON>ine([\n", "        (\"scaler\", StandardScaler()),\n", "        (\"clf\", MLPClassifier(hidden_layer_sizes=(64, 32), max_iter=1000, random_state=42))\n", "    ])\n", "}"]}, {"cell_type": "markdown", "id": "a57603b4", "metadata": {}, "source": ["## Train and evaluate models"]}, {"cell_type": "code", "execution_count": 4, "id": "6c16e4bc", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\sklearn\\linear_model\\_logistic.py:1247: FutureWarning: 'multi_class' was deprecated in version 1.5 and will be removed in 1.7. From then on, it will always use 'multinomial'. Leave it to its default value to avoid this warning.\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\joblib\\externals\\loky\\backend\\context.py:136: UserWarning: Could not find the number of physical cores for the following reason:\n", "[WinError 2] The system cannot find the file specified\n", "Returning the number of logical cores instead. You can silence this warning by setting LOKY_MAX_CPU_COUNT to the number of cores you want to use.\n", "  warnings.warn(\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\joblib\\externals\\loky\\backend\\context.py\", line 257, in _count_physical_cores\n", "    cpu_info = subprocess.run(\n", "               ^^^^^^^^^^^^^^^\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\subprocess.py\", line 548, in run\n", "    with Popen(*popenargs, **kwargs) as process:\n", "         ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\subprocess.py\", line 1026, in __init__\n", "    self._execute_child(args, executable, preexec_fn, close_fds,\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\subprocess.py\", line 1538, in _execute_child\n", "    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,\n", "                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Accuracy</th>\n", "      <th>Precision</th>\n", "      <th>Recall</th>\n", "      <th>F1</th>\n", "      <th>ROC AUC</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Random Forest</th>\n", "      <td>0.968553</td>\n", "      <td>0.959482</td>\n", "      <td>0.943320</td>\n", "      <td>0.950102</td>\n", "      <td>0.994514</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SVM (RBF)</th>\n", "      <td>0.968553</td>\n", "      <td>0.940185</td>\n", "      <td>0.957011</td>\n", "      <td>0.946619</td>\n", "      <td>0.992553</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Grad<PERSON></th>\n", "      <td>0.962264</td>\n", "      <td>0.955072</td>\n", "      <td>0.926653</td>\n", "      <td>0.939717</td>\n", "      <td>0.990131</td>\n", "    </tr>\n", "    <tr>\n", "      <th>k-NN</th>\n", "      <td>0.955975</td>\n", "      <td>0.922898</td>\n", "      <td>0.937368</td>\n", "      <td>0.929334</td>\n", "      <td>0.976141</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MLP (ANN)</th>\n", "      <td>0.955975</td>\n", "      <td>0.930198</td>\n", "      <td>0.923677</td>\n", "      <td>0.926301</td>\n", "      <td>0.990855</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Logistic Regression</th>\n", "      <td>0.924528</td>\n", "      <td>0.904602</td>\n", "      <td>0.877094</td>\n", "      <td>0.888935</td>\n", "      <td>0.982976</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     Accuracy  Precision    Recall        F1   ROC AUC\n", "Random Forest        0.968553   0.959482  0.943320  0.950102  0.994514\n", "SVM (RBF)            0.968553   0.940185  0.957011  0.946619  0.992553\n", "Gradient Boosting    0.962264   0.955072  0.926653  0.939717  0.990131\n", "k-NN                 0.955975   0.922898  0.937368  0.929334  0.976141\n", "MLP (ANN)            0.955975   0.930198  0.923677  0.926301  0.990855\n", "Logistic Regression  0.924528   0.904602  0.877094  0.888935  0.982976"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["metric_names = [\"Accuracy\", \"Precision\", \"Recall\", \"F1\", \"ROC AUC\"]\n", "metrics = {m: [] for m in metric_names}\n", "prob_scores = {}\n", "\n", "for name, model in models.items():\n", "    model.fit(X_train, y_train)\n", "    y_pred = model.predict(X_test)\n", "\n", "    if hasattr(model, \"predict_proba\"):\n", "        y_score = model.predict_proba(X_test)\n", "    else:\n", "        y_score = model.decision_function(X_test)\n", "        if y_score.ndim == 1:\n", "            y_score = np.column_stack([1 - y_score, y_score])\n", "    prob_scores[name] = y_score\n", "\n", "    metrics[\"Accuracy\"].append(accuracy_score(y_test, y_pred))\n", "    metrics[\"Precision\"].append(precision_score(y_test, y_pred, average=\"macro\", zero_division=0))\n", "    metrics[\"Recall\"].append(recall_score(y_test, y_pred, average=\"macro\", zero_division=0))\n", "    metrics[\"F1\"].append(f1_score(y_test, y_pred, average=\"macro\", zero_division=0))\n", "    metrics[\"ROC AUC\"].append(roc_auc_score(y_test, y_score, multi_class=\"ovr\"))\n", "\n", "results_df = pd.DataFrame(metrics, index=models.keys()).sort_values(\"Accuracy\", ascending=False)\n", "results_df"]}, {"cell_type": "markdown", "id": "741dd611", "metadata": {}, "source": ["## Visualise performance metrics"]}, {"cell_type": "code", "execution_count": 5, "id": "6afb5a6a", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA90AAAJOCAYAAACqS2TfAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjEsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvc2/+5QAAAAlwSFlzAAAPYQAAD2EBqD+naQAAgcJJREFUeJzt3QmcjfX7//EL2dckW9mi7EvZKkoiQkSKqEhSkaW0WYoQWlERspO9ooVIChUiEkr2rbKWEFni/B/vz+9/n++ZMcOM5p4zM+f1fDxOY856z8zdfd/X57o+1ydVIBAIGAAAAAAASHCpE/4tAQAAAACAEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAACfEHQDAOIlVapU9uKLL8b7dTt27HCvHT9+vCUlkyZNshIlSljatGktR44c4d4c+ED7q/a9xHT27FkrU6aM9e/fP1E/N6n7448/LHPmzDZ37txwbwoAJBqCbgBIhhS4KojQ7Ztvvjnn8UAgYAUKFHCP33HHHZacLFq0KPiz6aZg+KqrrrJWrVrZtm3bEvSzfvnlF3vwwQetaNGiNmrUKHv33XcT9P0jNbhNnTq17d69+5zHjxw5YhkzZnTP6dix40V9xoABA2z27NmW1E2dOtX9DryfM3SfPt9N+/9/dfz4cfe3iM97aVCsTZs27v+FDBkyWN68ee3mm2+23r17X9Q2KKiOaXDusssus4cfftheeOGFi3pfAEiOLgn3BgAALp4ujqdMmWLVq1ePcv/ixYvt119/tfTp01ty1blzZ6tcubKdPn3aVq9e7QLiOXPm2Lp16yx//vwJ8hkKSpSRfPPNN61YsWIJ8p4wt98p6Hz22Wej3P/hhx/+5/dW0H333Xdb48aN4/ya559/3rp162aJ6bXXXrN7773XsmfPHqyoCDVx4kRbsGDBOfeXLFkyQYLuPn36uH/fcsstF3z+li1b3P9rGhB56KGHrHDhwrZnzx73/90rr7wSfK/4Bt3Dhg2LMfB+7LHH7K233rIvv/zSbr311ni/NwAkNwTdAJCM1a9f32bOnOkuYC+55H+HdAXiFStWtIMHD1pyddNNN7ngSpSBu+aaa1wgPmHCBOvevft/eu9jx465Etf9+/e77xOyrFwBT6ZMmSzS98uYgm7tlw0aNLAPPvggUbbD+zvr/43Q/z/89sMPP9iPP/5ob7zxRvC++++/P8pzli9f7oLu6PeHw+DBg+3vv/+2NWvWWKFChaI85v0/kpA0sKDSe1XsEHQDiASUlwNAMtaiRQs3R1IX755Tp07Z+++/by1btow1EHnqqadc+bkyksWLF7fXX3/dlaSHOnnypD355JN2+eWXW9asWa1Ro0Yuex6T3377zWXI8uTJ496zdOnSNnbs2AT9Wb2L8+3btwfv++yzz1xwrsBK26iA7qefforyOpWPZ8mSxbZu3eqCQT3vvvvuc9k8r3RWP2P0uervvPOO+zn08yiz/vjjj9tff/0V5b2VRVTwsGrVKleKq2C7R48ewfnr+r0q26fyeD1Wp04dV3Ks33W/fv3syiuvdNnFO++80/78888o7/3RRx+5n0efrW1Q2a9ec+bMmRi34eeff7aaNWu6z7niiivs1VdfPed3eOLECfczagBDVRL58uWzu+66y/1uPMr8DxkyxP3seo7+po8++qgdOnQozn8r7XsK4FS+79m7d6/LbMa2X2p/099DFQf6ebV/KmjX/R79TrX/auDFK8fW3ze0tF2/B33GpZdeGqwAiW1O93vvvWdVqlRxvzM9X3/Dzz//PPj4999/b3Xr1rVcuXK5v1ORIkXcfn4hKn9Ply6de7/4iOvv/nzbpX1P+7MoQ+39ns7Xh0F/f+2L0QNuyZ079zn3Xej/O/1NtN9LaOl8qNtuu80++eSTc447AJASkekGgGRMgeMNN9zgsor16tULXhAfPnzYlbYqAx5KF7gKnr/66itr27atVahQwebPn2/PPPOMC5yV8fJo3qWCEgUwN954owuYdHEd3b59++z6668PztPVBb+2Qe+vObxPPPFEgvysXmCoOaGistzWrVu74EMlsMowDx8+3AVayjTqd+P5999/3fP0mAJhBVkKDFTiO2vWLPc6BeblypVzz1eAooCldu3a1r59e9u4caN7zsqVK+3bb79188w9GvTQ716/b2UtFSh5Jk+e7AZBOnXq5IJqBcLNmjVzAwgqbX/uuedcae/bb79tTz/9dJSBCmUBtU1du3Z1X/X779Wrl/udqnQ5lIKy22+/3QXQen8Nuui9y5YtG9wvFKxrfv/ChQvdtnbp0sWOHj3qBmzWr1/vgnpRkKfPVnWBKgs0yDF06FD3O43+s8dGwaaCOGW2+/bt6+6bPn26+zli2ocUbGq/VH+CRx55xGVCNY1A++OmTZuCc7j1N9d+qUBZzxNvuz333HOPXX311a4M/XwBnf6++jtr39Y2Kkj+7rvv3O9ZgyPK8Oqr9meVpqsaQgFtXErkly5d6gZC4vK7ChWX3/2Ftkv3a1/VftukSRO3T4i3b8dEwfYXX3wRp3LvuPx/p5/j999/j7F83qNKHP19FazrdwUAKVoAAJDsjBs3TtFEYOXKlYGhQ4cGsmbNGjh+/Lh77J577gnUrFnT/btQoUKBBg0aBF83e/Zs97qXXnopyvvdfffdgVSpUgW2bNnivl+zZo17XocOHaI8r2XLlu7+3r17B+9r27ZtIF++fIGDBw9Gee69994byJ49e3C7tm/f7l6rbT+fr776yj1v7NixgQMHDgR+//33wJw5cwKFCxd226if+ejRo4EcOXIE2rVrF+W1e/fudZ8Zen/r1q3d+3Xr1u2cz9LPocf0OZ79+/cH0qVLF6hTp07gzJkzwfv1e/a2y1OjRg1334gRI6K8r/ezXn755YG//voreH/37t3d/eXLlw+cPn06eH+LFi3cZ544cSJ4n/d7C/Xoo48GMmXKFOV53jZMnDgxeN/JkycDefPmDTRt2jR4n7Zbzxs0aNA573v27Fn39euvv3bPmTx5cpTH582bF+P95/t9Pv3004FixYoFH6tcuXKgTZs27t96zuOPPx58bNKkSYHUqVO7zw+l36ue++233wbvy5w5s/ubxvbZ+l3G9phn8+bN7vOaNGkS5W8c+ruYNWtW8P+x+Lryyiuj/O5jop8/dJvi+ruPy3bp9x/9/9PzWb9+fSBjxozuNRUqVAh06dLFHSuOHTsW5Xnx+f8u+s8X3dKlS93j06dPj9M2AkByRnk5ACRzymz+888/9umnn7rMpb7GVsKr5kZp0qRxWbRQKjdXLKQMtfc8if686FlrvUbzcxs2bOj+rTnk3k2ZMGXc1YzpYqhcVlk7lVcrO+qVFVeqVMll0FTqrfL60M/Uz1a1alWXyY9Omb+4UMZP2Wn9rOrC7WnXrp1ly5bNNXMLpVJoZSZjoqyr10hLtG2ijHjoHGPdr89UtYFHZcMe/V3186mkV5nF0LJtUQY5dG6wsrbKBod2e9ffSeXIyrpH55X+qj+Atlelv6G/V2Ul9Rkx/V5jo31QWXxVB3hfY9sv9bnKbmvpttDP9bKu8flcNem6EGXOlV1X5UDo3zj0d+HN89f/T2rmFx+qflC5enzE9Xf/X7YrNipn13QA7UPKmquxoBrVqWpDXf09F/P/XWy8309y7jsBAHFFeTkAJHMKTFUGrVJeBWQqI/YakEW3c+dOF8RqHmZMHZP1uPdVwUj00l3N/w514MABdxGuzuKxLbd1sY2YFBApyNQFvYJFbaMXqG7evNl9ja0UVsFxKL1O5c5x4f0Oov+sCmQ1N9t73KP503osJgULFozyvReAa75yTPeHzt1V2a26bqvkVyXloTSYEUo/W/Q5swpq1q5dG6U8Xz/T+RqK6feq945pHm98/5bXXnutC6K1XypQ1BJUsf299LkbNmwIzkX+L5+r+c0Xot+F9u9SpUrF+pwaNWpY06ZNXRm6yqA1d16BqAYO4rIqQHznKsf1d/9ftys2muevUnAdPzQvXkG9pkOojF+/Ux1j4vv/XVx+P4m9fjoAhANBNwCkALrgViZWzao0hzchu3Gfj7KFogyZ5nnG5HxzSc9H85F1oX++z1WQoGAuuuiBpYKR6BnNhBKakY5OAwbxud8LRDSQoeBKQYzmG3trJ6tqQHO1vZ8/ru8XV3pfBX2aix6T2ILi8+2Xmu+rQZ7mzZvH+jfQ5+rvPWjQoBgfjz5IcbF/j/hQMKi58eoyroZf6n2g6gt1JNd9yj7HRn0H4tN4Lj6/+/+yXXGhfUl/C93UL0LN+bRN+n8xvv/fnY/3+9GAGgCkdATdAJACqGGSmhfpolsNqy7UMEnlyqHZbq9c2eterK+6wPayox41FAvldTZXdiy2ANkPXgZeQUpCf673O9DPqsy2R+XfamyVGD+nmqypRFnNsUI7YId2br+Y35kahakkObYGX3qO9o9q1aolSPCqoFsVC1rzObaGWt7naomtWrVqXTDzmRCZUX2e9m9ldNVM8HzUJFC3/v37u6y9Ot9PmzbNNXSLjTL88f1bxfd3f77tSqjssaZyiP5+3jbG9f+7C22D9/tJiHXJASCpY043AKQAym4po6huzJpfHRstmaUAWR2RQ6lMVRfJXqdr72v07udazih6VkylrpovrA7Y0an83A+aL64ssDpUxzSv9b98roIJlYvrZw/NFI8ZM8aV/8bUfTuheZnr0M9X0K9lzC6W/k6aPxv9bx/6OeoPoP1DS5NFpw7w0ZdMuxAFadpnBg4c6OaYx0afq/nsofOHPepXoPn8Hi1TFd/tiE7l2Mq6q4ogetWA97tQJjZ6pYAXoIcuYxYTZYj1/8OFnhcqrr/7uGyXt058XH9PX3/9dYz/H3m9HbyBt/j8f6e/0/m2QcvsaVqF5pMDQEpHphsAUojYyrtDKSBXuWjPnj1dw6Ty5cu7dYm1JrQah3mZLF3Eq1mSgjwFmlpWSUtNqSFWdC+//LJroKRGSipx1zxZLY+lUmhl7qKvP50QdOGvQYYHHnjArrvuOrcElrLuu3btco3OlC2MKbiMC71P9+7d3ZxZLcOlpayU9dbvonLlylEalvlFv2/NydbfVM3sNCCiTPF/WdO4VatWbok0LUG2YsUKN19ewaz+Rh06dHBrhaukXRUTCpLVWEtLUykrrrm8avSlBlux9QuIjZYmuxD9HWfMmOGaoGlf0t9PAagqMHS/Sqi9rKsai2mbVYqu/gSab+w1qIsrrQWu/wcU4Or3oGW1NAVBzd70nvr51bRPf3NVkej/C1WHaFBA+54Gr85Hv0u99+LFi93vMC7i+ruPy3YpU67/D1X1ornaOXPmdMtyxbY0l5b+UhCs34M3HUT//2p/0Wu9Borx+f9OfyfR/qtgXQNJen5oUzYdj5jTDSAihLt9OgDgvy0Zdj7Rlwzzlv158sknA/nz5w+kTZs2cPXVVwdee+214FJJnn/++SfQuXPnwGWXXeaWaWrYsGFg9+7dMS5FtG/fPrdEUIECBdx7armqWrVqBd59993gc+K7ZNjMmTMv+HvQc+vWreuWK8qQIUOgaNGigQcffDDw/fffB5+j5aW0/TGJacmw0CXCSpQo4X6ePHnyBNq3bx84dOhQlOdoua7SpUuf81rvZ9XvNS4/W0x/Ty2Tdf3117ulnPS3evbZZwPz5893z9P7XGgb9HPr7x9Ky5D17NkzUKRIkeDfScvFbd26Ncrz9HerWLGi+2wtR1e2bFn3+Vq+7XzO9/sMFX3JMDl16lTglVdecT9L+vTpA5deeqnbhj59+gQOHz4cfN4vv/wSuPnmm4NLXHnLh53vs6MvGRa6jNq1114b/Dz9LhcsWOAeW716tVt+rGDBgu7x3LlzB+64444o+9b5lCtXzi2nF5vYltS60O8+rtulJbn0PlqK7kLLh2lf0/aUKVPG/b+kfUPvr/+Xou8bcf3/7t9//w106tTJLZunpf5Cf9YNGza477/44os4/CYBIPlLpf+EO/AHAABISVSZ8Pjjj7sscGI1NkwulDlfsmSJy66T6QYQCQi6AQAAEpjmiqtUW9M0VMqO/6MGgWpWqGkDFyrTB4CUgqAbAAAAAACf0L0cAAAAAACfEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8usQjsJvr7779b1qxZWaYCAAAAAHBR1JP86NGjlj9/fkudOvZ8dsQF3Qq4CxQoEO7NAAAAAACkALt377Yrr7wy1scjLuhWhtv7xWTLli3cmwMAAAAASIaOHDniErpejBmbiAu6vZJyBdwE3QAAAACA/+JC05ZppAYAAAAAgE8IugEAAAAA8AlBNwAAAAAAPom4Od0AAAAAkJjOnDljp0+fDvdmIJ7Spk1radKksf+KoBsAAAAAfFrHee/evfbXX3+Fe1NwkXLkyGF58+a9YLO08yHoBgAAAAAfeAF37ty5LVOmTP8pcEPiD5gcP37c9u/f777Ply/fRb8XQTcAAAAA+FBS7gXcl112Wbg3BxchY8aM7qsCb/0dL7bUnEZqAAAAAJDAvDncynAj+fL+fv9lTj5BNwAAAAD4hJLy5C0h/n4E3QAAAAAA+ISgGwAAAAAAn4S1kdqSJUvstddes1WrVtmePXts1qxZ1rhx4/O+ZtGiRda1a1f76aefrECBAvb888/bgw8+mGjbDAAAAAD/ReFucxLts3a83OCiX7ts2TKrXr263X777TZnTuJtc0oT1kz3sWPHrHz58jZs2LA4PX/79u3WoEEDq1mzpq1Zs8aeeOIJe/jhh23+/Pm+bysAAAAARJIxY8ZYp06dXLL0999/D9t2nDp1ypKzsAbd9erVs5deesmaNGkSp+ePGDHCihQpYm+88YaVLFnSOnbsaHfffbcNHjzY920FAAAAgEjx999/2/Tp0619+/Yu8Tl+/Pgoj3/yySdWuXJly5Ahg+XKlStKTHfy5El77rnnXGVy+vTprVixYi6AF71Pjhw5orzX7NmzozQse/HFF61ChQo2evRoF//pM2TevHku867Xaxm2O+64w7Zu3RrlvX799Vdr0aKF5cyZ0zJnzmyVKlWy7777znbs2GGpU6e277//PsrzhwwZYoUKFbKzZ8+aX5LVnG6VN9SuXTvKfXXr1nX3AwAAAAASxowZM6xEiRJWvHhxu//++23s2LEWCATcYyo1V5Bdv359++GHH2zhwoVWpUqV4GtbtWplU6dOtbfeess2bNhgI0eOtCxZssTr87ds2WIffPCBffjhh67K2auU1lRjBc76TAXR2g4vYNZAQY0aNey3336zjz/+2H788Ud79tln3eOFCxd2seS4ceOifI6+13RlvVeKnNMdX3v37rU8efJEuU/fHzlyxP7555/g4uWhNMqim0fPBQAAAADETplpBduiOd2HDx+2xYsX2y233GL9+/e3e++91/r06RN8vqYNy6ZNm1zAvmDBgmDC9KqrrrqokvKJEyfa5ZdfHryvadOmUZ6jgQA9/vPPP1uZMmVsypQpduDAAVu5cqXLdIuy7B5NTX7sscds0KBBLgO/evVqW7dunX300Ufmp2SV6b4YAwcOtOzZswdvKnEAAAAAAMRs48aNtmLFClemLZdccok1b948WCKuzHOtWrVifK0eS5Mmjcs4/xeFChWKEnDL5s2b3TYpiM+WLZvLXsuuXbuCn33ttdcGA+7o1LRb26YG3l6pu/qFee/jl2QVdOfNm9f27dsX5T59r194TFlu6d69uxuV8W67d+9OpK0FAAAAgORHwfW///5r+fPndwG3bsOHD3fl3oqpYou95HyPicq4vTJ1z+nTpy06zceOrmHDhvbnn3/aqFGj3Dxt3UIbrV3os9OlS+dK31VSrtcoM/7QQw+Z35JVefkNN9xgc+fOjXKfyhZ0f2xUNqAbkFCGPfal75/x+Ihbff8MAAAAIDoF2yrrVvPqOnXqnJMp1lztcuXKuTnVbdq0Oef1ZcuWdXOoVYoevR+XKHt99OhRNz/bC6y9Odvn88cff7gMvALum266yd33zTffRHmOtkvN1xSYx5btVom5StHfeecd97PeddddlqKDbk101wT50CXB9AvXL6hgwYIuS61J8Pqji+rvhw4d6ibDa0Tiyy+/dPMFWDMOQFLHYA0AAEgOPv30Uzt06JC1bdvWTc8NpTnVyoK/9tprrry8aNGibm63glclR9WxXKXarVu3dvGaGqmVL1/edu7cafv377dmzZpZ1apVLVOmTNajRw/r3Lmzy1ZH74wek0svvdR1LH/33XctX758rqS8W7duUZ6j0vMBAwa4wQFNM9bz1OhNGXsvUatVsK6//nq3rdrGC2XHk33Qra5zqqH3qBOd6I+kX/yePXuC9fmidvEKsJ988kl788037corr3QjGepgDgBASpIYAzXCYA0Q2TjWIDoF1cpQRw+4vaD71VdfdUnSmTNnWr9+/ezll192031vvvnm4PNUiq6gukOHDi5DrYSqvhe99r333rNnnnnGZa0VvGuJsEceeeSCZenTpk1zgboy1eqqrqBejd1Cy8c///xze+qpp1xndQ0GlCpVyoYNGxblvTSgsHTp0kQpLZdUgegF9SmcupdrB9JcBO0cQHyRscTFYL9BfHEhDCAxcKzxz4kTJ1wlb+g600gaNFigQYO1a9f+p79jXGPLZNVIDQAAAACAi53evH79ejdluVOnTon2ucmqkRoAAAAAILz27zzi+2fkLpTwVckdO3Z0jeA05zuxSsuFoBsAAAAAkOKNHz8+Tk3bEhrl5QAAAAAA+IRMNwAAQApB00YASHrIdAMAAAAA4BOCbgAAAAAAfEJ5eRJWuNucRPmcHS83SJTPQcrZb9hnAAAAgLgh0w0AAAAAgE/IdANAhKOqBgAAhFuewtlt3MjJVr/uHRd8bqpUqWzWrFluve3kgKAbAAAAABLTi9kT8bMOx/slDz74oE2YMMH9O23atFawYEFr1aqV9ejRwy65xJ8Qct2KTZY9e444PXfPnj126aWXWnJB0I3E+Z/+Iv5nBwAAABAet99+u40bN85Onjxpc+fOtccff9wF4N27d4/yvFOnTlm6dOn+8+flzp0nzs/NmzevJScE3QCSrLITyvr+Getar/P9M/D/McCXotC0EfHFVBYgeUmfPn0wuG3fvr0r5/74449t48aNtu/3g1ah/LU2buJoF3B//806++33X+3F/j1t0ZKvLHXqVFa18o32Uu+XrWCBQsH3nDJjko0YNdS279xmObJfanfUa2QD+75+Tnm5AvmOHTvaBx98YIcOHbI8efLYY489Fgz4o5eXr1u3zrp06WLLli2zTJkyWdOmTW3QoEGWJUuWYOb+r7/+surVq9sbb7zh3v/ee++1IUOGuIEEv9FIDQAAAABwXhkzZnTBqny9dLFt3bbFZrw3294bO8NOnz5t97a6yzJnzmIfzfzMPvngc8ucObO1aN00+Jrxk0Zb9xeetvtbPGiL5i2ziaOnWuFCV8X4WaPHj3AB/owZM1yQP3nyZCtcuHCMzz127JjVrVvXlZuvXLnSZs6caV988YUL2kN99dVXtnXrVvdVpfPjx493t8RAphspxoYSJRPng24ZljifAwAAAIRZIBCwhQsX2vz5861Tp0524MABy5Qxkw16+e1gWfn7s6bb2bNnbfArQ10WWt587R27plxBW7r8a7vl5lo2eOjr9li7jvbIQ+2D731t+Yoxfuavv/9qV199tctM6/0KFfpftjy6KVOm2IkTJ2zixIku0JehQ4daw4YN7ZVXXnFZclFQrvvTpEljJUqUsAYNGrifq127duY3Mt0AAAAAgCg+/fRTV56dIUMGq1evnjVv3txefPFF91jJEqWizOP+acM6VzJ+VekrrEip/O5WvEJhO3HyhO3Yud0OHDxge/ftsZturBGnz7737pa2Zs0aK168uHXu3Nk+//zzWJ+7YcMGK1++fDDglmrVqrlBAGXJPaVLl3YBtydfvny2f/9+SwxkugFENCokAAAAzlWzZk0bPny4C67z588fpWt5poz/C3C9Eu9yZSrY8DdHnfM+l+XMZalTxy/Xq/favn27ffbZZ65UvFmzZla7dm17//33L/rniT53Wxl0BeaJgaAbKaYh1gzfPwEAAACIDMocFytWLE7PLVumvH306YeW67LLLWvWbDE+p8CVBd1c8Oo33hyn98yWLZvLrut29913u27qf/75p+XMmTPK80qWLOnmZivw97Ld3377rQv0lSlPCigvBwAAAABctKaNm1nOnJdZq3YtbfmKpbZz9w77dtnX1uPFZ+33Pb+55zzzRHfXuXzUuBG2bftWW7t+jY0ePzLG9xsxeqhNnTrVfvnlF9u0aZNrjqZO6jlynLuO93333edK4Fu3bm3r1693jdI09/yBBx4IzucONzLdAJLm0k9SpGDifA6QFKclMCUBAJBMqLHaR9M/s34v97aHHrvf/v77b8ubN5/dVK2GZc2S1T2n+d0t7eTJEzZyzDvWZ8DzlvPSy+yO+nfG+H7qgv7qq6/a5s2b3TzsypUru7XCYypT1xJhavKmJcP0vNAlw5IKgm4AABDREmMK1LrW63z/DADJyIuHLSk731Jaemz/ziPn3J87dx57e9CI875vq/secreY7Nvxv9/JAy0etKe6dT5vR/VQZcuWtS+//PK82xyd1uhOLJSXAwAAAADgE4JuAAAAAAB8Qnk5AACAz1ieMIX1HUnipcEAkhaCbgBAipEYc3OFJQoBJAaaNgIpA0E3AAAAEA8M8CWewt3m+P4ZO15u4PtnILIRdAMAgKSJ5QkBACkAjdQAAAAAAPAJQTcAAAAAAD4h6AYAAAAAwCcE3QAAAACAJCVVqlQ2e/Zs9+8dO3a479esWWPJEY3UAAAAACAFdsCXda3Xxfs1Dz74oE2YMMH9+5JLLrErr7zS7rnnHuvbt69lyJDBh61M2Qi6AQAAAABR3H777TZu3Dg7ffq0rVq1ylq3bu2yza+88kq4Ny3ZIegGAAAAELn8Wp4wSwGzam+Y7f/H7JJUZvmvteQkffr0ljdvXvfvAgUKWO3atW3BggUu6D579qy9PXywTZo63g4c2G9XFSlmXTs/Yw3rNw6+/pdNG6zfy71t+YqlFggErEypsvbW6+9Y4UJX2Q8/rrIBr/W19T+ttdP//mtlSpa1vr0GWLkyFSwlYk43AAAAACBW69evt6VLl1q6dOnc92++84bN/HCavdZ/sC1esNwebdvBHn/iEVu6/Bv3+J69v1vjZvUsfbp09sGUj23BJ4utxT3327//nnGP/33sb2vetKV9PHO+zZ31hRUpcpW1fPAe+/vvo5YSkekGAAAAAETx6aefWpYsWezff/+1kydPWurUqW3o0KHu328OG2Qz3/vIKles4p5buGAR++77ZTZxyji78frqNnbiKMuaNZuNfHucpU2b1j2n6FXFgu990401onzWGwPfsqvLFbSl331rdWrdbikNQTcAAAAAIIqaNWva8OHD7dixYzZ48GDXUK1p06b2008/2T//HLdmD/yvlFxOnz5lZUqVc//+6ed1dn3lG4MBd3T7D+y3l9/o5zLjB/84aGfOnHHv+dvvuy0lIugGAAAAAESROXNmK1bs/7LTY8eOtfLly9uYMWOsTJky7r7JY2dYvrz5orwmXbr07uuFOpx3fuoxO/TXn/ZS71fsyisKuDL0BnfdZqdOnbaUiKAbAAAAABArlZb36NHDunbtaps2bbL06dLbb7//6krJY1KqRBmb/sEU1/k8bQzZ7hWrvrNX+r1htWvWcd/rvf748w9LqWikBgAAAAA4L63TnSZNGhs5cqS1f6ST9erX3aa/P8V27Nxma9evsdHjR7rv5aHWj7imaI92amNr1q62bdu3usZrW7Zudo9fVfgqmzlrmm3astFW/fC9dXiinWXMkNFSKjLdAAAAAJBC/bN+fbxfc+bQITtz9Og5r3307rvt1YED7btv1ttlOXPZW+8Msp27d1i2bNmtXOny1uXxp9zzcl6a096f8on1HfCCNW7ewAXrWjKscqWq7vHBrwy1p3t0sdsa3Gz5819hPZ7pZX36v2ApFUE3AAAAACSida3XJZkAOybv9u8f4/1PP/ywux3NlNkeeai9u8WmdMkyNn3SrBgfK1umvM3/eFGU+0LX+Bat7e0pXLhwlO+TG8rLAQAAAADwCUE3AAAAAAA+IegGAAAAAMAnzOkGAAAAAJ/9dPAn3z/jKt8/AReDTDcAAAAAAD4h6AYAAAAAwCcE3QAAAAAA+ISgGwAAAAAAnxB0AwAAAADgE4JuAAAAAAB8wpJhAAAAAJCIUle/25f33RHDfYXfnxnv93mkZ0977+OPz7l/3Zw5tmf/fnvtvWds7bo1tm//Xhs3crLVr3vHRW5xZCDoBgAAAABEcVu1ajbypZei3Hf5pZfalp07rXTJMtbynvutzWP3h237khOCbgAAAABAFOnTpbO8uXKdc3/dm26yG+vfF5ZtSq6Y0w0AAAAAgE/IdAMAAAAAovhsyRK7vEqV4Pd1qle3yYMGhXWbkiuCbgAAAABAFDUqV7Y3X3gh+H2mjBnDuj3JGUE3AAAAACAKBdlFCxYM92akCMzpBgAAAADAJ2S6AQAAAABx8vfx47Z+19rg97t277T1P621HDkutSuvKBDWbUuqCLoBAAAAAHGy+qef7PaHHgp+3/ulHu5r86Yt7a03hodxy5Iugm4AAAAASERnv3nfl/e9am8gQd7n3f79Y33s5sqVbd+OwwnyOZGCOd0AAAAAAPiEoBsAAAAAAJ8QdAMAAAAA4BOCbgAAAAAAfELQDQAAAACATwi6AQAAAADwCUE3AAAAAAA+IegGAAAAAMAnBN0AAAAAAKTUoHvYsGFWuHBhy5Ahg1WtWtVWrFhx3ucPGTLEihcvbhkzZrQCBQrYk08+aSdOnEi07QUAAAAAIK4usTCaPn26de3a1UaMGOECbgXUdevWtY0bN1ru3LnPef6UKVOsW7duNnbsWLvxxhtt06ZN9uCDD1qqVKls0KBBYfkZAAAAACA+Fj2/z5/3jeG+hzqeG1edzyM9e9p7H3/s/n3JJZfYFXny2F116tgLjz9uGdKnj/LczxfOs3dGvmVrf/rRzp45Y8WvKWFtHmhn995z3znv++lnH9mY8e/aup/X2pkzZ6xQwcLWsN6d9lDrdnZpjpzn3aZHH33URo8ebdOmTbN77rknymOKB//66y+bPXt2lPsXLVpkNWvWtEOHDlmOHDncfadOnXIx5+TJk23z5s2WKVMml9B9+OGH7f7777e0adNaist0K1Bu166dtWnTxkqVKuWCb/3gCqpjsnTpUqtWrZq1bNnSZcfr1KljLVq0uGB2HAAAAAAQN7dVq2bbvvrKfv7sM3vlmWdszMyZ9tKwYVGeM3r8SGvdroVVrlTVPpu90L6a9601btjUnu35pL3Yv2eU5w54ra890rGNVSh/rU0d/74tnr/M+vR8yX7asM5mfjj9vNty/PhxF2w/++yzscaJcaGAWwnel19+2R555BEXWyqOfPzxx+3tt9+2n376yVJcpls/9KpVq6x79+7B+1KnTm21a9e2ZcuWxfgaZbffe+8998upUqWKbdu2zebOnWsPPPBAIm45AAAAAKRc6dOls7y5crl/X5k3r0399FNbuHy5vfT/H//t919dYP3IQx2s57O9g69r366TpU2bznq++Kw1rN/EKl5byVavWWVvDnvD+vV62R55qH3wuQULFLIaN91qhw//dd5tmTlzpkvQquI5f/78tnv3bjfNOL6U4V6yZIl9//33du211wbvv+qqq1z2XPGpX8KW6T548KArK8iTJ0+U+/X93r17Y3yNMtx9+/a16tWru9R/0aJF7ZZbbrEePXrE+jknT560I0eORLkBAAAAAC7sp82bbfmaNZYupPT6k7kf2enTp63DI53OeX6rlm0sc+YsNuvj9933H8ye4b5v88DDMb5/9uz/V/odmzFjxrjS7+zZs1u9evVs/PjxF/VzqKRcCd7QgNuj2DJz5syWYhupxYfq8gcMGGDvvPOOrV692j788EObM2eO9evXL9bXDBw40P2BvNvFjIoAAAAAQKT4bMkSu7xKFbu0YkWrfNddduDPP+3JBx8MPr5t+xbLljW75cmd95zXpkuXzgoVKOyeI9t3bHXfX8x8ac27Xr58uTVv3tx9r+B73LhxFggELuq9SpQoYeEQtqA7V65cliZNGtu3L2oTAX2fN++5fzx54YUXXCm5JrqXLVvWmjRp4oJwBdZnz56N8TUqXz98+HDwpnIEAAAAAEDMalSubMvff98WT5li9zdqZA80bmyNb7vtot4rcBEBskdzuDUPW7Gj1K9f38V0X375ZaJuR7INujUCUrFiRVu4cGHwPgXO+v6GG26IdRK95n2HUuB+vl9i+vTpLVu2bFFuAAAAAICYZcqY0YoWLGjlihe3Ef362fdr19r4Dz8MPn5VkWJ25Ohh27tvzzmv1dzoHbu2u+d4z925e4crR48PTUWeMGGCq2xWF3Xd1HT7zz//jNJQTfGdAvHo1NFcsaJXNn7NNdfYL7/8YuEQ1vJyLRc2atQo98vcsGGDtW/f3o4dO+a6mUurVq2iNFpr2LChDR8+3HWv2759uy1YsMBlv3W/F3wDAAAAABKGkp7PtGtnfd5+2/45ccLdd0e9Rq5cfPiooec8f8LksXb8+DFr0uhu9/1dd95jx479beMmjY7x/WNrpKaG2UePHrUffvjB1qxZE7xNnTrVTTNWUC1a8kudx9XLK5SmIxcpUiRY1q7+YF988YV7v+g0IKA4NEUG3arNf/31161Xr15WoUIF90ucN29esLnarl27bM+e/42ePP/88/bUU0+5r+pg17ZtW1duMHLkyDD+FAAAAACQcmmd7jSpU9vIadPc91deUcBe6NbX3h37jlsObPOWTbZj5zYbMXqo9RvYy9q36+g6l0vFaytZx0e7uG7nfQe+YCtXrbDdv+6yJd8usoc7tLLpH0yNtYFagwYNrHz58lamTJngrVmzZm7dbTVGk/vuu89SpUrlErZaHWvLli0uE65u5YodPU888YRbfrpWrVo2bNgw+/HHH91qWDNmzLDrr7/ezflOcUuGeTp27OhusTVOC6WSgt69e7sbAAAAAMB/isMea9HCBo8bZ+2aNTPLavZo2w5WqGBhGz7qbRs1boSdPXPGil9Twl55aZC1aHZ/lNe/0L2vlStbwcZNHG0TJo9z04oLFyxiDevfac2btjjn8/Yf2O/KyqdMmRJj5l29vRSUa41tBeBff/21W1KsUaNGrtS8WLFiNmjQIJekDZ12rErpwYMHu6Tt008/7crVS5YsaZ07d3YBfYoNugEAAAAgktzyUtRlkxPKVXv/e7Owd/v3j/H+px9+2N3k6P+/7/bb6rtbXNx5x13uFhe5L8993jngWs0qlOZrq+T8QhR4KzjXLTElqyXDAAAAAABITgi6AQAAAADwCUE3AAAAAAA+IegGAAAAAMAnBN0AAAAAkNACgdAvSKYCCfAHJOgGAAAAgASW9uSfZmdO2fHYm3AjGTh+/Lj7mjZt2ot+D5YMAwAAAIAElubf45Zj52e2P93dZpbDztpZs1T+fubJs4mTVj/97ynfP+PEiRMW7gy3Au79+/e7tcDTpElz0e9F0A0AAAAAPsi7eYr7ur9QPduXPqOl8jnqDhyxRHHiyBnfP+PIqQyWFCjgzps37396D4JuAAAAAPBBKgtYvs2TLfe2D61TsTKW2ufZvYPf/dcSw7oqvXz/jPv6lLRwU0n5f8lwewi6AQAAAMBHac78Y/tO7fP9c1LvSZyg+8Ths75/RoYMSSPTnRBopAYAAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAACfEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAACfEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAACfEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAACfEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAACfEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAACfEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAACfEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAACk1KB72LBhVrhwYcuQIYNVrVrVVqxYcd7n//XXX/b4449bvnz5LH369HbNNdfY3LlzE217AQAAAACIq0ssjKZPn25du3a1ESNGuIB7yJAhVrduXdu4caPlzp37nOefOnXKbrvtNvfY+++/b1dccYXt3LnTcuTIEZbtBwAAAAAgyQbdgwYNsnbt2lmbNm3c9wq+58yZY2PHjrVu3bqd83zd/+eff9rSpUstbdq07j5lyQEAAAAASIrCVl6urPWqVausdu3a/9uY1Knd98uWLYvxNR9//LHdcMMNrrw8T548VqZMGRswYICdOXMmEbccAAAAAIAknuk+ePCgC5YVPIfS97/88kuMr9m2bZt9+eWXdt9997l53Fu2bLEOHTrY6dOnrXfv3jG+5uTJk+7mOXLkSAL/JAAAAAAAJNFGavFx9uxZN5/73XfftYoVK1rz5s2tZ8+eriw9NgMHDrTs2bMHbwUKFEjUbQYAAAAARK6wBd25cuWyNGnS2L59+6Lcr+/z5s0b42vUsVzdyvU6T8mSJW3v3r2uXD0m3bt3t8OHDwdvu3fvTuCfBAAAAACAJBZ0p0uXzmWrFy5cGCWTre81bzsm1apVcyXlep5n06ZNLhjX+8VEy4ply5Ytyg0AAAAAgBRfXq7lwkaNGmUTJkywDRs2WPv27e3YsWPBbuatWrVymWqPHlf38i5durhgW53O1UhNjdUAAAAAAEhqwrpkmOZkHzhwwHr16uVKxCtUqGDz5s0LNlfbtWuX62ju0Xzs+fPn25NPPmnlypVz63QrAH/uuefC+FMAAAAAAJAEg27p2LGju8Vk0aJF59yn0vPly5cnwpYBAAAAABBB3csBAAAAAEhOCLoBAAAAAPAJQTcAAAAAAD4h6AYAAAAAwCcE3QAAAAAA+ISgGwAAAAAAnxB0AwAAAADgE4JuAAAAAAB8QtANAAAAAEBSDLpPnTplGzdutH///TfhtggAAAAAgEgOuo8fP25t27a1TJkyWenSpW3Xrl3u/k6dOtnLL7+c0NsIAAAAAEDkBN3du3e3H3/80RYtWmQZMmQI3l+7dm2bPn16Qm4fAAAAAADJ1iUX86LZs2e74Pr666+3VKlSBe9X1nvr1q0JuX0AAAAAAERWpvvAgQOWO3fuc+4/duxYlCAcAAAAAIBIdlFBd6VKlWzOnDnB771Ae/To0XbDDTck3NYBAAAAABBp5eUDBgywevXq2c8//+w6l7/55pvu30uXLrXFixcn/FYCAAAAABApme7q1au7RmoKuMuWLWuff/65KzdftmyZVaxYMeG3EgAAAACASMh0nz592h599FF74YUXbNSoUf5sFQAAAAAAkZjpTps2rX3wwQf+bA0AAAAAAJFeXt64cWO3bBgAAAAAAEjgRmpXX3219e3b17799ls3hztz5sxRHu/cufPFvC0AAAAAACnKRQXdY8aMsRw5ctiqVavcLZSWDyPoBgAAAADgIoPu7du3J/yWAAAAAACQwlzUnO5QgUDA3QAAAAAAQAIF3RMnTnRrdGfMmNHdypUrZ5MmTbrYtwMAAAAAIMW5qPLyQYMGuXW6O3bsaNWqVXP3ffPNN/bYY4/ZwYMH7cknn0zo7QQAAAAAIDKC7rffftuGDx9urVq1Ct7XqFEjK126tL344osE3QAAAAAAXGx5+Z49e+zGG288537dp8cAAAAAAMBFBt3FihWzGTNmnHP/9OnT3RreAAAAAADgIsvL+/TpY82bN7clS5YE53R/++23tnDhwhiDcQAAAAAAItFFZbqbNm1q3333neXKlctmz57tbvr3ihUrrEmTJgm/lQAAAAAAREqmWypWrGjvvfdewm4NAAAAAACRnumeO3euzZ8//5z7dd9nn32WENsFAAAAAEBkBt3dunWzM2fOnHN/IBBwjwEAAAAAgIsMujdv3mylSpU65/4SJUrYli1bEmK7AAAAAACIzKA7e/bstm3btnPuV8CdOXPmhNguAAAAAAAiM+i+88477YknnrCtW7dGCbifeuopa9SoUUJuHwAAAAAAkRV0v/rqqy6jrXLyIkWKuJv+fdlll9nrr7+e8FsJAAAAAECkLBmm8vKlS5faggUL7Mcff7SMGTNa+fLl7aabbkr4LQQAAAAAIBIy3cuWLbNPP/3U/TtVqlRWp04dy507t8tuN23a1B555BE7efKkX9sKAAAAAEDKDbr79u1rP/30U/D7devWWbt27ey2225zS4V98sknNnDgQD+2EwAAAACAlB10r1mzxmrVqhX8ftq0aValShUbNWqUde3a1d566y2bMWOGH9sJAAAAAEDKDroPHTpkefLkCX6/ePFiq1evXvD7ypUr2+7duxN2CwEAAAAAiISgWwH39u3b3b9PnTplq1evtuuvvz74+NGjRy1t2rQJv5UAAAAAAKT0oLt+/fpu7vbXX39t3bt3t0yZMkXpWL527VorWrSoH9sJAAAAAEDKXjKsX79+dtddd1mNGjUsS5YsNmHCBEuXLl3w8bFjx7qO5gAAAAAAIJ5Bd65cuWzJkiV2+PBhF3SnSZMmyuMzZ8509wMAAAAAgHgG3Z7s2bPHeH/OnDn/6/YAAAAAABCZc7oBAAAAAEDcEXQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAACfEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAACfEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAICUHHQPGzbMChcubBkyZLCqVavaihUr4vS6adOmWapUqaxx48a+byMAAAAAAMku6J4+fbp17drVevfubatXr7by5ctb3bp1bf/+/ed93Y4dO+zpp5+2m266KdG2FQAAAACAZBV0Dxo0yNq1a2dt2rSxUqVK2YgRIyxTpkw2duzYWF9z5swZu++++6xPnz521VVXJer2AgAAAACQLILuU6dO2apVq6x27dr/26DUqd33y5Yti/V1ffv2tdy5c1vbtm0v+BknT560I0eORLkBAAAAAJDig+6DBw+6rHWePHmi3K/v9+7dG+NrvvnmGxszZoyNGjUqTp8xcOBAy549e/BWoECBBNl2AAAAAACSfHl5fBw9etQeeOABF3DnypUrTq/p3r27HT58OHjbvXu379sJAAAAAIBcEs5fgwLnNGnS2L59+6Lcr+/z5s17zvO3bt3qGqg1bNgweN/Zs2fd10suucQ2btxoRYsWjfKa9OnTuxsAAAAAABGV6U6XLp1VrFjRFi5cGCWI1vc33HDDOc8vUaKErVu3ztasWRO8NWrUyGrWrOn+Tek4AAAAACApCWumW7RcWOvWra1SpUpWpUoVGzJkiB07dsx1M5dWrVrZFVdc4eZmax3vMmXKRHl9jhw53Nfo9wMAAAAAYJEedDdv3twOHDhgvXr1cs3TKlSoYPPmzQs2V9u1a5fraA4AAAAAQHIT9qBbOnbs6G4xWbRo0XlfO378eJ+2CgAAAACA/4YUMgAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAACfEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAACfEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAACfEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAACfEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAACfEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAACfEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAACfEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAACfEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAA8AlBNwAAAAAAPiHoBgAAAADAJwTdAAAAAAD4hKAbAAAAAACfEHQDAAAAAOATgm4AAAAAAHxC0A0AAAAAgE8IugEAAAAASMlB97Bhw6xw4cKWIUMGq1q1qq1YsSLW544aNcpuuukmu/TSS92tdu3a530+AAAAAAARG3RPnz7dunbtar1797bVq1db+fLlrW7durZ///4Yn79o0SJr0aKFffXVV7Zs2TIrUKCA1alTx3777bdE33YAAAAAAJJ00D1o0CBr166dtWnTxkqVKmUjRoywTJky2dixY2N8/uTJk61Dhw5WoUIFK1GihI0ePdrOnj1rCxcuTPRtBwAAAAAgyQbdp06dslWrVrkS8eAGpU7tvlcWOy6OHz9up0+ftpw5c8b4+MmTJ+3IkSNRbgAAAAAApPig++DBg3bmzBnLkydPlPv1/d69e+P0Hs8995zlz58/SuAeauDAgZY9e/bgTeXoAAAAAABERHn5f/Hyyy/btGnTbNasWa4JW0y6d+9uhw8fDt52796d6NsJAAAAAIhMl4Tzw3PlymVp0qSxffv2Rblf3+fNm/e8r3399ddd0P3FF19YuXLlYn1e+vTp3Q0AAAAAgIjKdKdLl84qVqwYpQma1xTthhtuiPV1r776qvXr18/mzZtnlSpVSqStBQAAAAAgGWW6RcuFtW7d2gXPVapUsSFDhtixY8dcN3Np1aqVXXHFFW5utrzyyivWq1cvmzJlilvb25v7nSVLFncDAAAAACCpCHvQ3bx5cztw4IALpBVAaykwZbC95mq7du1yHc09w4cPd13P77777ijvo3W+X3zxxUTffgAAAAAAkmzQLR07dnS3mCxatCjK9zt27EikrQIAAAAAIIK7lwMAAAAAkJQRdAMAAAAA4BOCbgAAAAAAfELQDQAAAACATwi6AQAAAADwCUE3AAAAAAA+IegGAAAAAMAnBN0AAAAAAPiEoBsAAAAAAJ8QdAMAAAAA4BOCbgAAAAAAfELQDQAAAACATwi6AQAAAADwCUE3AAAAAAA+IegGAAAAAMAnBN0AAAAAAPiEoBsAAAAAAJ8QdAMAAAAA4BOCbgAAAAAAfELQDQAAAACATwi6AQAAAADwCUE3AAAAAAA+IegGAAAAAMAnBN0AAAAAAPiEoBsAAAAAAJ8QdAMAAAAA4BOCbgAAAAAAfELQDQAAAACATwi6AQAAAADwCUE3AAAAAAA+IegGAAAAAMAnBN0AAAAAAPiEoBsAAAAAAJ8QdAMAAAAA4BOCbgAAAAAAfELQDQAAAACATwi6AQAAAADwCUE3AAAAAAA+IegGAAAAAMAnBN0AAAAAAPiEoBsAAAAAAJ8QdAMAAAAA4BOCbgAAAAAAfELQDQAAAACATwi6AQAAAADwCUE3AAAAAAA+IegGAAAAAMAnBN0AAAAAAPiEoBsAAAAAAJ8QdAMAAAAA4BOCbgAAAAAAfELQDQAAAACATwi6AQAAAADwCUE3AAAAAAA+IegGAAAAAMAnBN0AAAAAAPiEoBsAAAAAAJ8QdAMAAAAA4BOCbgAAAAAAfELQDQAAAACATwi6AQAAAADwCUE3AAAAAAA+IegGAAAAAMAnBN0AAAAAAPiEoBsAAAAAAJ8QdAMAAAAA4BOCbgAAAAAAfELQDQAAAACATwi6AQAAAADwCUE3AAAAAAA+IegGAAAAAMAnBN0AAAAAAKTkoHvYsGFWuHBhy5Ahg1WtWtVWrFhx3ufPnDnTSpQo4Z5ftmxZmzt3bqJtKwAAAAAAySbonj59unXt2tV69+5tq1evtvLly1vdunVt//79MT5/6dKl1qJFC2vbtq398MMP1rhxY3dbv359om87AAAAAABJOugeNGiQtWvXztq0aWOlSpWyESNGWKZMmWzs2LExPv/NN9+022+/3Z555hkrWbKk9evXz6677jobOnRoom87AAAAAADnc4mF0alTp2zVqlXWvXv34H2pU6e22rVr27Jly2J8je5XZjyUMuOzZ8+O8fknT550N8/hw4fd1yNHjlhSd/bk8UT5nCOpAr5/xpl/zvj+GX+f8f8z5J9Tx3z/jP+yfybGfpMY+4yw3yTOfsOxJmnuN4mxzyT1/YZjTfxxrOFYE18cazjWpLRjTWLytjEQuMA+FAij3377TVsXWLp0aZT7n3nmmUCVKlVifE3atGkDU6ZMiXLfsGHDArlz547x+b1793afwY0bN27cuHHjxo0bN27cuFkC33bv3n3euDesme7EoCx6aGb87Nmz9ueff9pll11mqVKlCuu24fyjRgUKFLDdu3dbtmzZwr05SAbYZ3Ax2G8QX+wzuBjsN7gY7DdJnzLcR48etfz585/3eWENunPlymVp0qSxffv2Rblf3+fNmzfG1+j++Dw/ffr07hYqR44c/3nbkTh0gOEgg/hgn8HFYL9BfLHP4GKw3+BisN8kbdmzZ0/ajdTSpUtnFStWtIULF0bJROv7G264IcbX6P7Q58uCBQtifT4AAAAAAOES9vJylX63bt3aKlWqZFWqVLEhQ4bYsWPHXDdzadWqlV1xxRU2cOBA932XLl2sRo0a9sYbb1iDBg1s2rRp9v3339u7774b5p8EAAAAAIAkFnQ3b97cDhw4YL169bK9e/dahQoVbN68eZYnTx73+K5du1xHc8+NN95oU6ZMseeff9569OhhV199tetcXqZMmTD+FEhomhKgtdujTw0AYsM+g4vBfoP4Yp/BxWC/wcVgv0k5UqmbWrg3AgAAAACAlCisc7oBAAAAAEjJCLoBAAAAAPAJQTcAAAAAAD4h6AYAAAAAwCcE3QCSBXo+AgCSmrNnz4Z7EwAkAwTdCMsJ6ejRo2HZFiQf//zzj02fPt1OnDjh9qFUqVIReCNOuAhGfHFsQXydPn3azpw5E2VZWyA+OO5EFo4U8J1OSDt37rQhQ4a472fOnGmtWrWyw4cPh3vTkIR98cUX1rNnT7v77rvtwQcfdPuQAm/gQryL4OXLl9vvv/8e7s1BMrjw1bHlr7/+cjfgQv7991/r0aOHXX/99TZp0iRbuXJluDcJyXBAmGuayELQjUQ5OQ0fPtzGjRtnrVu3tubNm9udd95p2bNnD/emIQmP+jZs2NB+/vnn4ABNhQoV7K233rJdu3aFdRuRPC5ovvzyS6tfv75NnDjRDhw4ENbtQtL3xx9/WMWKFe2dd96xQ4cOhXtzkMQpWHr44YfdoPAnn3xijRs3tgEDBrj9CIjt+sYbEB41apQ98cQT9vrrr9svv/wS7k1DIkkVoLYBiVQqrGD7008/tWbNmtm0adPc/SrNSpMmTbg3D0ko2yTaT8qUKWOFCxcOPv7iiy/alClTrF69evb444/bNddcE8atRVLef95++213zOnTp4+lT5/enn32WWvbtq1dfvnl4d5MJGFPPfWUy1p2797dDRDnzJkz3JuEZEDVNAsXLrTHHnvMGjRoYM8995wbwAFCB4S9gFvHl9GjR1u5cuXcII3OW0pMqWoCKRuZbvjKG9NJly6d5ciRw2677Tb79ddfbeDAge5+BdwKvBHZQgMmBdRPPvmkZciQIfiYF3R37drVFi1aZBMmTKAMFFF4+0/fvn3thRdecIMyGtzTIN+rr75qY8eOtYMHD4Z7M5FEhOYbTp065b6+8cYbLnDSPqRjzJ9//hnGLURSNWzYMDcw7MmXL5898MAD9u2339qqVausX79+tnHjxrBuI5IWL+DevHmzHTlyxObPn+8GarQv6Vx1//33u+lQSOGU6Qb8cPbsWff1+++/D+zcudN9f+jQoUDHjh0DVatWDQwYMCDK8w8cOBCmLUVS2E/kiSeeCFx66aWBH374IcpzTp8+Hfz34MGDA7lz5w4sXLjQfX/mzJlE3FokZX/99VfguuuuC7z++utR7n/22WcDmTJlCgwcODCwb9++sG0fkgbvmLF///4Yjx/du3cP5MiRIzBo0KDAH3/8EYYtRFKlfeauu+4K5M+fP/D5558Hz2HeOWrdunWByy+/PNC+ffswbymSmhkzZgQKFSoUqFy5cpTz0MqVKwP33HNPoFixYoFly5aFdRvhLzLd8DVzOWvWLDevUuWeKqNRtlvNsSpXrmwff/yxmwMlvXr1svbt29vJkyfDvekIU4ZSWWyVj2suruZveyVZyjZdcsklwfm6mgelEj7tL8ePH6dzLKLMlwvtJqzO9/LKK69YjRo1bOjQoa58mCqJyKb9Y+vWrVaoUCF3rOnSpYt98MEHrlmj6LykKQm9e/d2PQGokIBHU1R0vXL77be7Od2ff/65O4dpn1L/Gk2LUoXNmDFjbMaMGeHeXCQh2keKFy/u5nCHnoMqVarkjjf6qmrQn376KazbCR/5HNQjgs2dOzeQMWPGwJgxY87JYmuU7+mnnw4ULVo0ULJkyUDOnDkDy5cvD9u2Iry+/fbbQKpUqQKvvPJK8L5///03ULhw4UDnzp2D93lZKVVO1K1bNzB//vywbC/CL7YKh2bNmrljiufUqVPu62OPPRaoUKFCIG/evIGPP/74nCoLRA793WfNmhVIly6dO+5o38iWLVugdOnSgdq1awcmTZrkqrJ69uwZuPLKKwPDhg2jQiIChR4foh8rVq9eHWjdunWgYMGCwfOQjkk6b3nVEi1btgwcO3aMaqwIFNvfXPvKDTfc4LLdGzZsOOc66IUXXgjuQ0h5SBHBF5ojpzWWO3bsaA899JBlzJjRNmzY4LLcaiChbJSyCGoe0aFDB/vuu++satWq4d5shInmNCnTrQyC5jqJmoqUKlXKza/0eBnMK664wu1TqpZAZDel0bFjzZo1tmPHDve9usEq43TTTTe5yhnveaq0UcdYZbzVyEZYriUyO9vr737rrbfae++9Z1myZLFcuXK5zLfm/mfOnNkGDx5sBQsWtH379tlvv/3mGqypaov13yOLd3zQyiuqklm3bp0dO3bM3Xfttde6hmk6zrRr187mzZvnjjXe8UbHGc3vPnr0KNVYEXx+Wrx4sauGmDt3rvu+Tp067prm0ksvddfGoXP/b7zxRvcYvY5SsHBH/UiZlF2qUaOGm6eyd+/eQLt27QK33HJL4JprrgnkyZMn0KVLl3BvIsIktuyiMktdu3YNpEmTJnDFFVcEGjdu7Oboxvb6n3/+OdC/f/9gJhORt/+oWqZAgQKBrFmzBm6//fbAxIkTgxkDZbs177JOnTqBsmXLuqoaGTJkSKBixYpknyLQtm3b3P7g+fvvv90+o2OOMpOeI0eOBD766KPAyy+/7HoE6Jy1cePGMG01wumDDz5w1RC6ZciQIVCvXj13PfPNN98Ejh49Gti0aZObv625ugsWLIjy2gceeCAwbdq0sG07wkvnJ52DrrrqKlf1qeo8VUh4GW99X7169cD69evDvalIJCwZhgTvPu2ZM2eO68iorJNG97SeZYsWLezll1+22bNnuy7UXodqRN5+8s033wS7BivrJJrn9Oabb7qlntQ9WB1hY9q3RPO5T58+zXrvEZQ90H4Quv8ow6S5k5pz++GHH9ratWtd93stD6Ylw5S5/Pvvv91xRlUUadOmddkFPX/mzJluVQWy3ZFjxYoVbsnBEiVKuE7Tov1E87m1z6hPxJAhQ6K8RscYdRu+7LLLwrTVSEzRzze7du2yTp062eHDh11FVunSpV0Vn44hymK3bNnS9Y7Q83788Ud33qpZs6Z77cqVK91rOEdFHlVVqbLzs88+s9y5c7sKiSZNmrieAKqcKFq0qLtG1qosWl5uxIgR4d5kJIJLEuNDEBknKV3EfP3113bgwAGrXbu2a3alhhDbtm2z6tWrB5do2bNnjyvdQ+RezPTo0cMFPQq6VUqlaQgqL1ejPV3g6AT14IMPuhJyDdbEFHhnypQpTD8JwiG0RFNBksr1tByYSvJEDWo0YKMyUAVKWvpJU1g8KhVWg6yPPvrIlixZ4tbvRmRRo6JPPvnErcFdpUoVF4TrGNO0aVN3fFHgraaNmqIgGjDWQA0Bd+TwzjOatlKkSBF3rfLWW2+5wTwtd1q3bl1bunSp+7fOYevXr3f7lPYVDRoroeAF3drfGNRL+TTNrVatWm5qikf7hZIJCqhVKq7rHJWaa5/wliTUNbKOLToWIUIkVkodKb8E67LLLgs0bNgw8NBDD7lSrG7dugVOnDgRfM6PP/7oSviyZ8/u/o3ILAnW1AItC6YSYC0n16lTJ1dmFUplnyo1T5s2bWDmzJlh2GIkFWpWpIZWopLwXbt2uRJh7UMPP/xwlOf+8ssvrtSzUqVKgVdffTV4/6+//hp466233P3Rl6NDyuRNH4g+/URLO3399dduuoGaGXmOHz8eeO+99wJZsmQJPProo4m+vUg6VPqrsmA19vSawG7fvt1d31SrVi0wefLkKPvZwYMHA/PmzQuMGjUqjFuNcNDSt3fccUeUaxztE3fffbebiuD5559/3NepU6e66XM6j4ViulNkIOjGf6YLXc1nGjlyZDBgUrCkoNuzZs2aQKtWrQJlypRx/0Zk6tGjh7uo1T7jUadgzf9/55133EXO7t27g/uR5kRpAGfFihVh3GqEiy5Upk+ffk7gtHTp0kCTJk1c5+DogzKae9uiRYvAgw8+GOVC6Pfff2fN5Qij44zm1fbp0yewZcuWwJ49e4KPabUMzbXU/P7QwHv06NFuDjfdyiNbhw4dAuXLlw+89tprwcB7x44dLvDW+Wr8+PGxvpYAKrJ4a7RrvrZ604h6Qmge94QJE6I8d8qUKW4VjZj61SDlI+jGf/bdd98Fbr75ZvdvXdhoFO+RRx4JPu6N6K1cudJlnBC5F8AKoBUQefuBLk5KlSoVKF68uNuHcuTI4bKRangkOoFpZBiRJ3rDvREjRgQaNWoUvH/ZsmWBpk2bugaNH374YZTn6pjjXfhyARyZtFSTmut5TbCU1dYShL169QrMmDHDDeQsWbLEHW+qVq0aZaDn8OHDYd12JB7veOJ9DR3g69ixo0sUxBR416xZ0w0YI3KFLu2lZSi19O3w4cNdI0Ydf5544olAkSJFAu+++677XgO/DRo0OCczjshBIzXEmze/VssgaA6u5qvcd999NnXqVNco7bbbbrN33nknOIfltddes5EjR7plnhDZxo8f7xqHaD/RnG01StPSGZMnT3bzoXbu3OnmQGmO9yuvvBLrMhxI+aL/vTVXW8cR7R9qRKNjkJqpqfGVmho98cQT1rhx4/O+ByJvrqUaGulcVK1aNTd/UscaLeWkBlc69lSoUMGdo2644YZgczVEHv3ttQxY9H4hmsutpq9t2rRxvQDUCEvnqc6dO7tl5nRM0r6FyBLTuaVVq1aueZ760+j6Rn1EdC08aNAgt9+of0TWrFlt+fLlrlcE56cIFO6oH8mT5sRlzpzZLbeiEWCN3GXKlMllMcUbxVOJuUaEvVFiRIbzjeKOGzfOzZfLlSuXy26fPHky+JhGiG+44YYoUxMQeTTf36uQefLJJ12WWxlIZRGuvfbawP333x/cx7R0j5YmVMXE4sWLw7zlSGrHH/Ub0flJWe+tW7e6+3777bfAsGHD3JSnq6++OpgNjz7PEpEzL9erhlDpuEqAv/rqqyjTokqXLu36RHjTDlTVN2jQoDBuNcIltHpKVTOaz+9p27at6xeh+f1eT6MNGza4ir05c+YEs+NeSToiC93LEW8a5VXnYHWgVqZSGjZs6LqUawkedSzXMiwzZsxwWQZ1Cs6VK1e4NxuJJLTT+OrVq10lhEZzlaEUryu5MghlypSx33//3QoXLuwe0xIs6gDrfY/IopH/Q4cOudUO7rnnHpcVeP/9990xRMt+KZOg/UvHFf174sSJLst08uRJ+/LLL8k4wdHxxzsO3XXXXS7TrUoJHXO0jI/2rw4dOribzmf79+93We8CBQqEe9MRBnny5HFftZpG+fLlrV+/fi5LqX9rdQRluTdt2uSWf9I+pWOPlnx68skn3etiW9YSKY/+1l52+rnnnnMraWilDFXMaD8aPXq021+0NK6eq9VXtEShbh5dE2mVBESgcEf9SF40YqdMpBqnqfFVqNdff93Nr0ydOrVrQHLdddfRKTiCM0wvvPBCoGTJkoHcuXO7m7KVodSIRvP/1c1cc7zVxEjPb9y4cRi2HEnJzp07XROaDBkyBD777LMo+5bmxunYo+OLOptHr6oInWeHyBY6P1f70W233eY6CqsRn4c5//DOR7p2GTt2rKuEWLVqlVtBQ/P9r7nmGpfpVjY8ffr0gblz54Z7cxFmAwcOdNV6asgYE62soV41gwcPducsQAi6EW/ekk933nnnOR0YVR6sg5AumrWMBiJ7H1m4cKErqVLzEAVRupAJDZJ0oaMO1FrmSZ2EdVHs4WI4snh/b5XdrVu3zjXVU9DdsmXLYGM9jzrbq9Q8b968gb59+7r7aEwT2by/vzddxRt80YCejkNeR2EtN6dGWJoihchyoQG6oUOHusC7f//+Ue5Xwz2tonDrrbe6cxUiex/SKhi6VtFUOW85OV3naJqTBmq80nElEJo3b865CUEE3Tiv2A4Wzz77rJtDqaVYvCUSANEFizICyhZ4NPdNy8gtWrTonOePGTPGPV9LQHkIuCNL6N9bAzOhHe/VK0IXM7qwie6TTz4hs40oHYRVceVluNVpWoN/oUGU9hllL7VPeWvnIrKsXbs21uOPqmgUeGued/R5txrsi+k1SNli+lurV5FW01CwrZ4RWr9dx5SsWbO6LHf01xJ4Q5hUgFh585S+++4719lT87WLFCliDRo0cJ2l//33X/voo4/cczp16uQ6mTO3KbL99ttvtmLFCitUqJD98ssvlj9/fjd/6b333nNfx44da0uXLnVzJ5s0aeI6lj/00ENubneVKlXce9DRM7KE/r2ff/55Nzdbc2/Vibx48eKuQ7nm4GperuZaFitWzO688043V1fdhEX7lh5H5FBfkSuvvNLKlSsXPO9MmjTJrr/+etcZWHNyy5Yt61bW6NatW/A5d9xxh9tXSpcu7foEILLoGNK7d2+7/fbb3ZxszbUNncvfvn1791UraGg/efrpp4PHJ52vos/rReScnz755BPLli2b1ahRw83bfvfdd13vEa2cof3ppptusr59+7q+Ruozkj59evdarmngYckwxMi7QFGTCB1c1CRCDa42bNjgAmwtgSA6aS1btsxuueUW6969u2XPnj3cm45EduLECRszZowLlERBt5bJUBO1/v3728CBA92Ajfabw4cP2w8//OAaYOmiV6/V0nNeoz1OTpFLDa50EaOlBytVqhRlEO/777+32rVruwtkNWlUw6O1a9e64AqRRwG1lvjSeUdBUalSpdz9N998sxus0ZI9GzdutC+++MI1S/MGgjm+QNcuCoq0H+iaZt26da4hlgZ9dU7y6Bymc5aua1566aWwbjPCIzSJpH1k1qxZLsBWM1gNyCjxpGPRVVddFXxNzZo13UDgm2++GcYtR5JFwh+xlc9s3rw5kC9fvmDDtD///DMwbdo0V+751FNPBZ/3yCOPuAZqLAsWmb788stA/fr1o9y3YsUKt6yTSq3Kli17zmt2794dGDlyJEuDwVHDxRIlSrjlv0RTVn7++efAm2++GVi5cqW7T/O8e/Xq5eZwe2WfLLsSuTQNQUs8qZRT+4ZovvakSZOizO1WWSdTECJbaGmvyoG132iuvxpcDRkyJFCrVi1XHtyzZ0/XP8Lbd1577TW3rBwim6YaXH755e78FNO1snoZabnKunXrBsqVK8d5CbEi043g6L9GfLV8U926dd39KivX0hgLFy50ZXyeKVOm2MMPP2yffvqp3Xrrre4+LbmSO3fusP0MCJ/Nmzdb1apVXYbS23dE2cm33nrLfR08eLB7TIcbjQ4rQxk6ikwGKrJt2bLF6tSp47IDmpowYsQId9zRPqLHli9f7jJRofuJ9iOWXYlsqprRuUiVWKq6UkayXbt2VqtWrSjPO3bsWLA0GJFDFTGqsoru3nvvdeceVWhlypTJVUTo/KTSYVXTaFqUstyqpPAwdS4yHTx40E1l0nFG18O7du1yVTS63tF+omOOzlUTJkxwy11++OGH7vqG8xNiwlVuhPMuYlWqqTUpVRrs0clo69atbn1K8cZndCLKly+f7dmzJ/hcAu7IpH3i6quvtkcffdRdwITuEyoR1oWL1ud+6qmngmucRg+4hYA7so450enC+Nprr3UlfBrA0b4xYMAAN2Cj+xcvXnzOfsIFDbRvaF1cnb9ef/11mzNnjj3yyCN22223uUEcBd86X2l+rqYlILKOMz169LB58+a573XO8Y49mjKnYErHEwVRmvevudzbtm2ztm3bupJhTYHyEHBHLk2Z1DWLeo1oTneXLl3sxRdfdIkmDRLr2kbHmWeeecb1OCLgxvmwV0QwL+Bes2aN3Xjjje4E9cILLwQf14hvvXr1bNiwYW5+5XXXXefu1/zbnDlz2unTp8O49UgKvAsRza9UUL1o0SJr3rx5MDiqXLmyde7c2YYOHWrPPvusm8OtBmpcwESm0Ey1jjt//vmnlSxZ0goWLOiy2+oDoMG+atWqueepGY3mzl1++eXh3nQk4cB75MiRbp5l0aJF3bxcZS01X1f7l5qlNWzY0DJmzBjuTUUiZ7k1dztLliyuyZXOOd55Rw2vdL2jfWbJkiXuOufll192z1VGUw0aQ/tFcL6KDDFV3Gk/aNSokc2cOdM1TVNFjW7qH6F+EgcOHHDPU9NG7z0IuBEbyssjnMpklOHu1auXOwl5VDquDIHKZtR4RKN9yiCoe7lGgMeNG+ey4oULFw7r9iPpeOyxx1zjPY32ahAnlDKWGh3WiUmN1RB5QrNF6iat8rzjx4+7ixp1g1XDIjWgEWUlNdVFAzbKOqm8nAsZnI8GcXSO0vlMTfk4N2H+/PnuvKMBPe0XoSsd6DE13dMgsK5pYjq+kOGOzIB7/Pjx7niifUUDNM2aNbO///7b9u7d61bP8OgaWZV8b7zxRhi3HMkJNZ0RTFlHnZA0uqtMpUcdp1Vq9euvv7qledStUdkCnaA00qcOjjphcVED8Ur2dGGjYLtp06ZujpxOUqGl5prfTcAdubyLV3UF1lQELR+nPhJawufIkSPuOLN+/Xr3HM2PU8Ct+7U6gi6IdQEExEbzupXx/vHHH92gjlbaQGRTV2kt26TkgcdbWlDLEarrvfYbHV9imvZCwB05vIBbFXkaANbfXmXimjqnknJdJyvgVn8IJZxUTaM53Fo+F4grgu4IpkBamQFlmXThu3LlSnv77bddZnvUqFGuvFw0Ejx58mR3EaMmEVqzWyV9iAwxFcOEXqB461DK7Nmz3ciwyva0H2mupcdbVoPimsikv7sCZx0/NIdS8+Dy5s3r5lBqzq2mq0yfPt09V/O6VeKp6QreHDnW4caF6Lyk6VDKSGlKFCKb+o0ocaDKh6+++srd552rlDTQcUgDwdu3b6evCFyy4P3333eJJTV/1fJfmqbgVWCJBnDUP0IDNargY0AY8UF5OdycJgVICqp37tzpLnSvv/76YHCkET/KrKDGVhqo0Rq4Mc1/Ct1HXnvtNVcSrABLAZXmdod2Nkfkuv/++92FjALs0GOK5snNnTvXzcMMLfX0ykGB+FRx6ViFyBV6PtLAnhIGCxYscFVXnh07dri5uX379nUDxYgs0a9h3nvvPRs+fLi7btH+on1C1zLKdqtyT5VZujZW7xEN8Om1NE1DfLCnRDDvpKSTjg4eaiSiZVVUPhMabHv/RuRS587nn38+OKqrTp1ehts7aWkf8QIkPa4mRhrQUUdhvU6BtxrwITLEtgycsk/qOK3lnrzmjKK5caq20TxvLd3jIeBGfBFwI/SaRYGTrmuUuXz33XddlY1WXFG2W4+p+SciT+gcbp2LdN7RPqEBYTXUU0ZbAbd88803rteRSsx1rhKapiG+yHRHuNDR4K+//to1hNA8SgVN6ugZ/TmITAqGlC1QIzSVVD3wwAMuCL9QxltUNqwTE/tQ5AjdJ7TveKXlXu8ITWlRozQF39dcc40b7NM0FjVsVIYBAM4n9Dyjc0xot/GYrllUXdO7d2/XL+KOO+5wTbBatmx5wUFCpDyhf2sNumitbZ2nlM2+9dZb3TWwplo+/vjjwcaeWqtbS+WqHwnXMrhYBN2IcoLySs2VbdIBR/OhELlC9w0FRQqU7r77bjdvUiPAajgiXLAgJlp3e8aMGe6CVyW/WjtZDfe0LJgG9TZv3uzK8/LkyeOCcg3oxLSOOwB4Qo8Pylyr2aLm8WuOdv369c9bUaVspRphKXiqXbu2C6a4zolMmsqkFVe0zKAawIpWX9G1jtbfVhWozlWqAtW63KtWrXIJBM5PuFgE3REq+kEj9HuV0WgJsaxZs9qUKVNcFgqRIaaTiXef5jNpHfeHHnrILaehTsGdOnVynYKFwBuhlCno06ePffLJJ26NZE03aNGihesWrNUPvAvgP/74w13I3Hvvva6UnDlyAOJyjlJ/Ec3D1TrcylT+9ttvrvu0KvXUtTxU9POTgij1sVGTrEsvvTTRfw6El65zFVRrP1H1g5YF8+i6V/P81Z1cy+RqUFgN1jQgTI8R/BcE3RF0klKHTl346iQTWooV/XmikeMCBQrYlVdeGYYtRrhpGYzDhw+7tbeVNdByGcpyKzBSplvLOSmgUpd7Bd7KaAojwPBocEZZgqFDhwbv27Ztmwu6NV9OFTXRcUEDIC5UZaWmVwq21SdClLVWFlv3qRQ4NgwQR56Y/ubqUK6MthIHup4JvS4+cOCA6wOgoFyrbHhLiDEgjP+CvScC6GCheZIdOnRwBx3Nm9QyGbfddluULHZol/LQdbuR8oUGy5999lmwbFwlexrtVeWDunXqxKS53Srh01ruCpBUaq45UFrfnYAbujDRfrBly5Yog3YnT550y8apWkKNarRfqXFNaJBNwA3gQlQ9o4FhnXMUcHsBla5xtGqGstjnC7oJuCPv+sb7m0+aNMn1ptHgr1bM0LQn9acpVKhQsGmaXH755e7moWkaEgJHngg42ChDqZOTDizz5s2zUqVKuczktGnTXOOIUARNkcn7u2tkV108FVBfdtll7qau42o806ZNG1u6dKndeOONrrxc3V+VzVT22+t4j8ijJQaVcVI5njLVujBR8KzlVhYvXmwff/yxe55X7qnO0npcJecE2QDiq2HDhu6mZILKy9VIzQvGdXxRB2rAC5a96xtlr1u3bm0vvviirV+/3t2nBIOSCephpMq92DBQg4TAsE0Kz1zqq+Yr3XTTTS5oUmZbjSN0Qfzqq6+652q5DJUPI7KpsYzm4ao874knnnAZS9331Vdf2T333OMajKhUeNeuXa6bp8rM8+fPbz169LAcOXK496C8PLKo+7gG89SBXBcxuuhVaaeoWkYdgrXsivYllX4ePHjQVVLoojj6nEsAuBCvxFfnIx1TdB2TK1cud9yZOHGiSyyomo8Scoi3Dyi41nVLyZIl3Tno6NGj7npHSShvJZaOHTu6RJQy4IAfmNOdgml9ZK0/qCBJo7/KOOlk5NGIn7oxKqupIJyGaZFLhwE1RlPJnppcKYhSkxl17VQgpX2nevXqtm/fPtf8qlGjRlFKr7z3IOCOHNpflB2YOXOmW2ZFxxl1A9b+o9I90Vrcb775pnuOyj3TpUvnbrpApks5gIsR2vuhcePGrgeNAio1wFJVFr0hEErnIFVi6ZpYCaa//vrLJQ1KlCjhpsepe7moAlTVfFrFh/MS/EDQnUJpXpOCJJX/KgOlAErznZ5++ukonTo1Uvzrr7/aggULogTkiDxa1knBtgInZTBl06ZNNmDAABd0T5061erWrRvMIJBJiFwq69RFiy5itPSXqPFe1apVrUGDBu54o+oIPUfBtTrff/fdd25KgpZmoUs5gPgKPeeEBtYPPPCA60atrLfKzpVkYEAPHlV5at9Rl3KPGgvrfFWxYkW332ied+g+xv4DP3DFnAJt3LjRlQTrQKI1LDVyp4OOAmuN6uniOPTiefbs2QTcEU4XMMpAqlRcWQOvRFhBeM+ePd06pvfff78tXLgweEIi4I5MyihpEEZN0dQvwqNjjI4t2pdUoqfO98owaD9RXwCV7mlZFl0oe3O/AeB8FCj169fP/dsb7BXvOOI1x1J1jXrXzJo1yx2jCJigfUXXKprWpJV7Qpt6aikwNfVUZZaucXbv3h18nIAbfuGqOYXRkjzqwPjWW29FmTOp5XmU+VaArcBbnYM9mpeLyONdsIRmDK6//np3clJW0qPusDopKYOgjvc///wzJ6QIpgZo6mavigjN+VdVhPpC6Njz7bff2pAhQ1x5njLgY8eOdRfA0VH6CeB8FPhoVYwvvvjCzd/W8k4xBd6qmBEF20WLFnXLWarRJyKPt194tK/oWkUrrnz55Zc2btw4d793baxeNHpMlaE6p4W+BvAD5eUpjE5AKgfWwUXBkk5EoXO1n3nmGZfd1jxurU/IwSUyqSGaLmhatWp1zlrsGv1VLwAFUAULFgzer5JhlfC1a9cuDFuMpMLLAqg8T8eazz//3I4fP+6mseTJk8f9W+tzK8utFRLUA0Bd8AHgfEIzjBqs0wDfzp077bXXXrPvv//eTVnRdYvENr1JKyno+gaRJXR/UB8anZ80tUnNPNU8Tc3RNHijNbk17VLJBX1VTwD1p7nvvvtcVWi5cuXC/aMgBSPTncxFHzNRyaZKhDV/WyU1agyh4Mqjk5eWeNK8SgLuyNxP9O+5c+e6pVbKlCnjGoxoFNjTpEkT12xEJyDxlmPRicsLuKOPKCNyeKsiqDxPXV81z19rnGowTxRwa/BPFz7FihWznDlzhnuTASQD3jWJrlMUJKlxp44tzz77rFvKUg0Z33jjDfccb5qTNyBcq1Ytt0ShF3BzjoosXsCtfaVLly4u4aQBYV3jaEUW7U8KrFUJofOSpjyp+aemRWXNmtXy5s3ruuADfiLTnQJGhRUcaa1cXeiqGYSCJpUMa6keHXjUKGLgwIGWLVu2cG8ywpw90FxtZbbVSVpzmLTEyvTp093FjcqFdQLSV2XA1RvAKzNnjlPkii2j5O0TO3bscHMplenWfqOLXk1FULn5jz/+6AYC2X8AxIWqqW6++WbXWfr222931Xk6XylA0moaq1evdg0a1RRWfvrpJ9e8sXjx4m5+LiKXuterEkIZ7SpVqrjrG63MowRDy5Yt3XWxrmtUTq4+RupVo/OT9iVdR6sqi0Fi+ImgO5nz1tzWqJ3KsRQkaU63RoM1b0VLQGlNQjU9UkmxRvQQOUKDHa/JjEr0HnnkkWAX+y1btriASWXlajCiEV8FTWpeozm7ej6gdU2jHz9CS801sKf5/grCNaVF+5TK++hSDiCuNFh34403upUOVE2jm9ZY1tSV0MBbFXtaHky9IxRwK2ASVtWIPN7fvE+fPvbHH3+4nkaqvNKyuOpnpAo9nb+0VFiBAgWCr1MArmlQCtbVh4TScviNI1Mypgvdrl27ulIslQdrDq7KhjW6p9FhNRnRV81p2bNnjx07dizcm4xE5gXcKrnSoIvmMymIVsDtjbep1Mpb61RNsDS/6aWXXnIdqJUBR2TSMUVzsqVTp07uYtdrvhdTqbmmtagpY6lSpQi4AVxU8KQEgaY8KThSTxGdl3Ts0blI3+scVqlSJdexXKtrqHcNAXfk0d/aOx95f3NNhdN9Si4o4Na1sTclTvdpNR/1HPGWSP3hhx9cMP71118TcCNRkOlOJkaNGuXmpqi7tBdI6cJWwZLmTmq+rXfC0dq5jRo1cicijQLrIKQRPpoZRSYFTuo+rjJyXayE8ppexRRwaf12lQsj8qjJjC5W9u7d6wZhtNygSvK8tUyj8zLeujDW83UcIuAGEJ+mad5KGt6Sp8pCqmRcy4bddNNNLuD2Mt7KfmvKnBqnCQF35NA1rzLZWrJSUxA0X1u0n6jiStcuGqjRMpWipSxbtGhh5cuXd497FHgrUA9tNgz4iSNUMjk5qWxGnRZXrVoVzFDqhKVSLG99Qd2vmzLbyjbpMdFJjIA7cqnJjOb1a9BGdGGjdZYffvhhV56nJTO8fcprmnbrrbcGA24a0kQezWvTRYuCb+0rvXv3DgbcMY3TehfPuiD2lvQh4AYQG++YoeoqZSXV7EpBkNSsWdNNj1MjWFXZaA635txqupyX8VblFgF35FG2WvuL9p906dK5OdxaRUN0vxILekxN0TR1TnP+NRVB+4233rt3DtPrCbiRmDhKJZPRYAXQGTJkcI2u1AxLWaTSpUu70TsF5DphKbjWczVirOwlJ6HI5g3CbN26NdjBXnO2H3jgAVd29csvv7iy4IULF7qLGlFJcHTsR5HFuyDR313lm1qbXX0hNO9NdIyJXmYeHfsMgAvRGtyaIvf++++7qj0F2To3ia5rNHCnPhHKcKtZmgJvZbh1PvN6kuh4xfEmMowePdp1H1evmbFjx7rv1dVe/1bWW9Q0TYknTZFT8knJBVVSqFJL+5POXTT1RLhQXp4MKFDSqK/m2FaoUMGN8qpEpmrVqq4MS03T9u/f70qI1XxEnRt1MFIgrvlRiGyaE1etWjU3SKMmV5qKoNFhLRunk5AymjppqWus9h9EptiyRWvXrnWZhN9++80tRaiBPs+hQ4eCF78AEB+avqLso8rFNeBbo0YNN5VOK2zoWkeNYpVoUL8IUVCuhIIqbxBZNPiiaivtD7q+9Wg/URZb87JVqafrG9E+pddoX1LwzZQnJAUE3ckk0z1jxgwXYCs7qbUodaDRaN+1117rvh8/frwb4VNTLB1c9G89BoiWblJGW6VUGvnVPuKN9o4cOdKmTp3qBmu0jAYie36ljiUKsNWpXF3uVWGjATx1gdXFjaa5qFpC63Mro6DMEwBcDDV5VVZS06A03UnzcFU6vmnTJldKXL16dTevWxV8oViGMLLs3LnT7RdKEGiu//333+8SB+o6riXmNGisrvYqL9f0BC19qmXnPExBQFJA0J0MaARPF7hvv/22m5er0TwFTionDw2uVYKuUTwFVszhRlwveFS2V6tWrWBZHyJL6MWIVjsYN26cm3agLLYGYXT8UXZJgfewYcPc4J93Aex1KQeA/3IeUjWNqrK0BKrXAEsNs7R8mK5nQo9TBNyRSSXkWg7snXfecRWfOi9NnjzZdbBX/xEF5qr81Eo+Crg1LQpISgi6kwFlmGbOnOlG9LwLXM1p0trcWbJkcQcgNcqibAZxpUymSs1VLly4cGGX5RYuZiKX1jft0qWLK+FUxYyWU3n88cddh3v9Wxc4WtdUPQK0XOGjjz7qjjmU7AFIiFLz/v3723fffWdNmjSJUkFDlhKhgfeIESPcdbGmVHr7iZJR3jKVOmfp2ph9BkkNe2QS5o2HaLkDLfnlBdxqCqGlMjTip4thlYBq3iUQF+oN8Nhjj7llNrQEnRdw68KGgDsyqYxTDWnUGyJfvnwuk33DDTe4KSwKtvWYjjvFixe3+vXru2Dca0pDwA3gv8qbN68LotSrRhludSr3EDzBkz9/frecpRqqqbeRzlHiBdw6H+n6WPvMhRp+AomNI1kS5gVAzZo1c5lJb31Br7RTyx00bNjQNVnLkSNHWLcVyYdGgLUGqsr5NJ9byCRELv3ttbyKmuitW7fOXbB4xx9NXdFFjfYZXeyoqWMoTXEBgIQMvDUvVwOBc+bMCfcmIQwuVIBboEABNwVBN3W/1zxviT4AzPkJSQ3l5UmIV9q7Zs0at7ag5qSo9FfzmVR2pQOLmhjppKRspYLwY8eO2euvv062CReNkvLIEtMAi4LpRYsWuQyCVjzQv0Op5FMlfeoay4UMAD8pyfDll1+6ho2I3POTqquUZIrtGkWl5uozomvhjz/+2DXiA5Iygu4k5sMPP3RLIlx++eWukVHLli1dGbCyUOrcqOykgnBlnn799Vd3YqJLOYD4XtAsWLDAzaPUsaRKlSp2xRVX2Oeff+7K9rTMitbQjYlK9gi8ASQGBoUj8/ykajytuqJplOdrDLx7926bO3eutW3bluQTkjyC7iR0UtHBQ3MlVTJ+3333BZcBU+apT58+VrRoUdfESCN66iqsZRLU8AgA4kPN0qZMmeK6vqpzsMrL1ZCmXr16ruPr008/7QJvBeEAACTm+WnSpEmuqlMr98T1OpemnkjqCLqTiJUrV9rEiRNdWZXmMukiWHSfyjq1hI8OROXKlQv3pgJI5utw62Lmgw8+cI30vC6wM2bMcAN+unBRlltVNirv1JqoAAD4neFW9aaWjVPCSYklICWhc1ISoVLP6dOn2/Lly12nck+rVq1cp2kF488//7z9/PPPYd1OAMmHugBLaHmmVjpo3LixC7gVeKuKZvDgwS7gVo+IgwcPWp06dVwTIwXkAAAktG7durmvoT1GtNa2kk7qYu+JnhtUkA4kRwTdSUSPHj2sd+/ermmELnR14AkNvFVurnUI6VIOIC6UvVaPiNALFv1ba5iWKVPGli5d6jIKWppHA3uaqz116lT79NNP3UWQlgzT3G2WXQEAJKTFixe7AWBVVoXSuefPP/90055C6Tyk7Pe+fftYaQXJFntuGHgXwbr4VRdyj+Zza81tZbtV0rlr167gY+oqPG3aNLdsDwBcyBNPPOGmqngrIoj+Xbp0aXesqVGjhutGroBblOXWMSZ0wE9omgYASEga1FU1leZgz5w5M3h/oUKF3GoaOhf98ccfwfOWgvNRo0a56VFAckXQHaa5lTrYKHutzuOaq63ui6J/33PPPW7JHnUr37FjR/C1ap4GAOejJQS13rZWQEibNq29//77dv/99wfXZO/UqZM9/PDDli5dOtekcf/+/bZt2zZr1qyZHT582FXcAADgB2Wtdf7RtfCmTZtcxZW33Nctt9zikk9aqUcdzDVFSllxTX86evSoPfXUU+HefOCi0eYvkekgo+7jLVq0sK5du9rtt9/uLoqXLFni5nKreZG6CCu7NHz4cHdgevHFF+nICOCCNFinTIAaM7700kuuO7kyCvqq0nEdR7S0igb3dLypXr265cuXz3LmzGmZMmVyJed6DsuCAQASmnqGeI2C1TTt1ltvdQ2Ddd2rwFpBtvqMZMuWzWbPnu2qPkuVKuWWDfvuu+84PyFZo3t5Itu4caPdfffd1rFjR3v00Uftn3/+ceU0uujVfG2tyd28eXP3XDU3UsMjdS4HgLjQBczYsWNdplsDdion13rcOuZonpymqiiz4DVw1DFIVTQ33XSTmyvHsisAgISmCs8xY8bYG2+84YJprcGt+dvp06cPLlWp85XXAFRVWKq+UsWWrpO9MnPOT0iuCLoTYYmeUJqn/c4779izzz7r5nRrXqWy3co+KRhX4K35lvoeAOLq1KlTrjJGdIxREzUN5vXv3z+4HrdKyxWAK+hWifn5lm4BACChLFu2zE2fVBZbDdFUNq6mnnLixAk3zVKBd9myZe2jjz465/Wcn5Dcsff6QAcGBdxqAqElvjS/0nPFFVe4OSm6GO7Xr59btufll1+26667zv37wIEDrvxco3uMhwCICx0rvIBbqx+sWLHCtm/f7pYEUxfzDRs2uDLyt99+231VF9iY1t/mggYAkNDnJ10Xa6pTgwYN3DzuypUrRykRz5Ahg3tMPUl03RzTGt2cn5DcsQcnMG8kbv369VavXj13ENE8FTWGEB1kVPbplZrrAjhr1qzue31VQK6Owyr3jClTDgDReccKXbCopFxN0WbNmuWaoin47tWrV/B4o8Bbxyh9z8AeAMDvJJQXMNepU8cmTJhgW7dudeeq77//PvhclZnXr1/f+vbt6+Zwsx43UhrKy30IuH/88UerVq2aW4pHHRnVKE1LHQwZMsTat2/vmkBoSQQ9fujQIReU6wA0adIk1wBJ2XAAiCsdxlVe3qRJEytXrpyrnvFoEE/fV6lSxVXXqNRcVTiXXnqpO17FNhUGAICLFVoOrsFeNe9U36IsWbLYt99+a61atbJKlSq5xp6q9hSVld95550xvgeQ3LEnJyAdGLZs2eLKxHVgUdZJyx94SxwosPay3eoUrGV81BRCyyKowYRuBNwA4ktBs7IEmTNndnO3Q6nKRschHV806KfMt7IIOl55WQgAABKKBnO9YPmZZ55xA7+q8lRzNFFiSittrF692q20oX8rAfXQQw9FyXATcCMloQVgAtKBQl2DVSaui1rPtGnT7PTp07Z582aX7dZ8bpV/qsymZs2arnujAnFvGQUAOJ/o2Wnve2Wxp0+fbmvXrnUZb88111xj5cuXt6pVq7ousB4uaAAACUUN0TQ/2zs/jRs3zvUQUa8izeP2zldac1srZkyePNk1Txs2bJhrsKZGn1RgIaWivDyB/f777y5zvXz5cmvdurU7sGiETx3JK1So4A4wu3fvdtmo4sWL2xNPPOFG9wAgLkLL7X799Ve3fIoucrTygejCRisjaEqLgm0NAt57771uPVQtG6YLGUr2AAAJqUWLFu5co/JwL2jWNa6mUWoetxqkff31127Kk5oF69pYq/Yo+63pUfnz52fZSqRoBN0+0EidlunRGrgqKZ8/f7674BXvYDJ06FBXVqMRvlKlSoV7kwEkA6HBcp8+fdyxRVNaVDXTqFEjV0GjTEOtWrXcwJ4uejSVRT0kdMGjYw8ZBABAQuvRo4drjqaVNLwlLLUmtxJRDzzwgH355ZdWpEgRt0yYlgybOnWqbdu2LUplKAPCSMkYSvJB3rx57fnnn3cHjkWLFtkPP/wQDLq9uSrKODGaByA+vIsRdSPXWtyjR492QbWmragZjTLcWoNbTWpmzpzpliDUMUdNG3WsURPH0GVaAAD4L7xAecCAAe774cOHu8Fdzc++6667XAM1lZe3bdvWDRCXKFHClixZ4payjN6hnIAbKRmZ7kTIeKsjuboK66JYCLYBxEdodloDeZquooBb654qe6BVEtSdXOXmWiZMWYXoCLgBAH6dn7yvOh8poNa5SOXmynj//fffrmu5dw2saZW6DlYwTuUVIgVDSj5nvHv27OnmWH7yySfuACQE3ADiKrTDuErG1RBNg3g6rqi8XBc1Wo5l5MiR7tiiEj9lGqIj4AYA+DUgrEFf+fTTT+3GG290SSf1MfICbn398MMPXbZb5zL92+sxAkQCgu5ECrzVVXjp0qVufVwAiCuv3K5bt27uljFjRndM0f0KtLUkWJs2bVxjRvWH0OoIy5YtcxdDAAD4PSA8ZcoUN21SU5tk0qRJVrFiRXvllVfcVCdNfdL177p169z18Pfff29p06Z1WW9KyhEpSLkmUuCtLo0S2jACAOKSQVAQrWoZLb+iTuWirMFPP/1k1157rbtoOXLkiCvjU0B+zz33RCn3AwAgoYQ2PFOgPW/ePPvmm2/c+UnBtKY7KRBv2bKlvfbaa67SSt3N1TxYfUh0XtKUJyo/EUnY2xNJnjx5wr0JAJIRL1gePHiw7dq1y2655RZ3ISMKpnXBU6NGDZszZ46dPn3aXfgoENcSLCwLBgDwi3du6dq1q5uXrWXC6tevbx999JE7/3Tq1MmqVavmAu9WrVpZ586dLVeuXO453jmMKU+INFyRAUASEr0sfO3atfbmm2/aqlWrXBdY8ZYC08VMuXLl7LPPPnPrdCvToIshAm4AgJ800Ks52xMnTnRLg6mkfMyYMa6JmlbUWLFihXueHn/yySetbt26wddSgYVIRKYbAJIILaOi1Q50QaKyPE1NUUm5vmpu3IwZM1xncs3rlurVq1vVqlVdmV769Ond61gdAQDgN51nNLirc49HU5t0PrrvvvtcJtvLeHuNhFlFA5GMVAgAJAHKBrRr1851gFWnVwXanoEDB9qjjz5qXbp0sQ8++MBOnDgRfEwXMJpH583hJuAGAPhRgRW9EkuDvL/99pv7t6Y5SfPmzd1a3OvXr3fnNe9xIeBGJOPqDADCTGV5jz32mPuqNU69zIFK9K644gqXPdAyYLrgUfCtAPuuu+5yGe/QMnJK9gAACSl0upKCbDVKE1VZNWrUyB588EFbuHCha+op6lJeqVIlK1u2rL300kvWoEEDdx4DIh1BNwCEkea/qburGqY1bdo0eH+zZs3s/fffd/PglL3W2twjRoxwFz8qMVdTmtA5cgAA+BVwv/XWW7Z48WI3+Fu4cGEbNGiQGww+fPiwm+rUvXt3y5Ytm2uspqz3hAkTbOrUqa7niIJzINJRXg4AYbR79247evSo60SuCxx5/PHH7YcffrBPP/3UZRbUnEYBuLzzzjsuSK9Vq1aYtxwAkJJ5AbcC6n79+tk111xjOXPmdOejypUru+ae+remPmklDZ2r1ORz/vz57nWq2ipevHiYfwogaUgViD5BAwCQaPr37++y3AcPHgzet2fPHtdw5sorr3SZcM311qH6vffesyJFigSfR9M0AICffv75ZzftSVltr7pq27ZtwSlOy5Ytc/cpAFd/Ed3khRdesLFjx7rseLFixcL6MwBJAZluAAgjXYz8888/tmDBguB9+fLlcwG3Mt8lS5Z0pXlaEix37txRXkvADQDwk4JplZDrXCQaAL7qqqtc+fiuXbvcWtySNWtWF3Bv2rTJ9R4ZNWqUq9Yi4Ab+D0E3AISRSvQUPI8cOdJ27tx5TmmfSs+//vprV6KXOXPmsG0nACDyKNhWRvvDDz+M0rBTA8O6/8iRI1E6k2twWM0/ly5dGmyuBoBGagAQVsoYqEFamzZt3Py3Z555xipUqOAeUxCu0vL9+/fbrFmzglkGupQDAPxunqbzjc5LDRs2tE8++cRVYWlJMNHcbVVged3MvXOT7qtdu3ZYfwYgKWJONwCEmeZvjxs3zjp06GB58uSxMmXKuPnaynKLMt26sNHzWOcUAJCQtOSX5mY///zz5wTeot4iPXv2dOXkyl5XrFjRZsyY4XqRqOkn5yXgwgi6ASCJWLNmjY0ePdrNiStYsKBdd911bm6cLmhomgYASGgnT560zp07u6Bby1Gq2io08PYy2Fu2bLHZs2e7hp7Zs2d3We9JkyYxIAzEEUE3ACRxXNAAAPzy+++/26uvvmrLly+3Jk2a2HPPPRcMvBVwe1OaNPjrnYtC72NAGLgwGqkBQBIS0zgoATcAwC/58+e3bt26ucae6h/yyiuvuPu9TLfs27fPWrdubdOmTQsG3HqMgBuIGzLdAAAAQITbu3ev9e/f31auXGmNGzd2gbjs2bPHdSRXU0+t202gDcQfQTcAAACAKIF306ZN7aGHHnIBtzLd6jvCHG7g4hB0AwAAAAgG3gMGDLAVK1bYL7/84srPf/zxRxdwM4cbuDgE3QAAAACiBN5qqHbgwAH76KOPCLiB/4igGwAAAEAUhw4dcsuDqaEaATfw3xB0AwAAAIiRt2Y3gItH0A0AAAAAgE8YtgIAAAAAwCcE3QAAAAAA+ISgGwAAAAAAnxB0AwAAAADgE4JuAAAAAAB8QtANAAAAAIBPCLoBAAAAAPAJQTcAAAAAAD4h6AYAAAAAwCcE3QAAAAAAmD/+H3Hb7eg05Y8pAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(10, 6))\n", "bar_width = 0.18\n", "x = np.arange(len(models))\n", "\n", "for i, m in enumerate(metric_names):\n", "    ax.bar(x + (i - 2) * bar_width, results_df[m], bar_width, label=m)\n", "\n", "ax.set_xticks(x)\n", "ax.set_xticklabels(results_df.index, rotation=45, ha=\"right\")\n", "ax.set_ylabel(\"Score\")\n", "ax.set_title(\"Model Performance Metrics (Test Set)\")\n", "ax.legend()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "28c76b1e", "metadata": {}, "source": ["## Confusion matrix for best model"]}, {"cell_type": "code", "execution_count": 6, "id": "4307b1e0", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["best_model_name = results_df.index[0]\n", "best_model = models[best_model_name]\n", "y_pred_best = best_model.predict(X_test)\n", "ConfusionMatrixDisplay.from_predictions(y_test, y_pred_best)\n", "plt.title(f\"Confusion Matrix – {best_model_name}\")\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "35841ece", "metadata": {}, "source": ["## <PERSON><PERSON> (One-vs-Rest)"]}, {"cell_type": "code", "execution_count": 7, "id": "494ec81e", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 700x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(7, 6))\n", "for cls in np.unique(y):\n", "    RocCurveDisplay.from_predictions(\n", "        (y_test == cls).astype(int),\n", "        prob_scores[best_model_name][:, cls],\n", "        ax=ax, name=f\"Class {cls} vs rest\")\n", "ax.plot([0, 1], [0, 1], linestyle=\"--\")\n", "ax.set_title(f\"ROC Curves – {best_model_name} (One-vs-Rest)\")\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}