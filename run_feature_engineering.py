import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split

def main():
    # Load the datasets
    df_major = pd.read_csv('data_diag.csv')
    df_subgroup = pd.read_csv('data_diag_maj_sub.csv')
    
    # Extract features and targets
    X_major = df_major.drop('Diagnosis', axis=1)
    y_major = df_major['Diagnosis']
    
    # Train-test split
    X_train_maj, X_test_maj, y_train_maj, y_test_maj = train_test_split(
        X_major, y_major, test_size=0.2, random_state=42, stratify=y_major
    )
      # Feature engineering functions
    def compute_statistical_features(X, cell_type_prefix):
        features = [col for col in X.columns if col.startswith(cell_type_prefix)]
        print(f"Found {len(features)} features for {cell_type_prefix}: {features}")
        stats = {
            f'{cell_type_prefix}_mean': X[features].mean(axis=1),
            f'{cell_type_prefix}_std': X[features].std(axis=1),
            f'{cell_type_prefix}_max': X[features].max(axis=1),
            f'{cell_type_prefix}_min': X[features].min(axis=1),
            f'{cell_type_prefix}_range': X[features].max(axis=1) - X[features].min(axis=1),
            f'{cell_type_prefix}_cv': X[features].std(axis=1) / (X[features].mean(axis=1) + 1e-8)
        }
        return stats
    
    def compute_relational_features(X_eng):
        X_eng['NE_LY_ratio'] = X_eng['NE_mean'] / (X_eng['LY_mean'] + 1e-8)
        X_eng['NE_MO_ratio'] = X_eng['NE_mean'] / (X_eng['MO_mean'] + 1e-8)
        X_eng['LY_MO_ratio'] = X_eng['LY_mean'] / (X_eng['MO_mean'] + 1e-8)
        return X_eng
    
    def compute_geometric_features(X):
        geometric_features = {}
        for cell_type in ['NE', 'LY', 'MO']:
            x_col = f'{cell_type}X'
            y_col = f'{cell_type}Y'
            z_col = f'{cell_type}Z'
            magnitude = np.sqrt(X[x_col]**2 + X[y_col]**2 + X[z_col]**2)
            geometric_features[f'{cell_type}_magnitude'] = magnitude
        return geometric_features
    
    def enhanced_feature_engineering(X):
        X_eng = X.copy()
        # Statistical features for each cell type
        for cell_type in ['NE', 'LY', 'MO']:
            stats = compute_statistical_features(X, cell_type)
            for feature_name, feature_values in stats.items():
                X_eng[feature_name] = feature_values
        # Relational features
        X_eng = compute_relational_features(X_eng)
        # Geometric features
        geometric = compute_geometric_features(X)
        for feature_name, feature_values in geometric.items():
            X_eng[feature_name] = feature_values
        return X_eng
    
    # Apply feature engineering
    print('=== Feature Engineering Results ===')
    print(f'Original feature count: {X_train_maj.shape[1]}')
    
    X_train_maj_eng = enhanced_feature_engineering(X_train_maj)
    X_test_maj_eng = enhanced_feature_engineering(X_test_maj)
    
    print(f'Enhanced feature count: {X_train_maj_eng.shape[1]}')
    print(f'Feature expansion: {X_train_maj_eng.shape[1] - X_train_maj.shape[1]} new features')
    print(f'Percentage increase: {((X_train_maj_eng.shape[1] / X_train_maj.shape[1]) - 1) * 100:.1f}%')
    
    # Display new feature names
    new_features = [col for col in X_train_maj_eng.columns if col not in X_train_maj.columns]
    print(f'\nNew engineered features ({len(new_features)}):')
    for i, feature in enumerate(new_features, 1):
        print(f'{i:2d}. {feature}')
    
    print('\n=== Feature Engineering Completed Successfully! ===')
    
    # Show sample of engineered features
    print('\n=== Sample of Engineered Features ===')
    print('First 5 rows of engineered features:')
    print(X_train_maj_eng[new_features].head())
    
    print('\n=== Statistical Summary of New Features ===')
    print(X_train_maj_eng[new_features].describe())
    
    return X_train_maj_eng, X_test_maj_eng, new_features

if __name__ == "__main__":
    main()
