#!/usr/bin/env python3
"""
Additional Format Generator for Technical Report
Creates various output formats from the Technical_Report.md file
"""

import os
import re
from pathlib import Path
from datetime import datetime

def create_plain_text_version(md_file_path, output_dir):
    """Create a plain text version of the technical report"""
    
    with open(md_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remove markdown formatting
    # Remove headers markdown
    content = re.sub(r'^#{1,6}\s+', '', content, flags=re.MULTILINE)
    
    # Remove code blocks
    content = re.sub(r'```[\s\S]*?```', '[CODE BLOCK]', content)
    
    # Remove inline code
    content = re.sub(r'`([^`]+)`', r'\1', content)
    
    # Remove bold/italic
    content = re.sub(r'\*\*([^*]+)\*\*', r'\1', content)
    content = re.sub(r'\*([^*]+)\*', r'\1', content)
    
    # Remove links
    content = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', content)
    
    # Clean up extra whitespace
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    
    # Add header
    header = f"""
COMPREHENSIVE MACHINE LEARNING ANALYSIS OF CELL POPULATION DATA
FOR ACUTE LEUKEMIA DIAGNOSIS: TECHNICAL DOCUMENTATION

Generated on: {datetime.now().strftime("%B %d, %Y at %I:%M %p")}
Source: Technical_Report.md

{'='*80}

"""
    
    content = header + content
    
    # Save plain text version
    txt_output_path = output_dir / "Technical_Report.txt"
    with open(txt_output_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ Plain text version created: {txt_output_path}")
    return txt_output_path

def create_summary_document(md_file_path, output_dir):
    """Create an executive summary document"""
    
    with open(md_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract key sections for summary
    summary_content = f"""
EXECUTIVE SUMMARY
Comprehensive Machine Learning Analysis for Acute Leukemia Diagnosis

Generated: {datetime.now().strftime("%B %d, %Y")}

OVERVIEW
========
This technical report presents a comprehensive machine learning analysis for acute leukemia 
diagnosis using cell population data from automated hematology analyzers. The study evaluates 
multiple machine learning algorithms and achieves exceptional diagnostic performance.

KEY FINDINGS
============
• Dataset: 791 patient samples with 18 cell population parameters
• Best Performance: AUC > 0.99 for major diagnostic categories
• Subgroup Classification: AUC = 0.87 for detailed subgroup analysis
• Feature Engineering: 42 engineered features from 18 original parameters
• Model Interpretability: SHAP analysis for clinical understanding

METHODOLOGY
===========
• Advanced feature engineering (statistical, relational, geometric)
• Multiple ML algorithms: Random Forest, XGBoost, CatBoost, SVM, Logistic Regression
• Bootstrap confidence intervals for statistical robustness
• SHAP analysis for model interpretability
• Comprehensive cross-validation and performance evaluation

CLINICAL SIGNIFICANCE
====================
• Cost-effective screening tool for resource-limited settings
• Rapid diagnosis using existing laboratory infrastructure
• High accuracy comparable to specialized diagnostic methods
• Interpretable results for clinical decision-making

TECHNICAL ACHIEVEMENTS
=====================
• Exceptional AUC performance (>0.99 for major categories)
• Robust statistical validation with confidence intervals
• Comprehensive feature engineering pipeline
• Production-ready code implementation
• Detailed visualization and analysis framework

IMPLEMENTATION
==============
• Python-based machine learning pipeline
• Scikit-learn, XGBoost, CatBoost algorithms
• SHAP for explainable AI
• Bootstrap statistical analysis
• Comprehensive visualization suite

CONCLUSION
==========
The developed machine learning approach demonstrates exceptional potential for acute leukemia 
diagnosis using cell population data. The combination of advanced feature engineering, robust 
statistical validation, and model interpretability makes this approach suitable for clinical 
implementation in various healthcare settings.

For complete technical details, refer to the full Technical Report documentation.
"""
    
    # Save summary
    summary_path = output_dir / "Executive_Summary.txt"
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write(summary_content)
    
    print(f"✅ Executive summary created: {summary_path}")
    return summary_path

def create_code_extraction(md_file_path, output_dir):
    """Extract all code blocks from the markdown file"""
    
    with open(md_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find all code blocks
    code_blocks = re.findall(r'```python\n(.*?)\n```', content, re.DOTALL)
    
    if not code_blocks:
        code_blocks = re.findall(r'```\n(.*?)\n```', content, re.DOTALL)
    
    # Create combined code file
    code_content = f'''"""
Extracted Code from Technical Report
Generated: {datetime.now().strftime("%B %d, %Y at %I:%M %p")}

This file contains all code blocks extracted from the Technical_Report.md file.
Each code block is separated and labeled for reference.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import roc_auc_score, classification_report, confusion_matrix
import xgboost as xgb
from catboost import CatBoostClassifier
import shap
from scipy import stats

# ============================================================================
# EXTRACTED CODE BLOCKS
# ============================================================================

'''
    
    for i, code_block in enumerate(code_blocks, 1):
        code_content += f'''
# ============================================================================
# CODE BLOCK {i}
# ============================================================================

{code_block.strip()}

'''
    
    # Save code file
    code_path = output_dir / "Extracted_Code.py"
    with open(code_path, 'w', encoding='utf-8') as f:
        f.write(code_content)
    
    print(f"✅ Code extraction completed: {code_path}")
    print(f"📊 Total code blocks extracted: {len(code_blocks)}")
    return code_path

def create_bibliography(output_dir):
    """Create a bibliography and references file"""
    
    bibliography = f"""
BIBLIOGRAPHY AND REFERENCES
Comprehensive Machine Learning Analysis for Acute Leukemia Diagnosis

Generated: {datetime.now().strftime("%B %d, %Y")}

TECHNICAL REFERENCES
===================

Machine Learning Libraries:
• Scikit-learn: Pedregosa et al. (2011). Scikit-learn: Machine Learning in Python. JMLR 12, pp. 2825-2830.
• XGBoost: Chen & Guestrin (2016). XGBoost: A Scalable Tree Boosting System. KDD '16.
• CatBoost: Prokhorenkova et al. (2018). CatBoost: unbiased boosting with categorical features. NeurIPS.
• SHAP: Lundberg & Lee (2017). A Unified Approach to Interpreting Model Predictions. NeurIPS.

Statistical Methods:
• Bootstrap Methods: Efron & Tibshirani (1993). An Introduction to the Bootstrap. Chapman & Hall.
• ROC Analysis: Hanley & McNeil (1982). The meaning and use of the area under a ROC curve. Radiology.
• Cross-validation: Kohavi (1995). A study of cross-validation and bootstrap for accuracy estimation. IJCAI.

CLINICAL BACKGROUND
==================

Acute Leukemia Diagnosis:
• WHO Classification of Tumours of Haematopoietic and Lymphoid Tissues (2017). 4th Edition Revised.
• Arber et al. (2016). The 2016 revision to the WHO classification of myeloid neoplasms. Blood.
• Tefferi & Vardiman (2009). Classification and diagnosis of myeloproliferative neoplasms. Blood.

Automated Hematology:
• Briggs et al. (2012). Assessment of an immature platelet fraction in the diagnosis of inherited thrombocytopenias. Br J Haematol.
• Urrechaga et al. (2013). The role of automated measurement of RBC subpopulations in differential diagnosis of microcytic anemia. Am J Clin Pathol.

METHODOLOGY REFERENCES
=====================

Feature Engineering:
• Guyon & Elisseeff (2003). An introduction to variable and feature selection. JMLR.
• Zheng & Casari (2018). Feature Engineering for Machine Learning. O'Reilly Media.

Model Interpretability:
• Molnar (2020). Interpretable Machine Learning. Lulu.com.
• Ribeiro et al. (2016). "Why Should I Trust You?": Explaining the Predictions of Any Classifier. KDD.

IMPLEMENTATION TOOLS
===================

Programming Environment:
• Python 3.8+
• Jupyter Notebook / JupyterLab
• Pandas for data manipulation
• NumPy for numerical computing
• Matplotlib/Seaborn for visualization

Development Framework:
• Git for version control
• Conda/pip for package management
• Docker for containerization (optional)

REPRODUCIBILITY
===============

All code and analysis are designed for reproducibility with:
• Fixed random seeds (random_state=42)
• Documented software versions
• Standardized data preprocessing
• Comprehensive logging and documentation

For questions or clarifications, refer to the complete Technical Report documentation.
"""
    
    # Save bibliography
    bib_path = output_dir / "Bibliography_and_References.txt"
    with open(bib_path, 'w', encoding='utf-8') as f:
        f.write(bibliography)
    
    print(f"✅ Bibliography created: {bib_path}")
    return bib_path

def main():
    """Main function to create additional formats"""
    print("🔄 Creating additional output formats...")
    
    # Check if markdown file exists
    md_file = Path("Technical_Report.md")
    if not md_file.exists():
        print(f"❌ Error: {md_file} not found!")
        return False
    
    # Create output directory
    output_dir = Path("technical_report_output")
    output_dir.mkdir(exist_ok=True)
    
    try:
        # Create various formats
        print("📄 Creating plain text version...")
        txt_file = create_plain_text_version(md_file, output_dir)
        
        print("📋 Creating executive summary...")
        summary_file = create_summary_document(md_file, output_dir)
        
        print("💻 Extracting code blocks...")
        code_file = create_code_extraction(md_file, output_dir)
        
        print("📚 Creating bibliography...")
        bib_file = create_bibliography(output_dir)
        
        print("\n🎉 Additional formats created successfully!")
        print(f"📁 Output directory: {output_dir.absolute()}")
        print(f"📄 Plain text: {txt_file}")
        print(f"📋 Executive summary: {summary_file}")
        print(f"💻 Extracted code: {code_file}")
        print(f"📚 Bibliography: {bib_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating additional formats: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
