"""
Advanced Ensemble Methods for Acute Leukemia Diagnosis
Enhanced ML Pipeline with Voting Classifiers and Stacking
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report
import warnings
warnings.filterwarnings('ignore')

def advanced_feature_engineering(df):
    """Enhanced feature engineering with additional transformations"""
    X = df.drop('diagnosis', axis=1)
    
    # Original features
    feature_names = list(X.columns)
    
    # Enhanced feature engineering
    enhanced_features = X.copy()
    
    # 1. Ratios between cell types
    enhanced_features['NE_LY_ratio'] = X['NEX'] / (X['LYX'] + 1e-8)
    enhanced_features['NE_MO_ratio'] = X['NEX'] / (X['MOX'] + 1e-8)
    enhanced_features['LY_MO_ratio'] = X['LYX'] / (X['MOX'] + 1e-8)
    
    # 2. Centroid distances
    enhanced_features['NE_centroid_dist'] = np.sqrt(X['NEX']**2 + X['NEY']**2 + X['NEZ']**2)
    enhanced_features['LY_centroid_dist'] = np.sqrt(X['LYX']**2 + X['LYY']**2 + X['LYZ']**2)
    enhanced_features['MO_centroid_dist'] = np.sqrt(X['MOX']**2 + X['MOY']**2 + X['MOZ']**2)
    
    # 3. Volume measures
    enhanced_features['NE_volume'] = X['NEWX'] * X['NEWY'] * X['NEWZ']
    enhanced_features['LY_volume'] = X['LYWX'] * X['LYWY'] * X['LYWZ']
    enhanced_features['MO_volume'] = X['MOWX'] * X['MOWY'] * X['MOWZ']
    
    # 4. Inter-cellular distances
    enhanced_features['NE_LY_distance'] = np.sqrt((X['NEX']-X['LYX'])**2 + (X['NEY']-X['LYY'])**2 + (X['NEZ']-X['LYZ'])**2)
    enhanced_features['NE_MO_distance'] = np.sqrt((X['NEX']-X['MOX'])**2 + (X['NEY']-X['MOY'])**2 + (X['NEZ']-X['MOZ'])**2)
    enhanced_features['LY_MO_distance'] = np.sqrt((X['LYX']-X['MOX'])**2 + (X['LYY']-X['MOY'])**2 + (X['LYZ']-X['MOZ'])**2)
    
    # 5. Aspect ratios
    enhanced_features['NE_aspect_ratio'] = X['NEWX'] / (X['NEWY'] + 1e-8)
    enhanced_features['LY_aspect_ratio'] = X['LYWX'] / (X['LYWY'] + 1e-8)
    enhanced_features['MO_aspect_ratio'] = X['MOWX'] / (X['MOWY'] + 1e-8)
    
    # 6. Statistical measures
    enhanced_features['total_NE'] = X['NEX'] + X['NEY'] + X['NEZ']
    enhanced_features['total_LY'] = X['LYX'] + X['LYY'] + X['LYZ']
    enhanced_features['total_MO'] = X['MOX'] + X['MOY'] + X['MOZ']
    
    # 7. Polynomial features (selected)
    enhanced_features['NEX_squared'] = X['NEX']**2
    enhanced_features['LYX_squared'] = X['LYX']**2
    enhanced_features['MOX_squared'] = X['MOX']**2
    
    return enhanced_features

def create_advanced_ensemble(X_train, y_train):
    """Create advanced ensemble model with multiple algorithms"""
    
    # Individual models
    rf = RandomForestClassifier(n_estimators=200, max_depth=10, random_state=42)
    gb = GradientBoostingClassifier(n_estimators=200, max_depth=6, random_state=42)
    lr = LogisticRegression(max_iter=1000, random_state=42)
    svm = SVC(probability=True, random_state=42)
    mlp = MLPClassifier(hidden_layer_sizes=(100, 50), max_iter=1000, random_state=42)
    
    # Voting classifier
    voting_clf = VotingClassifier(
        estimators=[
            ('rf', rf),
            ('gb', gb),
            ('lr', lr),
            ('svm', svm),
            ('mlp', mlp)
        ],
        voting='soft'
    )
    
    return voting_clf

def main():
    print("🚀 Advanced Ensemble Analysis for Acute Leukemia Diagnosis")
    print("="*70)
    
    # Load datasets
    df_major = pd.read_csv('data_diag.csv')
    df_subgroup = pd.read_csv('data_diag_maj_sub.csv')
    
    results = {}
    
    for dataset_name, df in [('Major Categories', df_major), ('Subgroup Categories', df_subgroup)]:
        print(f"\n📊 Analyzing {dataset_name}...")
        print("-" * 50)
        
        # Enhanced feature engineering
        X_enhanced = advanced_feature_engineering(df)
        y = df['diagnosis']
        
        print(f"Original features: {len(df.columns)-1}")
        print(f"Enhanced features: {len(X_enhanced.columns)}")
        
        # Train-test split
        X_train, X_test, y_train, y_test = train_test_split(
            X_enhanced, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Feature scaling
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Create and train ensemble
        ensemble = create_advanced_ensemble(X_train_scaled, y_train)
        ensemble.fit(X_train_scaled, y_train)
        
        # Predictions
        y_pred = ensemble.predict(X_test_scaled)
        y_pred_proba = ensemble.predict_proba(X_test_scaled)
        
        # Calculate metrics
        accuracy = accuracy_score(y_test, y_pred)
        auc = roc_auc_score(y_test, y_pred_proba, multi_class='ovr')
        
        # Cross-validation
        cv_scores = cross_val_score(ensemble, X_train_scaled, y_train, cv=5, scoring='accuracy')
        
        # Store results
        results[dataset_name] = {
            'accuracy': accuracy,
            'auc': auc,
            'cv_mean': cv_scores.mean(),
            'cv_std': cv_scores.std(),
            'n_features': len(X_enhanced.columns)
        }
        
        print(f"✅ Ensemble Accuracy: {accuracy:.4f}")
        print(f"✅ Ensemble AUC: {auc:.4f}")
        print(f"✅ CV Accuracy: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
        print(f"✅ Feature count: {len(X_enhanced.columns)}")
        
        # Classification report
        print("\n📈 Classification Report:")
        print(classification_report(y_test, y_pred))
    
    # Summary
    print("\n" + "="*70)
    print("🎯 ADVANCED ENSEMBLE RESULTS SUMMARY")
    print("="*70)
    
    for dataset_name, metrics in results.items():
        print(f"\n📊 {dataset_name}:")
        print(f"   • Ensemble Accuracy: {metrics['accuracy']:.4f}")
        print(f"   • Ensemble AUC: {metrics['auc']:.4f}")
        print(f"   • CV Accuracy: {metrics['cv_mean']:.4f} ± {metrics['cv_std']:.4f}")
        print(f"   • Features Used: {metrics['n_features']}")
    
    print("\n🏥 Clinical Impact:")
    print("   • Multi-algorithm consensus for robust predictions")
    print("   • Enhanced feature engineering for better discrimination")
    print("   • Cross-validated performance for reliability")
    print("   • Production-ready ensemble model")
    
    print("\n✅ Analysis Complete!")

if __name__ == "__main__":
    main()
