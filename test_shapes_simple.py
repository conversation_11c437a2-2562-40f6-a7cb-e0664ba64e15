import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import roc_auc_score
import warnings
warnings.filterwarnings('ignore')

print("=== SHAPE VERIFICATION TEST ===")

# Load major categories data
df = pd.read_csv('data_diag.csv')
X = df.drop('Diagnosis', axis=1)
y = df['Diagnosis']

print(f"Dataset shape: {df.shape}")
print(f"Classes: {sorted(y.unique())}")
print(f"Number of classes: {len(y.unique())}")

# Split data
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42, stratify=y
)

# Train model
rf = RandomForestClassifier(n_estimators=100, random_state=42)
rf.fit(X_train, y_train)

# Get predictions
y_pred_proba = rf.predict_proba(X_test)

print(f"\nPrediction shape: {y_pred_proba.shape}")
print(f"Expected columns: {len(y.unique())}")
print(f"Actual columns: {y_pred_proba.shape[1]}")

# Test if shapes match
n_classes_true = len(np.unique(y_test))
n_cols_scores = y_pred_proba.shape[1]

print(f"\nShape Analysis:")
print(f"Classes in test set: {n_classes_true}")
print(f"Probability columns: {n_cols_scores}")
print(f"Shapes match: {n_classes_true == n_cols_scores}")

# Try AUC calculation
try:
    if n_classes_true == n_cols_scores:
        auc = roc_auc_score(y_test, y_pred_proba, multi_class='ovr', average='macro')
        print(f"AUC calculation SUCCESS: {auc:.4f}")
        print("RESULT: No shape mismatch - this is CORRECT behavior")
    else:
        print("SHAPE MISMATCH detected")
        if n_cols_scores > n_classes_true:
            y_adjusted = y_pred_proba[:, :n_classes_true]
            y_adjusted = y_adjusted / y_adjusted.sum(axis=1, keepdims=True)
            auc = roc_auc_score(y_test, y_adjusted, multi_class='ovr', average='macro')
            print(f"AUC after adjustment: {auc:.4f}")
        else:
            print("Cannot fix: insufficient columns")
except Exception as e:
    print(f"AUC calculation failed: {e}")

print("\n=== CONCLUSION ===")
if y_pred_proba.shape[1] == 3:
    print("CORRECT: Model produces exactly 3 columns for 3-class problem")
    print("The shape mismatch handling code should NOT be triggered")
else:
    print(f"ISSUE: Model produces {y_pred_proba.shape[1]} columns instead of 3")
