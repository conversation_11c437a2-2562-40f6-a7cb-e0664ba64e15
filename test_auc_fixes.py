#!/usr/bin/env python3
"""
Test script to verify the bootstrap AUC calculation fixes
"""

import pandas as pd
import numpy as np
from sklearn.metrics import roc_auc_score

def calculate_auc_ci(y_true, y_scores, confidence=0.95, n_bootstrap=100):
    """
    Fixed version of calculate_auc_ci function
    """
    def auc_statistic(y_true, y_scores):
        """Calculate AUC using One-vs-Rest multi-class approach"""
        try:
            # Ensure correct shape alignment
            if hasattr(y_scores, 'shape') and len(y_scores.shape) > 1:
                # Check if number of classes in y_true matches columns in y_scores
                n_classes_true = len(np.unique(y_true))
                n_cols_scores = y_scores.shape[1]
                
                if n_classes_true != n_cols_scores:
                    # Try to align by selecting appropriate columns or using macro averaging
                    if n_cols_scores > n_classes_true:
                        # Use only the first n_classes_true columns
                        y_scores = y_scores[:, :n_classes_true]
                    else:
                        # Not enough score columns - return NaN
                        return np.nan
            
            return roc_auc_score(y_true, y_scores, multi_class='ovr', average='macro')
        except (ValueError, IndexError) as e:
            return np.nan
    
    # Convert inputs to numpy arrays to avoid pandas indexing issues
    if hasattr(y_true, 'values'):
        y_true_array = y_true.values
    else:
        y_true_array = np.array(y_true)
    
    if hasattr(y_scores, 'values'):
        y_scores_array = y_scores.values
    else:
        y_scores_array = np.array(y_scores)
    
    # Initialize bootstrap results
    n_samples = len(y_true_array)
    bootstrap_aucs = []
    
    # Set random seed for reproducibility
    np.random.seed(42)
    
    # Generate bootstrap samples
    for iteration in range(n_bootstrap):
        try:
            # Bootstrap sample indices with replacement
            indices = np.random.choice(n_samples, size=n_samples, replace=True)
            y_true_boot = y_true_array[indices]
            y_scores_boot = y_scores_array[indices]
            
            # Calculate AUC for bootstrap sample
            auc_boot = auc_statistic(y_true_boot, y_scores_boot)
            if not np.isnan(auc_boot):
                bootstrap_aucs.append(auc_boot)
        except (IndexError, ValueError) as e:
            # Skip this bootstrap iteration if there's an error
            continue
    
    if len(bootstrap_aucs) == 0:
        return np.nan, np.nan, []
    
    # Calculate confidence interval using percentile method
    alpha = 1 - confidence
    lower_percentile = (alpha/2) * 100
    upper_percentile = (1 - alpha/2) * 100
    
    ci_lower = np.percentile(bootstrap_aucs, lower_percentile)
    ci_upper = np.percentile(bootstrap_aucs, upper_percentile)
    
    return ci_lower, ci_upper, bootstrap_aucs

def test_bootstrap_fixes():
    """Test the bootstrap AUC fixes"""
    print("🧪 Testing Bootstrap AUC Calculation Fixes")
    print("=" * 50)
    
    # Test Case 1: Pandas Series/DataFrame (original error case)
    print("\n✅ Test 1: Pandas Series/DataFrame Input")
    np.random.seed(42)
    n_samples = 150
    n_classes = 4
    
    # Create test data that mimics the original problem
    y_true = pd.Series(np.random.randint(0, n_classes, n_samples))
    y_scores = pd.DataFrame(np.random.rand(n_samples, n_classes))
    
    # Normalize scores to sum to 1 (like predict_proba output)
    y_scores = y_scores.div(y_scores.sum(axis=1), axis=0)
    
    try:
        ci_lower, ci_upper, bootstrap_aucs = calculate_auc_ci(y_true, y_scores, n_bootstrap=50)
        print(f"   ✅ Success: CI = [{ci_lower:.4f}, {ci_upper:.4f}]")
        print(f"   📊 Bootstrap samples: {len(bootstrap_aucs)}")
    except Exception as e:
        print(f"   ❌ Failed: {e}")
    
    # Test Case 2: Shape mismatch scenario
    print("\n✅ Test 2: Shape Mismatch Handling")
    y_true_mismatch = pd.Series(np.random.randint(0, 3, n_samples))  # 3 classes
    y_scores_mismatch = pd.DataFrame(np.random.rand(n_samples, 5))   # 5 columns
    
    try:
        ci_lower, ci_upper, bootstrap_aucs = calculate_auc_ci(y_true_mismatch, y_scores_mismatch, n_bootstrap=30)
        print(f"   ✅ Success: CI = [{ci_lower:.4f}, {ci_upper:.4f}]")
        print(f"   📊 Bootstrap samples: {len(bootstrap_aucs)}")
    except Exception as e:
        print(f"   ❌ Failed: {e}")
    
    # Test Case 3: NumPy arrays (should work as before)
    print("\n✅ Test 3: NumPy Array Input")
    y_true_np = np.random.randint(0, n_classes, n_samples)
    y_scores_np = np.random.rand(n_samples, n_classes)
    y_scores_np = y_scores_np / y_scores_np.sum(axis=1, keepdims=True)
    
    try:
        ci_lower, ci_upper, bootstrap_aucs = calculate_auc_ci(y_true_np, y_scores_np, n_bootstrap=50)
        print(f"   ✅ Success: CI = [{ci_lower:.4f}, {ci_upper:.4f}]")
        print(f"   📊 Bootstrap samples: {len(bootstrap_aucs)}")
    except Exception as e:
        print(f"   ❌ Failed: {e}")
    
    # Test Case 4: Edge case - small dataset
    print("\n✅ Test 4: Small Dataset Edge Case")
    y_true_small = pd.Series([0, 1, 2, 0, 1, 2])
    y_scores_small = pd.DataFrame([[0.7, 0.2, 0.1], [0.1, 0.8, 0.1], [0.1, 0.1, 0.8], 
                                   [0.6, 0.3, 0.1], [0.2, 0.7, 0.1], [0.1, 0.2, 0.7]])
    
    try:
        ci_lower, ci_upper, bootstrap_aucs = calculate_auc_ci(y_true_small, y_scores_small, n_bootstrap=20)
        print(f"   ✅ Success: CI = [{ci_lower:.4f}, {ci_upper:.4f}]")
        print(f"   📊 Bootstrap samples: {len(bootstrap_aucs)}")
    except Exception as e:
        print(f"   ❌ Failed: {e}")
    
    print("\n🎉 Bootstrap AUC Testing Complete!")
    print("These fixes should resolve the original '[indices] not in index' errors.")

if __name__ == "__main__":
    test_bootstrap_fixes()
