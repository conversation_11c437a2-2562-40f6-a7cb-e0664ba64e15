import pandas as pd
import numpy as np

# Test data loading
print("Testing data loading...")
df1 = pd.read_csv('data_diag.csv')
df2 = pd.read_csv('data_diag_maj_sub.csv')
print(f"data_diag.csv shape: {df1.shape}")
print(f"data_diag_maj_sub.csv shape: {df2.shape}")
print("Data loading successful!")
print("\nFirst few rows of data_diag.csv:")
print(df1.head(3))
print("\nColumn names:")
print(list(df1.columns))
