from sklearn.model_selection import RandomizedSearchCV, StratifiedKFold
import numpy as np

def tune_model(model, param_grid, X, y, name, n_iter=20):
    """Tune hyperparameters using RandomizedSearchCV"""
    print(f"Tuning {name}...")
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    search = RandomizedSearchCV(
        model, param_grid, n_iter=n_iter,
        cv=cv, scoring='accuracy', random_state=42, n_jobs=-1
    )
    
    search.fit(X, y)
    print(f"Best parameters for {name}: {search.best_params_}")
    print(f"Best score: {search.best_score_:.4f}")
    
    return search.best_estimator_

# Example parameter grids
param_grids = {
    "XGBoost": {
        "n_estimators": [100, 200, 300, 500],
        "learning_rate": [0.01, 0.05, 0.1, 0.2],
        "max_depth": [3, 4, 5, 6, 8],
        "subsample": [0.6, 0.8, 1.0],
        "colsample_bytree": [0.6, 0.8, 1.0],
        "reg_alpha": [0, 0.1, 0.5, 1.0],
        "reg_lambda": [0, 0.1, 1.0, 5.0]
    },
    "LightGBM": {
        "n_estimators": [100, 200, 300, 500],
        "learning_rate": [0.01, 0.05, 0.1, 0.2],
        "num_leaves": [20, 31, 50, 80],
        "max_depth": [-1, 5, 10, 15],
        "reg_alpha": [0, 0.1, 0.5],
        "reg_lambda": [0, 0.1, 0.5]
    }
}

# Usage example:
# tuned_xgb = tune_model(
#     xgb.XGBClassifier(random_state=42),
#     param_grids["XGBoost"],
#     X_train, y_train,
#     "XGBoost"
# )