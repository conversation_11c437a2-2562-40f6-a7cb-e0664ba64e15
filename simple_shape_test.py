#!/usr/bin/env python3
"""
Simple test to verify shape behavior for 3-class major categories dataset
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import roc_auc_score

print("=== SIMPLE SHAPE VERIFICATION TEST ===")

# Load major categories data
df = pd.read_csv('data_diag.csv')
X = df.drop('Diagnosis', axis=1)
y = df['Diagnosis']

print(f"Dataset shape: {df.shape}")
print(f"Classes in dataset: {sorted(y.unique())}")
print(f"Number of classes: {len(y.unique())}")

# Train-test split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

print(f"\nTraining set: {X_train.shape}, Test set: {X_test.shape}")
print(f"Training classes: {sorted(y_train.unique())}")
print(f"Test classes: {sorted(y_test.unique())}")

# Train a simple Random Forest
rf = RandomForestClassifier(n_estimators=50, random_state=42)
rf.fit(X_train, y_train)

# Get predictions
y_pred_proba = rf.predict_proba(X_test)

print(f"\n=== PREDICTION ANALYSIS ===")
print(f"Prediction probabilities shape: {y_pred_proba.shape}")
print(f"Expected shape for 3 classes: ({len(y_test)}, 3)")
print(f"✅ Shape matches expected: {y_pred_proba.shape[1] == 3}")

# Verify probability properties
print(f"\nProbability properties:")
print(f"Sum of probabilities (should be ~1.0): min={y_pred_proba.sum(axis=1).min():.6f}, max={y_pred_proba.sum(axis=1).max():.6f}")
print(f"Min probability: {y_pred_proba.min():.6f}")
print(f"Max probability: {y_pred_proba.max():.6f}")

# Test AUC calculation
print(f"\n=== AUC CALCULATION TEST ===")
try:
    n_classes_test = len(np.unique(y_test))
    n_proba_cols = y_pred_proba.shape[1]
    
    print(f"Classes in y_test: {n_classes_test}")
    print(f"Probability columns: {n_proba_cols}")
    print(f"✅ Perfect match: {n_classes_test == n_proba_cols}")
    
    if n_classes_test == n_proba_cols:
        auc = roc_auc_score(y_test, y_pred_proba, multi_class='ovr', average='macro')
        print(f"✅ AUC calculation SUCCESS: {auc:.4f}")
        print(f"✅ NO SHAPE MISMATCH - This is the correct behavior!")
    else:
        print(f"❌ UNEXPECTED: Shape mismatch detected: {n_classes_test} != {n_proba_cols}")
        
except Exception as e:
    print(f"❌ AUC calculation FAILED: {e}")

print(f"\n=== FINAL VERIFICATION ===")
print(f"✅ Major categories dataset has exactly 3 classes: {len(y.unique()) == 3}")
print(f"✅ Model produces exactly 3 probability columns: {y_pred_proba.shape[1] == 3}")
print(f"✅ No shape mismatch should occur for properly implemented models on this dataset")
print(f"🎯 CONCLUSION: The current shape mismatch handling is a safety net, but should NOT be triggered for major categories")
