"""
Test script for SHAP analysis implementation
"""
import os
import numpy as np
import pandas as pd
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
import shap

# Import the ModelInterpretability class
from advanced_ml_enhancements import ModelInterpretability

def test_shap_with_tree_model():
    """Test SHAP analysis with RandomForestClassifier"""
    print("\nTesting SHAP analysis with RandomForestClassifier...")
    
    # Create synthetic binary classification data
    X, y = make_classification(n_samples=1000, n_features=20, n_classes=2, random_state=42)
    X = pd.DataFrame(X, columns=[f'feature_{i}' for i in range(X.shape[1])])
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Train model
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)
    
    # Test SHAP analysis
    interpretability = ModelInterpretability()
    shap_values, shap_summary = interpretability.shap_analysis(model, X_train, X_test, max_samples=100)
    
    print("Shape of SHAP values:", np.array(shap_values).shape)
    print("\nTop 5 important features:")
    print(shap_summary.head())
    
    return shap_values is not None and shap_summary is not None

def test_shap_with_linear_model():
    """Test SHAP analysis with LogisticRegression"""
    print("\nTesting SHAP analysis with LogisticRegression...")
    
    # Create synthetic binary classification data
    X, y = make_classification(n_samples=1000, n_features=10, n_classes=2, random_state=42)
    X = pd.DataFrame(X, columns=[f'feature_{i}' for i in range(X.shape[1])])
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Train model
    model = LogisticRegression(random_state=42)
    model.fit(X_train, y_train)
    
    # Test SHAP analysis
    interpretability = ModelInterpretability()
    shap_values, shap_summary = interpretability.shap_analysis(model, X_train, X_test, max_samples=100)
    
    print("Shape of SHAP values:", np.array(shap_values).shape)
    print("\nTop 5 important features:")
    print(shap_summary.head())
    
    return shap_values is not None and shap_summary is not None

def test_shap_multiclass():
    """Test SHAP analysis with multi-class classification"""
    print("\nTesting SHAP analysis with multi-class classification...")
    
    # Create synthetic multi-class data
    X, y = make_classification(n_samples=1000, n_features=15, n_classes=3, n_informative=10, random_state=42)
    X = pd.DataFrame(X, columns=[f'feature_{i}' for i in range(X.shape[1])])
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Train model
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)
    
    # Test SHAP analysis
    interpretability = ModelInterpretability()
    shap_values, shap_summary = interpretability.shap_analysis(model, X_train, X_test, max_samples=100)
    
    print("Shape of SHAP values:", np.array(shap_values).shape)
    print("\nTop 5 important features:")
    print(shap_summary.head())
    
    return shap_values is not None and shap_summary is not None

def test_different_input_formats():
    """Test SHAP analysis with different input formats"""
    print("\nTesting SHAP analysis with different input formats...")
    
    # Create synthetic data
    X, y = make_classification(n_samples=1000, n_features=10, n_classes=2, random_state=42)
    
    # Test with numpy arrays
    X_train_np, X_test_np, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Test with pandas DataFrames
    X_train_pd = pd.DataFrame(X_train_np, columns=[f'feature_{i}' for i in range(X_train_np.shape[1])])
    X_test_pd = pd.DataFrame(X_test_np, columns=[f'feature_{i}' for i in range(X_test_np.shape[1])])
    
    # Train model
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_train_np, y_train)
    
    # Test both formats
    interpretability = ModelInterpretability()
    
    print("Testing with numpy arrays...")
    shap_values_np, summary_np = interpretability.shap_analysis(model, X_train_np, X_test_np, max_samples=100)
    
    print("Testing with pandas DataFrames...")
    shap_values_pd, summary_pd = interpretability.shap_analysis(model, X_train_pd, X_test_pd, max_samples=100)
    
    return (shap_values_np is not None and summary_np is not None and 
            shap_values_pd is not None and summary_pd is not None)

def test_edge_cases():
    """Test SHAP analysis with edge cases"""
    print("\nTesting SHAP analysis with edge cases...")
    
    # Test with single feature
    X_single = np.random.rand(1000, 1)
    y_single = (X_single > 0.5).ravel()
    
    # Test with many features
    X_many = np.random.rand(1000, 100)
    y_many = (X_many.mean(axis=1) > 0.5).astype(int)
    
    # Split data
    X_train_single, X_test_single, y_train_single, y_test_single = train_test_split(
        X_single, y_single, test_size=0.2, random_state=42
    )
    X_train_many, X_test_many, y_train_many, y_test_many = train_test_split(
        X_many, y_many, test_size=0.2, random_state=42
    )
    
    # Train models
    model_single = RandomForestClassifier(n_estimators=100, random_state=42)
    model_single.fit(X_train_single, y_train_single)
    
    model_many = RandomForestClassifier(n_estimators=100, random_state=42)
    model_many.fit(X_train_many, y_train_many)
    
    # Test SHAP analysis
    interpretability = ModelInterpretability()
    
    print("Testing with single feature...")
    shap_values_single, summary_single = interpretability.shap_analysis(
        model_single, X_train_single, X_test_single, max_samples=100
    )
    
    print("Testing with many features...")
    shap_values_many, summary_many = interpretability.shap_analysis(
        model_many, X_train_many, X_test_many, max_samples=100
    )
    
    return (shap_values_single is not None and summary_single is not None and 
            shap_values_many is not None and summary_many is not None)

def run_all_tests():
    """Run all SHAP analysis tests"""
    print("Starting SHAP analysis tests...")
    
    tests = {
        "Tree-based model": test_shap_with_tree_model,
        "Linear model": test_shap_with_linear_model,
        "Multi-class": test_shap_multiclass,
        "Different input formats": test_different_input_formats,
        "Edge cases": test_edge_cases
    }
    
    results = {}
    for name, test_func in tests.items():
        try:
            result = test_func()
            results[name] = "✅ Passed" if result else "❌ Failed"
        except Exception as e:
            results[name] = f"❌ Error: {str(e)}"
    
    print("\nTest Results:")
    print("=" * 50)
    for name, result in results.items():
        print(f"{name}: {result}")
    
    return all(result.startswith("✅") for result in results.values())

if __name__ == "__main__":
    try:
        print("Starting test execution...")
        print("Python working directory:", os.getcwd())
        print("Importing required modules...")
        success = run_all_tests()
        print("\nTest execution completed.")
        print("Final result:", "All tests passed!" if success else "Some tests failed!")
    except Exception as e:
        print(f"\nError during test execution: {str(e)}")
        import traceback
        print("\nFull traceback:")
        traceback.print_exc()
