"""
Enhanced Machine Learning Model Comparison with:
1. Cross-Validation
2. Hyperparameter Tuning
3. Feature Engineering

This script implements comprehensive improvements to boost model accuracy.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import (train_test_split, StratifiedKFold, cross_val_score,
                                   GridSearchCV, RandomizedSearchCV)
from sklearn.preprocessing import (StandardScaler, PolynomialFeatures, RobustScaler,
                                 MinMaxScaler, PowerTransformer)
from sklearn.pipeline import Pipeline
from sklearn.feature_selection import SelectKBest, f_classif, RFE, SelectFromModel
from sklearn.decomposition import PCA
from sklearn.metrics import (accuracy_score, precision_score, recall_score,
                             f1_score, roc_auc_score, confusion_matrix,
                             classification_report, make_scorer)
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import (RandomForestClassifier, GradientBoostingClassifier,
                            StackingClassifier, VotingClassifier)
from sklearn.svm import SVC
from sklearn.neighbors import KNeighborsClassifier
from sklearn.neural_network import MLPClassifier
import xgboost as xgb
import lightgbm as lgb
from catboost import CatBoostClassifier
from sklearn.gaussian_process import GaussianProcessClassifier
from sklearn.gaussian_process.kernels import RBF
import warnings
warnings.filterwarnings('ignore')

RANDOM_STATE = 42
TEST_SIZE = 0.20
CV_FOLDS = 5

# ---------------------------------------------------------------------
# 1. Load and explore data
# ---------------------------------------------------------------------

print("Loading and exploring data...")
df = pd.read_csv("data_diag_maj_sub.csv")
X = df.drop(columns=["Diagnosis"])
y = df["Diagnosis"]

print(f"Dataset shape: {df.shape}")
print(f"Features: {X.columns.tolist()}")
print(f"Target distribution:\n{y.value_counts().sort_index()}")
print(f"Missing values: {df.isnull().sum().sum()}")

# ---------------------------------------------------------------------
# 2. Feature Engineering
# ---------------------------------------------------------------------

print("\nPerforming feature engineering...")

def create_engineered_features(X):
    """Create new features from existing ones"""
    X_eng = X.copy()

    # Group features by type (based on naming pattern)
    ne_features = [col for col in X.columns if col.startswith('NE')]
    ly_features = [col for col in X.columns if col.startswith('LY')]
    mo_features = [col for col in X.columns if col.startswith('MO')]

    # Statistical features for each group
    for prefix, features in [('NE', ne_features), ('LY', ly_features), ('MO', mo_features)]:
        if len(features) > 1:
            X_eng[f'{prefix}_mean'] = X[features].mean(axis=1)
            X_eng[f'{prefix}_std'] = X[features].std(axis=1)
            X_eng[f'{prefix}_max'] = X[features].max(axis=1)
            X_eng[f'{prefix}_min'] = X[features].min(axis=1)
            X_eng[f'{prefix}_range'] = X_eng[f'{prefix}_max'] - X_eng[f'{prefix}_min']
            X_eng[f'{prefix}_cv'] = X_eng[f'{prefix}_std'] / (X_eng[f'{prefix}_mean'] + 1e-8)

    # Ratios between different measurement types
    X_eng['NE_LY_ratio'] = X_eng['NE_mean'] / (X_eng['LY_mean'] + 1e-8)
    X_eng['NE_MO_ratio'] = X_eng['NE_mean'] / (X_eng['MO_mean'] + 1e-8)
    X_eng['LY_MO_ratio'] = X_eng['LY_mean'] / (X_eng['MO_mean'] + 1e-8)

    # Coordinate-based features (X, Y, Z patterns)
    xyz_groups = {}
    for col in X.columns:
        if col.endswith('X') or col.endswith('Y') or col.endswith('Z'):
            base = col[:-1]
            if base not in xyz_groups:
                xyz_groups[base] = []
            xyz_groups[base].append(col)

    for base, coords in xyz_groups.items():
        if len(coords) == 3:  # X, Y, Z coordinates
            x_col, y_col, z_col = sorted(coords)
            X_eng[f'{base}_magnitude'] = np.sqrt(X[x_col]**2 + X[y_col]**2 + X[z_col]**2)
            X_eng[f'{base}_xy_ratio'] = X[x_col] / (X[y_col] + 1e-8)
            X_eng[f'{base}_xz_ratio'] = X[x_col] / (X[z_col] + 1e-8)
            X_eng[f'{base}_yz_ratio'] = X[y_col] / (X[z_col] + 1e-8)

    # Log transformations for skewed features
    for col in X.columns:
        if X[col].min() > 0:  # Only for positive values
            skewness = X[col].skew()
            if abs(skewness) > 1:  # Highly skewed
                X_eng[f'{col}_log'] = np.log1p(X[col])

    return X_eng

# Apply feature engineering
X_engineered = create_engineered_features(X)
print(f"Original features: {X.shape[1]}")
print(f"After feature engineering: {X_engineered.shape[1]}")

# ---------------------------------------------------------------------
# 3. Feature Selection and Preprocessing Pipeline
# ---------------------------------------------------------------------

def create_preprocessing_pipeline(feature_selection_method='selectkbest', n_features=50):
    """Create preprocessing pipeline with feature selection"""

    steps = [
        ('scaler', RobustScaler()),  # Robust to outliers
    ]

    if feature_selection_method == 'selectkbest':
        steps.append(('selector', SelectKBest(f_classif, k=min(n_features, X_engineered.shape[1]))))
    elif feature_selection_method == 'rfe':
        steps.append(('selector', RFE(RandomForestClassifier(n_estimators=50, random_state=RANDOM_STATE),
                                    n_features_to_select=min(n_features, X_engineered.shape[1]))))
    elif feature_selection_method == 'pca':
        steps.append(('pca', PCA(n_components=min(n_features, X_engineered.shape[1]), random_state=RANDOM_STATE)))

    return Pipeline(steps)

# ---------------------------------------------------------------------
# 4. Cross-Validation Setup
# ---------------------------------------------------------------------

def evaluate_model_cv(model, X, y, cv_folds=CV_FOLDS, scoring=['accuracy', 'f1_macro', 'roc_auc_ovr']):
    """Evaluate model using cross-validation"""
    skf = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=RANDOM_STATE)

    results = {}
    for score in scoring:
        if score == 'roc_auc_ovr':
            scorer = make_scorer(roc_auc_score, multi_class='ovr', needs_proba=True)
        else:
            scorer = score

        scores = cross_val_score(model, X, y, cv=skf, scoring=scorer, n_jobs=-1)
        results[score] = {
            'mean': scores.mean(),
            'std': scores.std(),
            'scores': scores
        }

    return results

# ---------------------------------------------------------------------
# 5. Hyperparameter Tuning Configurations
# ---------------------------------------------------------------------

def get_hyperparameter_grids():
    """Define hyperparameter grids for tuning"""

    param_grids = {
        'LogisticRegression': {
            'clf__C': [0.01, 0.1, 1.0, 10.0],
            'clf__penalty': ['l1', 'l2', 'elasticnet'],
            'clf__solver': ['liblinear', 'saga'],
            'clf__max_iter': [1000]
        },

        'RandomForest': {
            'n_estimators': [100, 200, 300],
            'max_depth': [5, 10, 15, None],
            'min_samples_split': [5, 10, 20],
            'min_samples_leaf': [2, 5, 10],
            'max_features': ['sqrt', 'log2', None]
        },

        'XGBoost': {
            'n_estimators': [100, 200, 300],
            'learning_rate': [0.01, 0.05, 0.1],
            'max_depth': [3, 4, 5, 6],
            'reg_alpha': [0, 0.1, 0.5],
            'reg_lambda': [0.5, 1.0, 2.0],
            'subsample': [0.8, 0.9, 1.0],
            'colsample_bytree': [0.8, 0.9, 1.0]
        },

        'SVM': {
            'clf__C': [0.01, 0.1, 1.0, 10.0],
            'clf__gamma': ['scale', 'auto', 0.001, 0.01, 0.1],
            'clf__kernel': ['rbf', 'poly']
        },

        'KNN': {
            'clf__n_neighbors': [3, 5, 7, 9, 11],
            'clf__weights': ['uniform', 'distance'],
            'clf__metric': ['euclidean', 'manhattan', 'minkowski']
        },

        'MLP': {
            'clf__hidden_layer_sizes': [(50,), (100,), (50, 50), (100, 50)],
            'clf__alpha': [0.001, 0.01, 0.1],
            'clf__learning_rate_init': [0.001, 0.01],
            'clf__max_iter': [500, 1000]
        }
    }

    return param_grids

# ---------------------------------------------------------------------
# 6. Model Definitions with Hyperparameter Tuning
# ---------------------------------------------------------------------

def create_tuned_models(X_train, y_train, quick_tune=True):
    """Create models with hyperparameter tuning"""

    print("Creating and tuning models...")
    param_grids = get_hyperparameter_grids()

    tuned_models = {}

    # Create preprocessing pipeline
    preprocessing = create_preprocessing_pipeline()

    # Logistic Regression
    print("Tuning Logistic Regression...")
    lr_pipeline = Pipeline([
        ('preprocessor', create_preprocessing_pipeline()),
        ('clf', LogisticRegression(random_state=RANDOM_STATE))
    ])

    if quick_tune:
        # Reduced grid for faster execution
        lr_grid = {
            'clf__C': [0.1, 1.0, 10.0],
            'clf__penalty': ['l2'],
            'clf__max_iter': [1000]
        }
        lr_search = GridSearchCV(lr_pipeline, lr_grid, cv=3, scoring='accuracy', n_jobs=-1)
    else:
        lr_search = GridSearchCV(lr_pipeline, param_grids['LogisticRegression'],
                               cv=CV_FOLDS, scoring='accuracy', n_jobs=-1)

    lr_search.fit(X_train, y_train)
    tuned_models['Logistic Regression'] = lr_search.best_estimator_

    # For other models, we'll use processed data
    X_processed = preprocessing.fit_transform(X_train, y_train)

    # Random Forest
    print("Tuning Random Forest...")
    if quick_tune:
        rf_grid = {
            'n_estimators': [100, 200],
            'max_depth': [10, None],
            'min_samples_split': [5, 10],
            'max_features': ['sqrt']
        }
        rf_search = RandomizedSearchCV(RandomForestClassifier(random_state=RANDOM_STATE),
                                     rf_grid, n_iter=10, cv=3, scoring='accuracy',
                                     random_state=RANDOM_STATE, n_jobs=-1)
    else:
        rf_search = RandomizedSearchCV(RandomForestClassifier(random_state=RANDOM_STATE),
                                     param_grids['RandomForest'], n_iter=20, cv=CV_FOLDS,
                                     scoring='accuracy', random_state=RANDOM_STATE, n_jobs=-1)

    rf_search.fit(X_processed, y_train)
    tuned_models['Random Forest'] = rf_search.best_estimator_

    # XGBoost
    print("Tuning XGBoost...")
    if quick_tune:
        xgb_grid = {
            'n_estimators': [100, 200],
            'learning_rate': [0.05, 0.1],
            'max_depth': [4, 6],
            'reg_alpha': [0.1],
            'reg_lambda': [1.0]
        }
        xgb_search = RandomizedSearchCV(xgb.XGBClassifier(random_state=RANDOM_STATE),
                                      xgb_grid, n_iter=8, cv=3, scoring='accuracy',
                                      random_state=RANDOM_STATE, n_jobs=-1)
    else:
        xgb_search = RandomizedSearchCV(xgb.XGBClassifier(random_state=RANDOM_STATE),
                                      param_grids['XGBoost'], n_iter=30, cv=CV_FOLDS,
                                      scoring='accuracy', random_state=RANDOM_STATE, n_jobs=-1)

    xgb_search.fit(X_processed, y_train)
    tuned_models['XGBoost'] = xgb_search.best_estimator_

    return tuned_models, preprocessing

print("Starting enhanced ML comparison...")

# ---------------------------------------------------------------------
# 7. Train-Test Split with Stratification
# ---------------------------------------------------------------------

X_train, X_test, y_train, y_test = train_test_split(
    X_engineered, y, test_size=TEST_SIZE, stratify=y, random_state=RANDOM_STATE)

print(f"Training set: {X_train.shape}")
print(f"Test set: {X_test.shape}")

# ---------------------------------------------------------------------
# 8. Model Training and Evaluation
# ---------------------------------------------------------------------

# Create tuned models (using quick_tune=True for faster execution)
tuned_models, preprocessing_pipeline = create_tuned_models(X_train, y_train, quick_tune=True)

# Add some additional models with good default parameters
print("Adding additional models...")

# Process training data
X_train_processed = preprocessing_pipeline.fit_transform(X_train, y_train)
X_test_processed = preprocessing_pipeline.transform(X_test)

# Additional models with regularization
additional_models = {
    'Gradient Boosting': GradientBoostingClassifier(
        n_estimators=200, learning_rate=0.05, max_depth=4,
        subsample=0.8, random_state=RANDOM_STATE
    ),

    'LightGBM': lgb.LGBMClassifier(
        n_estimators=200, learning_rate=0.1, num_leaves=31,
        reg_alpha=0.01, reg_lambda=0.01, random_state=RANDOM_STATE
    ),

    'CatBoost': CatBoostClassifier(
        iterations=200, learning_rate=0.1, depth=5,
        l2_leaf_reg=3.0, random_seed=RANDOM_STATE, verbose=0
    ),

    'SVM_Pipeline': Pipeline([
        ('preprocessor', create_preprocessing_pipeline()),
        ('clf', SVC(C=1.0, gamma='scale', probability=True, random_state=RANDOM_STATE))
    ]),

    'KNN_Pipeline': Pipeline([
        ('preprocessor', create_preprocessing_pipeline()),
        ('clf', KNeighborsClassifier(n_neighbors=7, weights='distance'))
    ])
}

# Combine all models
all_models = {**tuned_models, **additional_models}

# ---------------------------------------------------------------------
# 9. Cross-Validation Evaluation
# ---------------------------------------------------------------------

print("\nPerforming cross-validation evaluation...")
cv_results = {}

for name, model in all_models.items():
    print(f"Evaluating {name}...")

    if 'Pipeline' in name or hasattr(model, 'named_steps'):
        # Model already has preprocessing
        cv_result = evaluate_model_cv(model, X_train, y_train)
    else:
        # Model needs processed data
        cv_result = evaluate_model_cv(model, X_train_processed, y_train)

    cv_results[name] = cv_result

# ---------------------------------------------------------------------
# 10. Final Model Training and Test Evaluation
# ---------------------------------------------------------------------

print("\nTraining final models and evaluating on test set...")
final_results = {}

for name, model in all_models.items():
    print(f"Final training: {name}")

    if 'Pipeline' in name or hasattr(model, 'named_steps'):
        # Model has preprocessing
        model.fit(X_train, y_train)
        y_pred = model.predict(X_test)
        y_proba = model.predict_proba(X_test) if hasattr(model, 'predict_proba') else None
    else:
        # Model needs processed data
        model.fit(X_train_processed, y_train)
        y_pred = model.predict(X_test_processed)
        y_proba = model.predict_proba(X_test_processed) if hasattr(model, 'predict_proba') else None

    # Calculate metrics
    accuracy = accuracy_score(y_test, y_pred)
    precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
    recall = recall_score(y_test, y_pred, average='weighted')
    f1 = f1_score(y_test, y_pred, average='weighted')

    if y_proba is not None:
        roc_auc = roc_auc_score(y_test, y_proba, multi_class='ovr')
    else:
        roc_auc = 0.0

    final_results[name] = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'roc_auc': roc_auc,
        'cv_accuracy_mean': cv_results[name]['accuracy']['mean'],
        'cv_accuracy_std': cv_results[name]['accuracy']['std']
    }

# ---------------------------------------------------------------------
# 11. Ensemble Methods
# ---------------------------------------------------------------------

print("\nCreating ensemble methods...")

# Select top 3 models for ensemble
top_models = sorted(final_results.items(), key=lambda x: x[1]['accuracy'], reverse=True)[:3]
print(f"Top 3 models for ensemble: {[name for name, _ in top_models]}")

# Voting Classifier
voting_estimators = []
for name, _ in top_models:
    model = all_models[name]
    if 'Pipeline' in name or hasattr(model, 'named_steps'):
        voting_estimators.append((name.replace(' ', '_'), model))
    else:
        # Wrap in pipeline
        pipeline = Pipeline([
            ('preprocessor', create_preprocessing_pipeline()),
            ('clf', model)
        ])
        voting_estimators.append((name.replace(' ', '_'), pipeline))

voting_clf = VotingClassifier(estimators=voting_estimators, voting='soft')
voting_clf.fit(X_train, y_train)
y_pred_voting = voting_clf.predict(X_test)
y_proba_voting = voting_clf.predict_proba(X_test)

# Evaluate voting classifier
voting_accuracy = accuracy_score(y_test, y_pred_voting)
voting_precision = precision_score(y_test, y_pred_voting, average='weighted', zero_division=0)
voting_recall = recall_score(y_test, y_pred_voting, average='weighted')
voting_f1 = f1_score(y_test, y_pred_voting, average='weighted')
voting_roc_auc = roc_auc_score(y_test, y_proba_voting, multi_class='ovr')

final_results['Voting Ensemble'] = {
    'accuracy': voting_accuracy,
    'precision': voting_precision,
    'recall': voting_recall,
    'f1': voting_f1,
    'roc_auc': voting_roc_auc,
    'cv_accuracy_mean': 0.0,  # Not computed for ensemble
    'cv_accuracy_std': 0.0
}

# ---------------------------------------------------------------------
# 12. Results Analysis and Visualization
# ---------------------------------------------------------------------

print("\nAnalyzing results...")

# Create results DataFrame
results_df = pd.DataFrame(final_results).T
results_df = results_df.round(4)

# Sort by accuracy
results_df_sorted = results_df.sort_values('accuracy', ascending=False)

print("\n" + "="*80)
print("ENHANCED ML COMPARISON RESULTS")
print("="*80)
print(f"Dataset: {df.shape[0]} samples, {X_engineered.shape[1]} features (after engineering)")
print(f"Original features: {X.shape[1]}, Engineered features: {X_engineered.shape[1]}")
print(f"Cross-validation: {CV_FOLDS}-fold stratified")
print(f"Test set size: {len(y_test)} samples")
print("="*80)

print("\nTOP PERFORMING MODELS:")
print("-" * 50)
for i, (model_name, metrics) in enumerate(results_df_sorted.head(5).iterrows(), 1):
    print(f"{i}. {model_name}")
    print(f"   Test Accuracy: {metrics['accuracy']:.3f}")
    print(f"   CV Accuracy: {metrics['cv_accuracy_mean']:.3f} ± {metrics['cv_accuracy_std']:.3f}")
    print(f"   F1 Score: {metrics['f1']:.3f}")
    print(f"   ROC AUC: {metrics['roc_auc']:.3f}")
    print()

print("\nFULL RESULTS TABLE:")
print("-" * 50)
print(results_df_sorted.to_string())

# Save detailed results
results_df_sorted.to_csv("enhanced_model_metrics.csv", index_label="Model")
print(f"\nDetailed results saved to: enhanced_model_metrics.csv")

# Feature importance analysis for top model
print("\nFEATURE IMPORTANCE ANALYSIS:")
print("-" * 50)

best_model_name = results_df_sorted.index[0]
best_model = all_models[best_model_name]

try:
    if hasattr(best_model, 'feature_importances_'):
        # Direct feature importance
        feature_names = preprocessing_pipeline.get_feature_names_out() if hasattr(preprocessing_pipeline, 'get_feature_names_out') else [f'feature_{i}' for i in range(X_train_processed.shape[1])]
        importances = best_model.feature_importances_
    elif hasattr(best_model, 'named_steps') and hasattr(best_model.named_steps.get('clf'), 'feature_importances_'):
        # Pipeline with feature importance
        feature_names = [f'feature_{i}' for i in range(X_train_processed.shape[1])]
        importances = best_model.named_steps['clf'].feature_importances_
    else:
        importances = None

    if importances is not None:
        feature_importance_df = pd.DataFrame({
            'feature': feature_names[:len(importances)],
            'importance': importances
        }).sort_values('importance', ascending=False)

        print(f"Top 10 features for {best_model_name}:")
        print(feature_importance_df.head(10).to_string(index=False))

        # Save feature importance
        feature_importance_df.to_csv("feature_importance.csv", index=False)
        print(f"Feature importance saved to: feature_importance.csv")
    else:
        print(f"Feature importance not available for {best_model_name}")

except Exception as e:
    print(f"Could not extract feature importance: {e}")

# Model comparison visualization
print("\nCreating visualizations...")

plt.figure(figsize=(15, 10))

# Subplot 1: Accuracy comparison
plt.subplot(2, 2, 1)
models = results_df_sorted.index[:8]  # Top 8 models
accuracies = results_df_sorted['accuracy'][:8]
bars = plt.bar(range(len(models)), accuracies, color='skyblue', alpha=0.7)
plt.xlabel('Models')
plt.ylabel('Test Accuracy')
plt.title('Model Accuracy Comparison')
plt.xticks(range(len(models)), models, rotation=45, ha='right')
plt.ylim(0, 1)

# Add value labels on bars
for i, bar in enumerate(bars):
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,
             f'{height:.3f}', ha='center', va='bottom')

# Subplot 2: Cross-validation vs Test accuracy
plt.subplot(2, 2, 2)
cv_acc = results_df_sorted['cv_accuracy_mean'][:8]
test_acc = results_df_sorted['accuracy'][:8]
plt.scatter(cv_acc, test_acc, alpha=0.7, s=100)
plt.plot([0, 1], [0, 1], 'r--', alpha=0.5)
plt.xlabel('CV Accuracy')
plt.ylabel('Test Accuracy')
plt.title('Cross-Validation vs Test Accuracy')
plt.xlim(0, 1)
plt.ylim(0, 1)

# Add model names as labels
for i, model in enumerate(models):
    plt.annotate(model[:10], (cv_acc.iloc[i], test_acc.iloc[i]),
                xytext=(5, 5), textcoords='offset points', fontsize=8)

# Subplot 3: Multiple metrics comparison
plt.subplot(2, 2, 3)
metrics_to_plot = ['accuracy', 'precision', 'recall', 'f1', 'roc_auc']
top_3_models = results_df_sorted.index[:3]

x = np.arange(len(metrics_to_plot))
width = 0.25

for i, model in enumerate(top_3_models):
    values = [results_df_sorted.loc[model, metric] for metric in metrics_to_plot]
    plt.bar(x + i*width, values, width, label=model, alpha=0.7)

plt.xlabel('Metrics')
plt.ylabel('Score')
plt.title('Top 3 Models - Multiple Metrics')
plt.xticks(x + width, metrics_to_plot, rotation=45)
plt.legend()
plt.ylim(0, 1)

# Subplot 4: CV accuracy with error bars
plt.subplot(2, 2, 4)
cv_means = results_df_sorted['cv_accuracy_mean'][:8]
cv_stds = results_df_sorted['cv_accuracy_std'][:8]
plt.errorbar(range(len(models)), cv_means, yerr=cv_stds,
             fmt='o', capsize=5, capthick=2, alpha=0.7)
plt.xlabel('Models')
plt.ylabel('CV Accuracy')
plt.title('Cross-Validation Accuracy with Error Bars')
plt.xticks(range(len(models)), models, rotation=45, ha='right')
plt.ylim(0, 1)

plt.tight_layout()
plt.savefig('enhanced_model_comparison.png', dpi=300, bbox_inches='tight')
plt.show()

print(f"Visualization saved to: enhanced_model_comparison.png")

# Summary statistics
print("\nSUMMARY STATISTICS:")
print("-" * 50)
print(f"Best Model: {best_model_name}")
print(f"Best Test Accuracy: {results_df_sorted['accuracy'].iloc[0]:.3f}")
print(f"Average Accuracy: {results_df_sorted['accuracy'].mean():.3f}")
print(f"Accuracy Std: {results_df_sorted['accuracy'].std():.3f}")
print(f"Models with >80% accuracy: {(results_df_sorted['accuracy'] > 0.8).sum()}")

print("\nIMPROVEMENTS IMPLEMENTED:")
print("-" * 50)
print("✓ Feature Engineering: Created statistical, ratio, and coordinate-based features")
print("✓ Cross-Validation: 5-fold stratified cross-validation for robust evaluation")
print("✓ Hyperparameter Tuning: GridSearchCV and RandomizedSearchCV optimization")
print("✓ Advanced Preprocessing: RobustScaler, feature selection, PCA options")
print("✓ Ensemble Methods: Voting classifier with top performing models")
print("✓ Comprehensive Evaluation: Multiple metrics and statistical analysis")

print("\nENHANCED ML COMPARISON COMPLETE!")
print("="*80)
