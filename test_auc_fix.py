#!/usr/bin/env python3
"""
Test script to verify the AUC confidence interval fix
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import roc_auc_score

def calculate_auc_ci(y_true, y_scores, confidence=0.95, n_bootstrap=100):
    """
    Calculate bootstrap confidence intervals for AUC using One-vs-Rest approach
    """
    # Convert inputs to numpy arrays for consistent indexing
    y_true = np.array(y_true)
    y_scores = np.array(y_scores)
    
    # Validate input shapes
    if len(y_true.shape) != 1:
        raise ValueError(f"y_true must be 1D array, got shape {y_true.shape}")
    if len(y_scores.shape) != 2:
        raise ValueError(f"y_scores must be 2D array, got shape {y_scores.shape}")
    if y_true.shape[0] != y_scores.shape[0]:
        raise ValueError(f"Shape mismatch: y_true has {y_true.shape[0]} samples, y_scores has {y_scores.shape[0]} samples")
    
    # Get unique classes and expected number of classes
    unique_classes = np.unique(y_true)
    n_classes = len(unique_classes)
    expected_classes = np.arange(n_classes)
    
    print(f"Debug: y_true shape: {y_true.shape}, y_scores shape: {y_scores.shape}")
    print(f"Debug: Unique classes in y_true: {unique_classes}")
    print(f"Debug: Expected classes: {expected_classes}")
    print(f"Debug: Number of probability columns: {y_scores.shape[1]}")
    
    # Check if number of classes matches number of probability columns
    if y_scores.shape[1] != n_classes:
        print(f"Warning: Number of classes ({n_classes}) doesn't match probability columns ({y_scores.shape[1]})")
        # Adjust if necessary
        if y_scores.shape[1] > n_classes:
            y_scores = y_scores[:, :n_classes]
            print(f"Truncated y_scores to {y_scores.shape[1]} columns")
    
    def auc_statistic(y_true_boot, y_scores_boot):
        """Calculate AUC using One-vs-Rest multi-class approach with robust error handling"""
        try:
            # Check if all classes are present in bootstrap sample
            unique_boot_classes = np.unique(y_true_boot)
            
            # If we have fewer than 2 classes, can't calculate AUC
            if len(unique_boot_classes) < 2:
                return np.nan
            
            # If some classes are missing, we need to handle this carefully
            if len(unique_boot_classes) < n_classes:
                # Use only the classes present in the bootstrap sample
                # Create a mapping from original classes to present classes
                class_mask = np.isin(expected_classes, unique_boot_classes)
                if np.sum(class_mask) < 2:
                    return np.nan
                
                # Select only the probability columns for present classes
                y_scores_filtered = y_scores_boot[:, class_mask]
                
                # Renormalize probabilities to sum to 1
                row_sums = y_scores_filtered.sum(axis=1, keepdims=True)
                row_sums[row_sums == 0] = 1  # Avoid division by zero
                y_scores_filtered = y_scores_filtered / row_sums
                
                return roc_auc_score(y_true_boot, y_scores_filtered, 
                                   multi_class='ovr', average='macro')
            else:
                # All classes present, proceed normally
                return roc_auc_score(y_true_boot, y_scores_boot, 
                                   multi_class='ovr', average='macro')
                
        except (ValueError, IndexError) as e:
            print(f"Debug: AUC calculation failed: {str(e)}")
            return np.nan
    
    # Initialize bootstrap results
    n_samples = len(y_true)
    bootstrap_aucs = []
    
    # Set random seed for reproducibility
    np.random.seed(42)
    
    # Generate bootstrap samples
    successful_iterations = 0
    for iteration in range(n_bootstrap):
        # Bootstrap sample indices with replacement
        indices = np.random.choice(n_samples, size=n_samples, replace=True)
        y_true_boot = y_true[indices]
        y_scores_boot = y_scores[indices]
        
        # Calculate AUC for bootstrap sample
        auc_boot = auc_statistic(y_true_boot, y_scores_boot)
        if not np.isnan(auc_boot):
            bootstrap_aucs.append(auc_boot)
            successful_iterations += 1
    
    print(f"Debug: Successful bootstrap iterations: {successful_iterations}/{n_bootstrap}")
    
    if len(bootstrap_aucs) == 0:
        print("Warning: No successful bootstrap iterations for AUC calculation")
        return np.nan, np.nan, []
    
    # Calculate confidence interval using percentile method
    alpha = 1 - confidence
    lower_percentile = (alpha/2) * 100
    upper_percentile = (1 - alpha/2) * 100
    
    ci_lower = np.percentile(bootstrap_aucs, lower_percentile)
    ci_upper = np.percentile(bootstrap_aucs, upper_percentile)
    
    return ci_lower, ci_upper, bootstrap_aucs

def test_auc_fix():
    """Test the AUC confidence interval fix"""
    print("Testing AUC confidence interval fix...")
    
    # Create test data similar to the notebook
    np.random.seed(42)
    n_samples = 200
    n_features = 10
    
    # Create features
    X = np.random.randn(n_samples, n_features)
    
    # Create 3-class target (similar to major categories)
    y = np.concatenate([
        np.zeros(50),      # Class 0
        np.ones(100),      # Class 1
        np.full(50, 2)     # Class 2
    ])
    
    # Add class-specific patterns
    for i in range(n_samples):
        if y[i] == 1:
            X[i, 0] += 2.0
        elif y[i] == 2:
            X[i, 1] += 1.5
    
    print(f"Test data: {X.shape}, classes: {np.unique(y)}")
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    print(f"Train: {X_train.shape}, Test: {X_test.shape}")
    print(f"Test classes: {np.unique(y_test)}")
    
    # Train a simple model
    model = RandomForestClassifier(n_estimators=50, random_state=42)
    model.fit(X_train, y_train)
    
    # Get predictions
    y_pred_proba = model.predict_proba(X_test)
    
    print(f"Prediction probabilities shape: {y_pred_proba.shape}")
    
    # Test basic AUC calculation
    try:
        auc = roc_auc_score(y_test, y_pred_proba, multi_class='ovr', average='macro')
        print(f"Basic AUC: {auc:.4f}")
    except Exception as e:
        print(f"Basic AUC failed: {e}")
        return False
    
    # Test bootstrap confidence intervals
    try:
        ci_lower, ci_upper, bootstrap_aucs = calculate_auc_ci(y_test, y_pred_proba, n_bootstrap=50)
        print(f"Bootstrap CI: [{ci_lower:.4f}, {ci_upper:.4f}]")
        print(f"Bootstrap AUCs: {len(bootstrap_aucs)} successful iterations")
        
        if len(bootstrap_aucs) > 0:
            print(f"Bootstrap AUC range: [{min(bootstrap_aucs):.4f}, {max(bootstrap_aucs):.4f}]")
            return True
        else:
            print("No successful bootstrap iterations")
            return False
            
    except Exception as e:
        print(f"Bootstrap CI failed: {e}")
        return False

if __name__ == "__main__":
    success = test_auc_fix()
    if success:
        print("\n✅ AUC confidence interval fix appears to be working!")
    else:
        print("\n❌ AUC confidence interval fix needs more work.")
