import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

def main():
    print("=== Starting Comprehensive ML Analysis ===")
    
    # Load datasets
    print("\n1. Loading datasets...")
    df_major = pd.read_csv('data_diag.csv')
    df_subgroup = pd.read_csv('data_diag_maj_sub.csv')
    
    print(f"Major categories dataset shape: {df_major.shape}")
    print(f"Subgroup dataset shape: {df_subgroup.shape}")
    
    # Data preparation
    X_major = df_major.drop('Diagnosis', axis=1)
    y_major = df_major['Diagnosis']
    X_subgroup = df_subgroup.drop('Diagnosis', axis=1)
    y_subgroup = df_subgroup['Diagnosis']
    
    # Train-test split
    print("\n2. Splitting data...")
    X_train_maj, X_test_maj, y_train_maj, y_test_maj = train_test_split(
        X_major, y_major, test_size=0.2, random_state=42, stratify=y_major
    )
    X_train_sub, X_test_sub, y_train_sub, y_test_sub = train_test_split(
        X_subgroup, y_subgroup, test_size=0.2, random_state=42, stratify=y_subgroup
    )
    
    print(f"Training set size (major): {X_train_maj.shape[0]}")
    print(f"Test set size (major): {X_test_maj.shape[0]}")
    
    # Feature Engineering
    print("\n3. Feature Engineering...")
    
    def enhanced_feature_engineering(X):
        X_eng = X.copy()
        
        # Statistical features for each cell type
        for cell_type in ['NE', 'LY', 'MO']:
            features = [col for col in X.columns if col.startswith(cell_type)]
            if features:
                X_eng[f'{cell_type}_mean'] = X[features].mean(axis=1)
                X_eng[f'{cell_type}_std'] = X[features].std(axis=1)
                X_eng[f'{cell_type}_max'] = X[features].max(axis=1)
                X_eng[f'{cell_type}_min'] = X[features].min(axis=1)
                X_eng[f'{cell_type}_range'] = X[features].max(axis=1) - X[features].min(axis=1)
                X_eng[f'{cell_type}_cv'] = X[features].std(axis=1) / (X[features].mean(axis=1) + 1e-8)
        
        # Relational features
        if 'NE_mean' in X_eng.columns and 'LY_mean' in X_eng.columns and 'MO_mean' in X_eng.columns:
            X_eng['NE_LY_ratio'] = X_eng['NE_mean'] / (X_eng['LY_mean'] + 1e-8)
            X_eng['NE_MO_ratio'] = X_eng['NE_mean'] / (X_eng['MO_mean'] + 1e-8)
            X_eng['LY_MO_ratio'] = X_eng['LY_mean'] / (X_eng['MO_mean'] + 1e-8)
        
        # Geometric features (magnitude)
        for cell_type in ['NE', 'LY', 'MO']:
            x_col = f'{cell_type}X'
            y_col = f'{cell_type}Y' 
            z_col = f'{cell_type}Z'
            if all(col in X.columns for col in [x_col, y_col, z_col]):
                X_eng[f'{cell_type}_magnitude'] = np.sqrt(X[x_col]**2 + X[y_col]**2 + X[z_col]**2)
        
        return X_eng
    
    # Apply feature engineering
    X_train_maj_eng = enhanced_feature_engineering(X_train_maj)
    X_test_maj_eng = enhanced_feature_engineering(X_test_maj)
    X_train_sub_eng = enhanced_feature_engineering(X_train_sub)
    X_test_sub_eng = enhanced_feature_engineering(X_test_sub)
    
    print(f"Original features: {X_train_maj.shape[1]}")
    print(f"Enhanced features: {X_train_maj_eng.shape[1]}")
    print(f"New features added: {X_train_maj_eng.shape[1] - X_train_maj.shape[1]}")
    
    # Feature scaling
    print("\n4. Feature scaling...")
    scaler_maj = StandardScaler()
    X_train_maj_scaled = scaler_maj.fit_transform(X_train_maj_eng)
    X_test_maj_scaled = scaler_maj.transform(X_test_maj_eng)
    
    scaler_sub = StandardScaler()
    X_train_sub_scaled = scaler_sub.fit_transform(X_train_sub_eng)
    X_test_sub_scaled = scaler_sub.transform(X_test_sub_eng)
    
    # Model training
    print("\n5. Training models...")
    
    models_config = {
        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1),
        'Gradient Boosting': GradientBoostingClassifier(n_estimators=100, random_state=42),
        'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
        'SVM': SVC(random_state=42, probability=True)
    }
    
    # Train models for major categories
    models_major = {}
    print("\nTraining models for major categories:")
    for name, model in models_config.items():
        print(f"  Training {name}...")
        if name in ['Logistic Regression', 'SVM']:
            models_major[name] = model.fit(X_train_maj_scaled, y_train_maj)
        else:
            models_major[name] = model.fit(X_train_maj_eng, y_train_maj)
    
    # Train models for subgroups
    models_subgroup = {}
    print("\nTraining models for subgroups:")
    for name, model in models_config.items():
        print(f"  Training {name}...")
        if name in ['Logistic Regression', 'SVM']:
            models_subgroup[name] = model.fit(X_train_sub_scaled, y_train_sub)
        else:
            models_subgroup[name] = model.fit(X_train_sub_eng, y_train_sub)
    
    # Model evaluation
    print("\n6. Evaluating models...")
    
    def evaluate_models(models, X_test_eng, X_test_scaled, y_test, dataset_name):
        results = []
        print(f"\nEvaluating {dataset_name}:")
        
        for name, model in models.items():
            # Select appropriate test data
            X_test_model = X_test_scaled if name in ['Logistic Regression', 'SVM'] else X_test_eng
            
            # Predictions
            y_pred = model.predict(X_test_model)
            y_pred_proba = model.predict_proba(X_test_model)
            
            # Metrics
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average='macro', zero_division=0)
            recall = recall_score(y_test, y_pred, average='macro', zero_division=0)
            f1 = f1_score(y_test, y_pred, average='macro', zero_division=0)
            
            try:
                auc = roc_auc_score(y_test, y_pred_proba, multi_class='ovr', average='macro')
            except Exception as e:
                auc = np.nan
                print(f"    AUC calculation failed for {name}: {e}")
            
            results.append({
                'Model': name,
                'Accuracy': accuracy,
                'Precision': precision,
                'Recall': recall,
                'F1-Score': f1,
                'AUC': auc
            })
            
            print(f"  {name}:")
            print(f"    Accuracy: {accuracy:.4f}")
            print(f"    Precision: {precision:.4f}")
            print(f"    Recall: {recall:.4f}")
            print(f"    F1-Score: {f1:.4f}")
            if not np.isnan(auc):
                print(f"    AUC: {auc:.4f}")
        
        return pd.DataFrame(results)
    
    # Evaluate both datasets
    results_major = evaluate_models(models_major, X_test_maj_eng, X_test_maj_scaled, y_test_maj, "Major Categories")
    results_subgroup = evaluate_models(models_subgroup, X_test_sub_eng, X_test_sub_scaled, y_test_sub, "Subgroup Classifications")
    
    # Save results
    print("\n7. Saving results...")
    results_major.to_csv('major_categories_results.csv', index=False)
    results_subgroup.to_csv('subgroup_results.csv', index=False)
    
    print("\n=== Analysis Complete ===")
    print("\nBest performing models:")
    
    # Find best models
    best_major = results_major.loc[results_major['F1-Score'].idxmax()]
    best_subgroup = results_subgroup.loc[results_subgroup['F1-Score'].idxmax()]
    
    print(f"\nMajor Categories - Best: {best_major['Model']} (F1: {best_major['F1-Score']:.4f})")
    print(f"Subgroup Classifications - Best: {best_subgroup['Model']} (F1: {best_subgroup['F1-Score']:.4f})")
    
    # Display complete results
    print("\n=== Complete Results Summary ===")
    print("\nMajor Categories Results:")
    print(results_major.round(4))
    print("\nSubgroup Classifications Results:")
    print(results_subgroup.round(4))
    
    return results_major, results_subgroup

if __name__ == "__main__":
    results_major, results_subgroup = main()
