from Bio import Entrez
Entrez.email = "<EMAIL>"
Entrez.api_key = "d1dbd398bd51f753ba4f705376a639de6708"


search_term = "(adaptive design OR Bayesian design) AND (clinical trial OR phase I OR phase II) AND (pharmacology OR therapeutics)"
handle = Entrez.esearch(db="pubmed", term=search_term, retmax="100") # Get up to 100 PMIDs
record = Entrez.read(handle)
handle.close()
pmids = record["IdList"]


handle = Entrez.efetch(db="pubmed", id=pmids, rettype="medline", retmode="xml")
records = Entrez.read(handle)
handle.close()


for pubmed_article in records['PubmedArticle']:
    title = pubmed_article['MedlineCitation']['Article']['ArticleTitle']
    abstract_list = pubmed_article['MedlineCitation']['Article'].get('Abstract', {}).get('AbstractText', [])
    abstract = " ".join(abstract_list)
    # Extract other fields like authors, journal, publication date, keywords (MeSH terms) as needed
    print(f"Title: {title}")
    if abstract:
        print(f"Abstract: {abstract[:200]}...") # Print first 200 characters of abstract
    print("-" * 20)