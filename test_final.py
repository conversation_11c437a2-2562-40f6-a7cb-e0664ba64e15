#!/usr/bin/env python3
"""
Final test to verify ML model shapes for 3-class acute leukemia diagnosis
"""

print("Starting shape verification test...")

try:
    import pandas as pd
    import numpy as np
    from sklearn.model_selection import train_test_split
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.linear_model import LogisticRegression
    from sklearn.svm import SVC
    print("✓ All imports successful")
    
    # Load data
    df = pd.read_csv('data_diag.csv')
    X = df.drop('Diagnosis', axis=1)
    y = df['Diagnosis']
    print(f"✓ Data loaded: {df.shape}")
    
    print(f"✓ Classes found: {sorted(y.unique())}")
    print(f"✓ Class distribution: {dict(y.value_counts().sort_index())}")
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
    print(f"✓ Data split: Train={X_train.shape}, Test={X_test.shape}")
    
    # Test multiple models
    models = {
        'RandomForest': RandomForestClassifier(n_estimators=50, random_state=42),
        'LogisticRegression': LogisticRegression(random_state=42),
        'SVM': SVC(probability=True, random_state=42)
    }
    
    results = {}
    
    for name, model in models.items():
        print(f"\n--- Testing {name} ---")
        
        # Train
        model.fit(X_train, y_train)
        print(f"✓ {name} trained")
        
        # Get probabilities
        y_pred_proba = model.predict_proba(X_test)
        
        # Analyze shapes
        n_test_classes = len(np.unique(y_test))
        n_prob_cols = y_pred_proba.shape[1]
        
        print(f"Test classes: {n_test_classes}")
        print(f"Probability columns: {n_prob_cols}")
        print(f"Shape match: {n_test_classes == n_prob_cols}")
        
        results[name] = {
            'test_classes': n_test_classes,
            'prob_cols': n_prob_cols,
            'match': n_test_classes == n_prob_cols
        }
        
        if n_test_classes == n_prob_cols:
            print(f"✓ {name}: CORRECT - No shape mismatch")
        else:
            print(f"✗ {name}: ISSUE - Shape mismatch detected")
    
    print("\n" + "="*50)
    print("FINAL RESULTS:")
    print("="*50)
    
    all_correct = all(r['match'] for r in results.values())
    
    if all_correct:
        print("🎉 SUCCESS: All models produce correct shapes!")
        print("   - All models output exactly 3 probability columns")
        print("   - Shape mismatch handling should NOT be triggered")
        print("   - Current implementation is working correctly")
    else:
        print("⚠️  ISSUE: Some models have shape mismatches")
        print("   - Shape adjustment code would be triggered")
        print("   - Investigation needed for affected models")
    
    print(f"\nSummary:")
    for name, result in results.items():
        status = "✓" if result['match'] else "✗"
        print(f"  {status} {name}: {result['test_classes']} classes → {result['prob_cols']} columns")

except Exception as e:
    print(f"❌ Error occurred: {str(e)}")
    import traceback
    traceback.print_exc()

print("\nTest completed.")
