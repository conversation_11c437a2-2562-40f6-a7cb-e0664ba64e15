#!/usr/bin/env python3
"""
Execute the Technical Report Analysis and generate outputs
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Machine Learning libraries
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.metrics import classification_report, confusion_matrix

# Advanced ML libraries
try:
    import xgboost as xgb
    print("XGBoost imported successfully")
except ImportError:
    print("XGBoost not available, will use alternative models")
    xgb = None

try:
    from catboost import CatBoostClassifier
    print("CatBoost imported successfully")
except ImportError:
    print("CatBoost not available, will use alternative models")
    CatBoostClassifier = None

# SHAP for interpretability
try:
    import shap
    print("SHAP imported successfully")
except ImportError:
    print("SHAP not available, will skip interpretability analysis")
    shap = None

# Statistical libraries
from scipy import stats
from scipy.stats import ttest_rel

# Set random seed for reproducibility
np.random.seed(42)

# Configure plotting
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (10, 6)
plt.rcParams['font.size'] = 12

print("All libraries imported successfully!")
print(f"NumPy version: {np.__version__}")
print(f"Pandas version: {pd.__version__}")

def create_sample_data():
    """
    Create sample datasets matching the structure described in the technical report
    """
    np.random.seed(42)
    
    # Define feature names
    feature_names = [
        'NEX', 'NEY', 'NEZ', 'NEWX', 'NEWY', 'NEWZ',  # Neutrophil parameters
        'LYX', 'LYY', 'LYZ', 'LYWX', 'LYWY', 'LYWZ',  # Lymphocyte parameters
        'MOX', 'MOY', 'MOZ', 'MOWX', 'MOWY', 'MOWZ'   # Monocyte parameters
    ]
    
    # Create synthetic data with realistic patterns
    n_samples = 791
    n_features = 18
    
    # Generate base features
    X = np.random.randn(n_samples, n_features)
    
    # Add realistic patterns for different classes
    y_major = np.concatenate([
        np.zeros(100),      # Normal
        np.ones(555),       # Major leukemia
        np.full(136, 2)     # Secondary leukemia
    ])
    
    # For subgroup classification
    y_subgroup = np.concatenate([
        np.zeros(100),      # Normal
        np.ones(316),       # Subgroup 1
        np.full(239, 2),    # Subgroup 2
        np.full(136, 3)     # Subgroup 3
    ])
    
    # Add class-specific patterns to make classification meaningful
    for i in range(n_samples):
        if y_major[i] == 1:  # Major leukemia
            X[i, 1] += 2.0  # NEY increased
            X[i, 4] += 1.5  # NEWY increased
        elif y_major[i] == 2:  # Secondary leukemia
            X[i, 1] += 1.0  # NEY moderately increased
            X[i, 7] -= 1.0  # LYY decreased
    
    # Create DataFrames
    df_major = pd.DataFrame(X, columns=feature_names)
    df_major['Diagnosis'] = y_major.astype(int)
    
    df_subgroup = pd.DataFrame(X, columns=feature_names)
    df_subgroup['Diagnosis'] = y_subgroup.astype(int)
    
    return df_major, df_subgroup, feature_names

def enhanced_feature_engineering(X):
    """
    Complete feature engineering pipeline
    """
    X_eng = X.copy()
    
    # Statistical features for each cell type
    for cell_type in ['NE', 'LY', 'MO']:
        features = [col for col in X.columns if col.startswith(cell_type)]
        
        X_eng[f'{cell_type}_mean'] = X[features].mean(axis=1)
        X_eng[f'{cell_type}_std'] = X[features].std(axis=1)
        X_eng[f'{cell_type}_max'] = X[features].max(axis=1)
        X_eng[f'{cell_type}_min'] = X[features].min(axis=1)
        X_eng[f'{cell_type}_range'] = X[features].max(axis=1) - X[features].min(axis=1)
        X_eng[f'{cell_type}_cv'] = X[features].std(axis=1) / (X[features].mean(axis=1) + 1e-8)
    
    # Relational features
    X_eng['NE_LY_ratio'] = X_eng['NE_mean'] / (X_eng['LY_mean'] + 1e-8)
    X_eng['NE_MO_ratio'] = X_eng['NE_mean'] / (X_eng['MO_mean'] + 1e-8)
    X_eng['LY_MO_ratio'] = X_eng['LY_mean'] / (X_eng['MO_mean'] + 1e-8)
    
    # Geometric features
    for cell_type in ['NE', 'LY', 'MO']:
        x_col = f'{cell_type}X'
        y_col = f'{cell_type}Y'
        z_col = f'{cell_type}Z'
        
        magnitude = np.sqrt(X[x_col]**2 + X[y_col]**2 + X[z_col]**2)
        X_eng[f'{cell_type}_magnitude'] = magnitude
    
    return X_eng

def main():
    print("="*80)
    print("COMPREHENSIVE MACHINE LEARNING ANALYSIS")
    print("Acute Leukemia Diagnosis using Cell Population Data")
    print("="*80)
    
    # Create datasets
    print("\n1. Creating sample datasets...")
    df_major, df_subgroup, feature_names = create_sample_data()
    
    print(f"Dataset 1 (Major Categories): {df_major.shape}")
    print(f"Dataset 2 (Subgroup Classifications): {df_subgroup.shape}")
    
    # Data preprocessing
    print("\n2. Data preprocessing...")
    X_major = df_major.drop('Diagnosis', axis=1)
    y_major = df_major['Diagnosis']
    X_subgroup = df_subgroup.drop('Diagnosis', axis=1)
    y_subgroup = df_subgroup['Diagnosis']
    
    # Train-test split
    X_train_maj, X_test_maj, y_train_maj, y_test_maj = train_test_split(
        X_major, y_major, test_size=0.2, random_state=42, stratify=y_major
    )
    
    X_train_sub, X_test_sub, y_train_sub, y_test_sub = train_test_split(
        X_subgroup, y_subgroup, test_size=0.2, random_state=42, stratify=y_subgroup
    )
    
    print(f"Training set size: {X_train_maj.shape[0]}")
    print(f"Test set size: {X_test_maj.shape[0]}")
    
    # Feature engineering
    print("\n3. Feature engineering...")
    X_train_maj_eng = enhanced_feature_engineering(X_train_maj)
    X_test_maj_eng = enhanced_feature_engineering(X_test_maj)
    X_train_sub_eng = enhanced_feature_engineering(X_train_sub)
    X_test_sub_eng = enhanced_feature_engineering(X_test_sub)
    
    print(f"Original features: {X_train_maj.shape[1]}")
    print(f"Engineered features: {X_train_maj_eng.shape[1]}")
    print(f"Feature expansion: {X_train_maj_eng.shape[1] - X_train_maj.shape[1]} new features")
    
    # Feature scaling
    scaler_maj = StandardScaler()
    X_train_maj_scaled = scaler_maj.fit_transform(X_train_maj_eng)
    X_test_maj_scaled = scaler_maj.transform(X_test_maj_eng)
    
    scaler_sub = StandardScaler()
    X_train_sub_scaled = scaler_sub.fit_transform(X_train_sub_eng)
    X_test_sub_scaled = scaler_sub.transform(X_test_sub_eng)
    
    print("Feature scaling completed!")
    
    return {
        'datasets': {
            'major': (X_train_maj_eng, X_test_maj_eng, X_train_maj_scaled, X_test_maj_scaled, y_train_maj, y_test_maj),
            'subgroup': (X_train_sub_eng, X_test_sub_eng, X_train_sub_scaled, X_test_sub_scaled, y_train_sub, y_test_sub)
        },
        'feature_names': X_train_maj_eng.columns.tolist(),
        'original_features': feature_names
    }

if __name__ == "__main__":
    results = main()
    print("\nAnalysis setup completed successfully!")
    print("Ready for model training and evaluation...")
