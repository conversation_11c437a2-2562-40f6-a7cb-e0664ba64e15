import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, roc_auc_score

# Load data
print('Loading datasets...')
df1 = pd.read_csv('data_diag.csv')
df2 = pd.read_csv('data_diag_maj_sub.csv')

print(f'Dataset 1 shape: {df1.shape}')
print(f'Dataset 2 shape: {df2.shape}')

# Quick analysis for dataset 1
X1 = df1.drop('diagnosis', axis=1)
y1 = df1['diagnosis']
X1_train, X1_test, y1_train, y1_test = train_test_split(X1, y1, test_size=0.2, random_state=42, stratify=y1)

scaler1 = StandardScaler()
X1_train_scaled = scaler1.fit_transform(X1_train)
X1_test_scaled = scaler1.transform(X1_test)

rf1 = RandomForestClassifier(n_estimators=100, random_state=42)
rf1.fit(X1_train_scaled, y1_train)
y1_pred = rf1.predict(X1_test_scaled)
y1_pred_proba = rf1.predict_proba(X1_test_scaled)

acc1 = accuracy_score(y1_test, y1_pred)
auc1 = roc_auc_score(y1_test, y1_pred_proba, multi_class='ovr')

print(f'\nDataset 1 (Major Categories) Results:')
print(f'Accuracy: {acc1:.4f}')
print(f'AUC: {auc1:.4f}')
print(f'Class distribution: {dict(y1.value_counts().sort_index())}')

# Quick analysis for dataset 2
X2 = df2.drop('diagnosis', axis=1)
y2 = df2['diagnosis']
X2_train, X2_test, y2_train, y2_test = train_test_split(X2, y2, test_size=0.2, random_state=42, stratify=y2)

scaler2 = StandardScaler()
X2_train_scaled = scaler2.fit_transform(X2_train)
X2_test_scaled = scaler2.transform(X2_test)

rf2 = RandomForestClassifier(n_estimators=100, random_state=42)
rf2.fit(X2_train_scaled, y2_train)
y2_pred = rf2.predict(X2_test_scaled)
y2_pred_proba = rf2.predict_proba(X2_test_scaled)

acc2 = accuracy_score(y2_test, y2_pred)
auc2 = roc_auc_score(y2_test, y2_pred_proba, multi_class='ovr')

print(f'\nDataset 2 (Subgroup Categories) Results:')
print(f'Accuracy: {acc2:.4f}')
print(f'AUC: {auc2:.4f}')
print(f'Class distribution: {dict(y2.value_counts().sort_index())}')

print('\n=== ANALYSIS COMPLETE ===')
print('Current baseline performance verified.')
